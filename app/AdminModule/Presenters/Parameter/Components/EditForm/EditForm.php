<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\Parameter\Components\EditForm;

use App\Model\Orm\Mutation\MutationRepository;
use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\Parameter\ParameterModel;
use App\Model\Orm\Parameter\ParameterRepository;
use App\Model\Orm\String\StringRepository;
use App\Model\Orm\User\User;
use App\Model\Translator;
use App\Model\TranslatorDB;
use Nette\Application\AbortException;
use Nette\Application\UI;
use Nette\Utils\ArrayHash;
use Nextras\Dbal\Drivers\Exception\ForeignKeyConstraintViolationException;
use Throwable;
use Tracy\Debugger;
use Tracy\ILogger;

final class EditForm extends UI\Control
{

	public function __construct(
		private readonly Parameter $parameter,
		private readonly ParameterRepository $parameterRepository,
		private readonly MutationRepository $mutationRepository,
		private readonly User $userEntity,
		private readonly Translator $translator,
		private readonly TranslatorDB $translatorDB,
		private readonly ParameterModel $parameterModel,
		private readonly StringRepository $stringRepository,
	)
	{
	}

	public function render(): void
	{
		$this->template->setTranslator($this->translator);
		$this->prepareTranslates();

		$this->template->parameter = $this->parameter;
		$this->template->render(__DIR__ . '/editForm.latte');
	}

	protected function createComponentForm(): UI\Form
	{
		$form = new UI\Form();
		$form->setTranslator($this->translator);

		$form->addHidden('id', $this->parameter->id);
		$form->addText('name', 'label_internalName');

		$form->addText('uid', 'uid');
		$form->addText('extId', 'extId')->setNullable();
		$form->addCheckbox('isProtected', 'isProtected');
		$form->addText('type', 'type')->setDisabled();
		$form->addCheckbox('isInFilter', 'isInFilter');
		$form->addCheckbox('paramSearch', 'use_param_search');
		$form->addCheckbox('paramPrice', 'use_param_price');
		$form->addCheckbox('extra', 'use_extra');
		$form->addCheckbox('image', 'use_image');

		if ($this->userEntity->role !== User::ROLE_DEVELOPER) {
			$form['uid']->setDisabled();
			$form['extId']->setDisabled();
			$form['isProtected']->setDisabled();
		}

		$form->setDefaults($this->parameter->getFormData($form));
		$form->addSubmit('save', 'Save');

		$form->onSuccess[] = [$this, 'editFormSucceeded'];
		return $form;
	}



	public function editFormSucceeded(UI\Form $form, ArrayHash $values): void
	{
		$valuesAll = $form->getHttpData();

		if ($id = (int) $values['id']) {

			$parameter = $this->parameterRepository->getById($id);
			if (!isset($values->uid)) {
				$values->uid = $parameter->uid;
			} else {
				$values->uid = trim($values->uid);
			}

			try {
				$this->parameterModel->save($parameter, $values, $valuesAll);

				$this->handleParamsTranslations($valuesAll);
				$this->presenter->flashMessage('OK', 'ok');
				$form->setValues($parameter->getFormData($form));
				$this->redirect('this', ['id' => $id]);

			} catch (AbortException $e) {
				throw $e;
			} catch (Throwable $e) {
				Debugger::log($e, ILogger::ERROR);
				bd($e);
				$this->presenter->flashMessage('msg_operation_failed', 'error');
				$this->redirect('this', ['id' => $id]);
			}
		}
	}

	private function prepareTranslates(): void
	{
		// preklady
		$trParam = [];
		$trValues = [];
		$mutations = $this->mutationRepository->findAll();

		foreach ($mutations as $mutation) {
			$this->translatorDB->reInit($mutation);

			if ($this->parameter->hasTranslatedValues) {
				foreach ($this->parameter->options as $o) {
					$trValues[$o->id][$mutation->langCode]['value'] = $o->value;
					$trValues[$o->id][$mutation->langCode]['alias'] = $o->alias;
					$trValues[$o->id][$mutation->langCode]['filter'] = $o->filterValue;
				}
			}

			$trParam[$mutation->langCode]['name'] = $this->parameter->title;
			$trParam[$mutation->langCode]['tooltip'] = $this->parameter->description;
			$trParam[$mutation->langCode]['unit'] = $this->parameter->unit;
			$trParam[$mutation->langCode]['filterPrefix'] = $this->parameter->filterPrefix;
		}

		$csMutation = $this->mutationRepository->getBy(['langCode' => 'cs']);
		$this->translatorDB->reInit($csMutation);
		$this->template->mutations = $mutations;
		$this->template->trValues = $trValues;
		$this->template->trParam = $trParam;

		$usedValueIds = $this->parameterRepository->findProductParameterValueIds($this->parameter)->fetchPairs(null, 'parameterValueId');
		$usedValueIds = array_unique($usedValueIds);
		$this->template->usedValueIds = $usedValueIds;
	}

	public function handleDelete(): void
	{
		$parameterToDelete = $this->parameter;

		if ($parameterToDelete->isProtected) {
			$this->presenter->flashMessage('parameter_msg_error_delete_uid', 'error');
			$this->presenter->redirect('this');
		}

		try {
			$this->parameterModel->remove($parameterToDelete);
			$this->presenter->flashMessage('OK', 'ok');
		} catch (ForeignKeyConstraintViolationException $e) {
			$this->presenter->flashMessage('parameters_msg_error_remove_fk_constraint', 'error');
			$this->presenter->redirect('default');
		} catch (Throwable $e) {
			Debugger::log($e, ILogger::ERROR);
			$this->presenter->flashMessage('msg_operation_failed', 'error');
			$this->presenter->redirect('this');
		}

		$this->presenter->redirect('default');
	}



	private function handleParamsTranslations(array $data): void
	{
		$paramId = $data['id'];

		// param name
		$keys = ['pname', 'pname_tooltip', 'pname_unit', 'pname_filter_prefix'];
		foreach ($keys as $pKey) {
			if (isset($data[$pKey])) {
				foreach ($data[$pKey] as $lg => $name) {
					$key = $pKey . '_' . $paramId;
					$edit = [
						'value' => $name,
						'name' => $key,
						'lg' => $lg,

					];
					$this->stringRepository->replace($edit);
				}
			}
		}

		// param values
		$keys = ['pvalue', 'pvalue_short', 'pvalue_alias', 'pvalue_filter'];
		foreach ($keys as $pKey) {
			if (isset($data[$pKey])) {
				foreach ($data[$pKey] as $lg => $values) {
					foreach ($values as $valueId => $value) {
						$key = $pKey . '_' . $valueId;
						$edit = [
							'value' => $value,
							'name' => $key,
							'lg' => $lg,

						];
						$this->stringRepository->replace($edit);
					}
				}
			}
		}
	}

}
