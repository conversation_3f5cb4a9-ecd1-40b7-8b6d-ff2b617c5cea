{* <form n:if="$pages->search !== null" action='{link $pages->search, suggest=>1, search=>null, filter=>null}' id="form-search" class="f-search" data-controller="suggest" data-suggest='{link $pages->search, suggest=>1, search=>null, filter=>null}'> *}
<form id="form-search" class="f-search">
	<p class="f-search__wrap u-mb-0">
		<span class="inp-fix inp-icon">
			<label for="search" class="inp-label u-vhide">{_search_label}</label>
			<input type="text" id="search" name="search" class="f-search__inp inp-text" value="" placeholder="{_search_placeholder}" autocomplete="off" data-suggest-target="input">
			<span class="inp-icon__icon">
				{('search')|icon}
				<span class="f-search__loader"></span>
			</span>
		</span>
	</p>
	<p class="f-search__btn u-mb-0">
		<button type="submit" class="btn btn--icon" data-suggest-target="button">
			<span class="btn__text">
				{('search')|icon, 'btn__icon'}
				<span class="u-vhide">{_btn_search}</span>
			</span>
		</button>
	</p>
	<div class="b-suggest" data-suggest-target="wrap"> {* is-visible *}
		<div class="b-suggest__group">
			<h2 class="h4">
				{_search_tab_trees}
			</h2>
			<ul class="b-suggest__list u-mb-md">
				<li class="b-suggest__item">
					<a href="#" class="b-suggest__link">
						suggest link
					</a>
				</li>
				<li class="b-suggest__item">
					<a href="#" class="b-suggest__link">
						suggest link
					</a>
				</li>
				<li class="b-suggest__item">
					<a href="#" class="b-suggest__link">
						suggest link
					</a>
				</li>
				<li class="b-suggest__item">
					<a href="#" class="b-suggest__link">
						suggest link
					</a>
				</li>
				<li class="b-suggest__item">
					<a href="#" class="b-suggest__link">
						suggest link
					</a>
				</li>
			</ul>
			<p class="u-mb-0">
				<a href="#" class="btn">
					<span class="btn__text">{_search_show_all}</span>
				</a>
			</p>
		</div>
	</div>
</form>
