{default $class = 'u-mb-md u-mb-lg@md u-maw-9-12 u-mx-auto'}
{default $title = false}
{default $annot = false}
{default $items = []}

<div n:class="c-partners, $class">
	<h2 n:if="$title" class="h4 c-partners__title u-ta-c">{$title}</h2>
	<p n:if="$annot" class="c-partners__annot u-ta-c">{$annot}</p>
	<div class="grid grid--center">
		<div n:foreach="$items as $item" class="grid__cell size--6-12@sm size--4-12@md size--3-12@lg">
			{php $image = isset($item->image) ? $item->image->getEntity() ?? false : false}
			{php $position = $item->position ?? false}
			{php $country = $item->country ?? false}

			<div class="c-partners__box u-mb-last-0">
				<p class="c-partners__img">
					{if $image}
						<img class="img img--3-4" src="{$image->getSize('md-3-4')->src}" alt="" loading="lazy">
					{else}
						<img class="img img--3-4" src="/static/img/illust/noimg.svg" alt="" loading="lazy">
					{/if}
				</p>
				<h3 n:if="$item->name ?? false" class="c-partners__name">{$item->name}</h3>
				<p n:if="$item->position ?? false" class="c-partners__position">
					<span class="btn">
						<span class="btn__text">
							{$item->position}
						</span>
					</span>
				</p>
				<p n:if="$item->country ?? false">
					<span class="btn btn--bd">
						<span class="btn__text">
							{$item->country|upper}
						</span>
					</span>
				</p>
			</div>
		</div>
	</div>
</div>