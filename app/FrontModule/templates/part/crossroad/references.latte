{default $class = "u-mb-md u-mb-lg@md"}
{default $titleTag = h2}
{default $titleLang = false}
{default $items = $object->crossroad ?? []}
{default $pager = true}

<section n:if="count($items) > 0" n:class="c-articles, $class">
	<h2 n:tag="$titleTag" n:if="$titleLang" class="c-articles__title">
		{_$titleLang}
	</h2>

	<div class="c-articles__list grid" n:snippet="articleList" data-ajax-append>
		<div n:foreach="$items as $item" class="c-articles__item grid__cell size--6-12@md">
			{include '../box/reference.latte', article: $item, class: false}
		</div>
	</div>

	{if $pager}
		{snippet articlesPagerBottom}
			{control pager, [class: 'u-pt-sm u-pt-md@md', showPages: false, showMoreBtn: true]}
		{/snippet}
	{/if}
</section>
