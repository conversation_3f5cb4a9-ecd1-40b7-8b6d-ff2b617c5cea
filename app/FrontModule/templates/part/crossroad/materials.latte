{default $class = "u-mb-md u-mb-lg@md"}
{default $items = $object->crossroad ?? []}
{default $titleLang = false}
{default $showMore = false}

<section n:if="count($items) > 0" n:class="c-articles, $class">
	<h2 n:if="$titleLang" class="c-articles__title">
		{_$titleLang}
	</h2>

	<div class="c-articles__list grid">
		<div n:foreach="$items as $item" class="c-articles__item grid__cell size--6-12@md">
			{include $templates.'/part/box/material.latte', material: $item, class: false}
		</div>
	</div>

	<p n:if="$showMore" class="u-ta-c u-pt-sm">
		<a n:href="$pages->aboutUs" class="btn btn--primary">
			<span class="btn__text">
				{_"btn_show_all"}
			</span>
		</a>
	</p>
</section>

