{default $class = "u-mb-md u-mb-lg@md"}
{default $name = $object??->nameTitle ?? $object->name}
{default $titleTag = 'h1'}
{default $items = $object->crossroad ?? []}
{default $btn = false}
{* {default $pager = true} *}
{default $uidsToSkip = []}

<section n:if="count($items) > 0" n:class="c-articles, $class">
	<div class="row-main">
		<h2 n:tag="$titleTag" n:if="$name" class="c-articles__title u-ta-c h4">
			{$name}
		</h2>

		<div class="c-articles__list grid">
			<div n:foreach="$items as $item" n:if="(isset($item->uid) && !in_array($item->uid, $uidsToSkip)) || !isset($item->uid)" class="c-articles__item grid__cell size--6-12@md size--4-12@lg">
				{include '../box/feature.latte', article: $item, class: false}
			</div>
		</div>

		{if $btn}
			<p class="c-articles__btn u-ta-c u-pt-md u-pt-lg@md">
				{include $templates . '/part/core/linkChoose.latte', item: $btn, class: "btn", isButton: true, hasArrow: true}
			</p>
		{/if}

		{* {if $pager}
			{snippet articlesPagerBottom}
				{control pager, [class: 'u-pt-sm u-pt-md@md', showPages: false, showMoreBtn: true]}
			{/snippet}
		{/if} *}
	</div>
</section>
