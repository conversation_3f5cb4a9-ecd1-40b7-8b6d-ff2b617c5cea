{default $class = 'u-mb-md u-mb-lg@md'}
{default $items = $object->crossroad ?? []}
{default $snippet = 'articles'}
{default $linkAllTags = $pages->blog}
{default $isAjax = false}

<div n:if="count($items)" n:class="c-tags, $class" {if $isAjax}data-controller="tag-filter" data-tag-filter-target-value="{$snippet}"{/if}>
	<ul class="grid grid--center">
		<li class="grid__cell size--auto">
			{php $isActive = $presenter->link($linkAllTags) == $presenter->link($object)}
			<a n:tag="$isActive ? span"{if !$isActive} href="{plink $linkAllTags}"{if $isAjax} data-tag-filter-target="link" data-action="click->tag-filter#filter"{/if}{/if} n:class="btn, btn--square, $isActive ? btn--secondary : btn--gray">
				<span class="btn__text">
					{_"tag_all"}
				</span>
			</a>
		</li>
		<li n:foreach="$items as $item" class="grid__cell size--auto">
			{var $tag = $item->tag ?? $item}
			{php $isActive = $presenter->link($tag) == $presenter->link($object)}
			<a n:tag="$isActive ? span"{if !$isActive} href="{plink $tag}"{if $isAjax} data-tag-filter-target="link" data-action="click->tag-filter#filter"{/if}{/if} n:class="btn, btn--square, $isActive ? btn--secondary : btn--gray">
				<span class="btn__text">
					{$tag->name}
					{* {if $item->count ?? false}({$item->count}){/if} *}
				</span>
			</a>
		</li>
	</ul>
</div>