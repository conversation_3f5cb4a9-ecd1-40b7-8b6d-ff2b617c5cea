<footer class="footer">
	<div class="row-main">
		<div class="footer__inner">
			<div class="footer__menu">
				{include $templates.'/part/menu/footer.latte' props: [
					data: [
						[
							title: $object->mutation->cf?->footerMenu?->footer_menu_1?->title ?? false,
							list: $object->mutation->cf?->footerMenu?->footer_menu_1?->list ?? false,
						],
						[
							title: $object->mutation->cf?->footerMenu?->footer_menu_2?->title ?? false,
							list: $object->mutation->cf?->footerMenu?->footer_menu_2?->list ?? false,
						],
					]
				]}
				{php $socials = $pages->contact??->cf->socials ?? false}
				<nav n:if="isset($socials) && $socials" class="m-footer m-footer--social" data-controller="toggle-class">
					<h2 class="m-footer__title h6 link-mask">
						{_"social_media"}
					</h2>
					<ul class="m-footer__list">
						<li n:ifcontent class="m-footer__item">
							<a href="{$socials->facebook}" target="_blank" class="m-footer__link" rel="noopener noreferrer">{_"facebook"}</a>
						</li>
						<li n:ifcontent class="m-footer__item">
							<a href="{$socials->linkedin}" target="_blank" class="m-footer__link" rel="noopener noreferrer">{_"linkedin"}</a>
						</li>
						<li n:ifcontent class="m-footer__item">
							<a href="{$socials->youtube}" target="_blank" class="m-footer__link" rel="noopener noreferrer">{_"youtube"}</a>
						</li>
					</ul>
				</nav>
			</div>
			<div class="footer__contacts">
				{php $branches = $pages->contact??->cf->branches??->items ?? [[]]}
				{include $templates.'/part/box/footerContact.latte', branches: $branches}
			</div>
			<div class="footer__copyrights">
				<p class="footer__logo u-mb-0@md">
					{if !$isHomepage && $pages->title !== null}
						<a n:href="$pages->title">
							<img src="/static/img/logo-colors.svg" alt="{_logo}" width="236" height="40">
						</a>
					{else}
						<img src="/static/img/logo-colors.svg" alt="{_logo}" width="236" height="40">
					{/if}
				</p>

				{php $menuCopyright = $object->mutation->cf?->footerMenu?->footer_menu_copyright ?? []}
				<p class="footer__content u-mb-0@md">
					{_"copyright"|replace:'%currentYear', (string)date('Y')}

					{foreach $menuCopyright as $menuItem}
						{php $type = $menuItem->toggle}
						{php $page = isset($menuItem->systemHref) && isset($menuItem->systemHref->page) ? $menuItem->systemHref->page->getEntity() ?? false : false}
						{php $href = $menuItem->customHref??->href ?? false}
						{php $hrefNameSystem = $menuItem->systemHref??->hrefName ?? false}
						{php $hrefNameCustom = $menuItem->customHref??->hrefName ?? false}

						{if $type == 'systemHref' && $page}
							| <a href="{plink $page}" n:ifcontent class="m-footer__link">
								{if $hrefNameSystem}
									{$hrefNameSystem}
								{else}
									{$page->nameAnchor}
								{/if}
							</a>
						{elseif $type == 'customHref' && $href && $hrefNameCustom}
							| <a href="{$href}" class="m-footer__link" target="_blank" rel="noopener noreferrer">
								{$hrefNameCustom}
							</a>
						{/if}
					{/foreach}
				</p>
			</div>

			{php $socials = $pages->contact??->cf->socials ?? false}
			<ul n:if="$socials" class="footer__social">
				<li n:if="$socials->facebook"><a href="{$socials->facebook}" class="btn btn--transparent" target="_blank" rel="noopener noreferrer"><span class="btn__text">{_"facebook"}</span></a></li>
				<li n:if="$socials->linkedin"><a href="{$socials->linkedin}" class="btn btn--transparent" target="_blank" rel="noopener noreferrer"><span class="btn__text">{_"linkedin"}</span></a></li>
				<li n:if="$socials->youtube"><a href="{$socials->youtube}" class="btn btn--transparent" target="_blank" rel="noopener noreferrer"><span class="btn__text">{_"youtube"}</span></a></li>
			</ul>

			{* <button type="button" class="as-link" data-cookie-open>{_"btn_cookies_setting"}</button> *}
		</div>
	</div>
</footer>
