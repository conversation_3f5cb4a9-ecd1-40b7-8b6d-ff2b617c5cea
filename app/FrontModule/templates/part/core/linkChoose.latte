{default $item = null}
{default $class = 'm-footer__link'}
{default $icon = false}
{default $isButton = false}
{default $hasArrow = false}
{php $type = $item->toggle}
{php $page = isset($item->systemHref) && isset($item->systemHref->page) ? $item->systemHref->page->getEntity() ?? false : false}
{php $hrefName = ($item->systemHref??->hrefName ?? false) ?: ($item->customHref??->hrefName ?? false)}
{php $href = $item->customHref??->href ?? false}

{if $type == 'systemHref' && $page}
	<a href="{plink $page}" n:ifcontent n:class="$class">
		{if $isButton}<span class="btn__text">{/if}
			{if $icon}
				{php $img = $icon->getEntity()->getSize('icon')}
				<img src="{$img->src}" width="{$img->width}" height="{$img->height}" alt="" class="btn__icon">
			{/if}
			{if $hrefName}
				{$hrefName}
			{else}
				{$page->nameAnchor}
			{/if}
			{if $hasArrow} <span class="btn__arrow u-fw-n">&rarr;</span>{/if}
		{if $isButton}</span>{/if}
	</a>
{elseif $type == 'customHref' && $href && $hrefName}
	<a href="{$href}" n:class="$class" target="_blank">
		{if $isButton}<span class="btn__text">{/if}
			{if $icon}{$icon}{/if} {$hrefName}{if $hasArrow} <span class="btn__arrow u-fw-n">&rarr;</span>{/if}
		{if $isButton}</span>{/if}
	</a>
{/if}
