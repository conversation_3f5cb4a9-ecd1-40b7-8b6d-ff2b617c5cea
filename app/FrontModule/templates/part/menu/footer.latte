{ifset $props}
	{foreach $props['data'] as $item}
		<nav n:if="isset($item['list']) && $item['list']" class="m-footer" data-controller="toggle-class">
			<h2 n:if="isset($item['title']) && $item['title']" class="m-footer__title h6 link-mask">
				{$item['title']}
				<button type="button" class="m-footer__btn as-link link-mask__link" data-action="toggle-class#toggle" aria-expanded="false">
					{('angle-down')|icon}
					<span class="u-vhide">
						{_"btn_show"}
					</span>
				</button>
			</h2>
			<ul class="m-footer__list">
				<li n:foreach="$item['list'] as $item" n:ifcontent class="m-footer__item">
					{include $templates . '/part/core/linkChoose.latte', item: $item}
				</li>
			</ul>
		</nav>
	{/foreach}
{/ifset}
