{varType App\Model\Pages $pages}
{default $isCareer = isset($object->uid) && $object->uid === 'career' ?? false}

<header n:class="$isCareer ? 'header header--shadow' : 'header'">
	<div class="row-main">
		<div class="header__wrap" data-controller="toggle-class">
			<h1 n:tag="!$isHomepage ? 'p'" class="header__logo">
				{* <a n:href="$pages->title" n:if="$pages->title !== null">
					<img src="/static/img/logo-bw.svg" alt="{_logo}" width="250" height="16">
				</a> *}
				{if !$isHomepage && $pages->title !== null}
					<a n:href="$pages->title">
						{include 'logoSvg.latte'}
					</a>
				{else}
					{include 'logoSvg.latte'}
				{/if}
			</h1>
			{* <div class="header__search">
				{include 'form/search.latte'}
			</div> *}
			{* <div class="header__other">
				{include 'box/login.latte'}
				{include 'box/cart.latte'}
			</div> *}
			{include $templates.'/part/form/search.latte'}
			<p class="header__burger u-mb-0">
				<button type="button" class="b-burger" data-action="toggle-class#toggle">
					<span></span>
					<span></span>
					<span></span>
					<span></span>
				</button>
			</p>
			<div class="header__menu">
				{control menu}
			</div>
		</div>
	</div>

</header>
