{default $class = 'u-mb-md u-mb-lg@md'}
{default $name = $seoLink??->name ?? $object->name}
{default $uid = $object->uid ?? false}
{default $annotation = $object->cf->base->annotation ?? $object??->annotation ?? $seoLink??->description ?? null}
{default $context = null}
{default $cf = $object->cf->annot ?? $object->$context->cf->base->annot ?? false}
{default $bg = $cf->bg ?? 'u-bgc-yellow'}
{default $btn = $cf->btn ?? false}
{default $rightContent = $cf->right ?? false}
{* {default $date = false} *}
{* <p n:if="$date" class="b-annot__date">
	{$object->publicFrom|date:"j. n. Y"}
</p> *}

{if $uid == 'articlesMain' && isset($tagValue) && $tagValue}
	{php $name = $name . ' - ' . $tagValue->value}
{/if}

<header n:class="b-annot, $class, $bg">
	<div class="row-main">
		<div class="b-annot__grid grid grid--y-sm grid--y-md@md">
			<div class="grid__cell size--6-12@lg u-mb-last-0">
				{php $videoLink = $rightContent->link ?? false}
				{php $image = isset($rightContent->image) ? $rightContent->image->getEntity() ?? false : false}

				{if $videoLink}
					{* Video *}
					{include $templates.'/part/core/video.latte', class: 'b-annot__right', toggle: 'link', link: $videoLink, poster: $image}
				{elseif $image}
					{* Obrázek *}
					<p class="b-annot__right u-mb-0">
						<img class="img img--16-9" src="{$image->getSize('xl-16-9')->src}" alt="" fetchpriority="high">
					</p>
				{/if}

				{block right}{/block}
			</div>
			<div class="grid__cell size--6-12@lg">
				<div class="b-annot__left u-maw-4-12 u-mb-last-0">
					<h1 class="h4 b-annot__title">
						{$name}
					</h1>
					<div n:ifcontent class="u-mb-last-0 b-annot__desc">
						<p n:if="$annotation">
							{rtrim($annotation ?? '', '<br>')|texy|noescape}
						</p>
						{block annot}{/block}
					</div>
					<p n:ifcontent>
						{if $btn}
							{include $templates . '/part/core/linkChoose.latte', item: $btn, class: "b-annot__btn btn", isButton: true, hasArrow: true}
						{/if}
						{block btn}{/block}
					</p>
				</div>
			</div>
		</div>
	</div>
</header>
