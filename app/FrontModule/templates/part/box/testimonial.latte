{default $class = ''}
{var $testimonial = $testimonial->testimonial?->getEntity() ?? false}
{default $cf = $testimonial->testimonial->cf->base ?? false}
{default $title = $cf->title ?? false}
{default $bg = $cf->bg ?? 'u-bgc-green'}
{default $biggerQuote = $cf->biggerQuote ?? false}

<section n:if="$testimonial" n:class="b-testimonial, $class, $bg, u-pt-xl, u-pb-xl">
	<div class="row-main">
		<div class="b-testimonial__item grid grid--middle grid--x-lg">
			<div class="grid__cell grid__cell--eq size--6-12@lg">
				{if isset($cf->mainImage) && $cf->mainImage}
					{php $img = $cf->mainImage->getEntity()->getSize('xl-16-9')}
					<img class="b-testimonial__img" src="{$img->src}" alt="" loading="lazy" width="" height="">
				{else}
					<img class="b-testimonial__img" src="/static/img/illust/noimg.svg" alt="" loading="lazy" width="" height="">
				{/if}
			</div>
			<div class="grid__cell grid__cell--eq size--6-12@lg">
				<div class="b-testimonial__content">
					<h2 n:if="$title" class="h4 b-testimonial__title">{$title}</h2>
					<p n:if="isset($cf->quote) && $cf->quote" n:class="$biggerQuote ? 'b-testimonial__quote b-testimonial__quote--bigger' : 'b-testimonial__quote'">
						{$cf->quote}
					</p>
					<p class="b-testimonial__author" n:if="isset($cf->name) && $cf->name">
						<strong>{$cf->name}</strong><span n:if="isset($cf->position) && $cf->position">, {$cf->position}</span>
						<span n:if="isset($cf->company) && $cf->company">{$cf->company}</span>
					</p>
					<p n:if="isset($cf->logo) && $cf->logo" class="b-testimonial__logo u-mb-0">
						{php $img = $cf->logo->getEntity()->getSize('xs')}
						<img class="b-testimonial__logo" src="{$img->src}" alt="" loading="lazy" width="" height="">
					</p>
					{if isset($cf->button) && $cf->button}
						{include $templates . '/part/core/linkChoose.latte', item: $cf->button, class: 'b-testimonial__btn btn btn--bd', isButton: true, hasArrow: true}
					{/if}
					{if isset($cf->images) && $cf->images}
						<ul class="b-testimonial__logos">
							{foreach $cf->images as $image}
								{php $img = $image->getSize('xs')}
								<li class="b-testimonial__logo">
									<img class="b-testimonial__logo" src="{$img->src}" alt="" loading="lazy" width="" height="">
								</li>
							{/foreach}
						</ul>
					{/if}
				</div>
			</div>
		</div>
	</div>
</section>
