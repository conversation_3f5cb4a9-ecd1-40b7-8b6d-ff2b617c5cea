{default $class = 'u-mb-md u-mb-lg@md'}
{default $title = false}
{default $annot = false}
{default $branches = false}
{default $image = false}
{default $numbers = false}

<div n:class="b-branches-map, $class">
	<h2 class="b-branches-map__title u-ta-c h4" n:if="$title">{$title}</h2>
	<p class="b-branches-map__annot u-ta-c" n:if="$annot">{$annot}</p>
	<div n:if="$branches" class="b-branches-map__branches">
		<ul class="grid grid--center">
			<li n:foreach="$branches as $branch" n:if="$branch->name ?? false" class="grid__cell size--auto">
				<span class="b-branches-map__branch">
					{$branch->name}
					<span n:if="$branch->count ?? false" class="b-branches-map__count">{$branch->count}</span>
				</span>
			</li>
		</ul>
	</div>
	<p class="b-branches-map__img">
		{if $image}
			<img src="{$image->getSize('3xl')->src}" alt="" loading="lazy" width="1384" height="657">
		{else}
			<img src="/static/img/illust/noimg.svg" alt="" loading="lazy" width="1384" height="657">
		{/if}
	</p>
	{include $templates.'/part/box/count.latte', class: 'b-branches-map__numbers u-maw-8-12 u-mx-auto', items: $numbers}
	{var $cc = null}
	{foreach $object->cc as $key => $value}
		{if strpos($key, 'branches_map') === 0}
			{var $cc = $value}
			{breakIf true}
		{/if}
	{/foreach}
	{var $button = isset($cc[0]->button) ? $cc[0]->button : null}
	<p n:if="$button" class="b-branches-map__btn u-ta-c u-mb-0">
		{include $templates . '/part/core/linkChoose.latte', item: $button, class: "btn", isButton: true, hasArrow: true}
	</p>
</div>
