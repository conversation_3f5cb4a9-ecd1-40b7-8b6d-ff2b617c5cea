{default $class = 'u-bgc-tertiary u-pt-lg u-pb-3xl'}
{default $cf = $object->cf??->support ?? false}
{default $button = $object->cf?->support->button ?? false}
{default $title = $object->cf?->support->title ?? false}
{default $content = $object->cf?->support->content ?? false}
{default $image = $object->cf?->support->image ?? false}
{default $hasAnimation = $object->cf?->support->hasAnimation ?? false}

<section n:if="$cf && $title && $content" class="{$class}">
	<div class="row-main">
		<div class="grid grid--x-3xl">
			<div class="grid__cell size--5-12@lg">
				<h2 n:if="$title" class="h4">
					{$title}
				</h2>
				{if $content}
					{$content|noescape}
				{/if}
				{if $button}
					{include $templates . '/part/core/linkChoose.latte', item: $button, class: "btn btn--bd", isButton: true, hasArrow: true}
				{/if}
			</div>
			<div class="grid__cell size--7-12@lg">
				<div class="b-animation" data-controller="animation">
					{if $image}
						{php $img = $image->getSize('xl-16-9')}
						<img class="b-animation__img" src="{$img->src}" alt="" loading="lazy" width="768" height="309">
					{elseif $hasAnimation}
						<img class="b-animation__img" src="/static/img/illust/graf.png" alt="" loading="lazy" width="757" height="305">
					{/if}
					{if $hasAnimation}
						<svg viewBox="0 0 768 309" xmlns="http://www.w3.org/2000/svg" style="position: absolute; top: 0; left: 0;">
							<path data-animation-target="path" d="M1 234C337.96 210.103 460.107 205.124 756 1" fill="none" stroke="transparent" stroke-width="1" />
						</svg>
						<span class="b-animation__svg" data-animation-target="rocket">
							{('rocket')|icon}
						</span>
					{/if}
				</div>
			</div>
		</div>
	</div>
</section>
