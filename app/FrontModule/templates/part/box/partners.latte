{default $class = 'u-bgc-white u-pt-lg u-pb-lg'}
{default $cf = $object->cf?->partners ?? false}
{default $title = $cf->title ?? false}
{default $content = $cf->content ?? false}
{default $items = $cf->items ?? []}

<section n:if="count($items)" n:class="b-partners, $class">
	<div class="row-main">
		<div class="u-maw-hp u-mx-auto u-ta-c u-mb-sm u-mb-mdxs@md u-mb-last-0">
			<h2 n:if="$title" class="h4 b-partners__title">{$title}</h2>
			<p n:if="$content">{$content}</p>
		</div>
		<ul class="b-partners__list">
			<li n:foreach="$items as $item" class="b-partners__item">
				{if isset($item->image) && $item->image}
					{php $image = $item->image->getEntity()->getSize('sm')}
					<img src="{$image->src}" alt="" loading="lazy" class="b-partners__logo">
				{/if}
				<p n:if="isset($item->text) && $item->text" class="u-mb-0">
					{$item->text}
				</p>
			</li>
		</ul>
	</div>
</section>
