{default $class = false}
{default $items = []}

<div n:if="count($items)" n:class="b-count, $class">
	<ul class="grid grid--center">
		<li n:foreach="$items as $item" class="grid__cell size--6-12@md size--4-12@lg">
			<p class="b-count__box">
				<b n:if="$item->number ?? false" class="b-count__num">{$item->number}</b>
				<b n:if="$item->text1 ?? false" class="b-count__text">{$item->text1}</b>
				{$item->text2 ?? false}
			</p>
		</li>
	</ul>
</div>