{default $class = 'u-mt-md u-mt-lg@md u-mb-md u-mb-lg@md'}
{default $title = false}
{default $items = []}

<div n:if="count($items) > 0" class="row-main">
	<div n:class="b-tabs, $class" data-controller="tabs">
		<h2 n:if="$title" class="h4 u-mb-sm u-mb-md@md">{$title}</h2>
		<div class="b-tabs__grid grid">
			<div class="b-tabs__cell-tabs grid__cell">
				<ul class="b-tabs__list">
					<li n:foreach="$items as $item" n:if="$item->name ?? false">
						<button type="button" n:class="btn, btn--lg, btn--square, $iterator->isFirst() ? btn--secondary : btn--gray" data-action="tabs#toggle" data-tabs-target="btn">
							<span class="btn__text">
								{$item->name}
							</span>
						</button>
					</li>
				</ul>
			</div>
			<div class="b-tabs__cell-content grid__cell">
				<div n:foreach="$items as $item" n:if="$item->name ?? false" n:class="b-tabs__fragment, $iterator->isFirst() ? is-active" data-tabs-target="content">
					{include $templates.'/part/box/content.latte', class: 'b-tabs__content', content: $item->content ?? false}
				</div>
			</div>
		</div>
	</div>
</div>
