{default $class = false}
{default $person = false}

<article n:if="$person" n:class="b-author, $class, link-mask">
	<div class="b-author__img">
		{if $person->getFirstImage() !== null}
			{php $img = $person->getFirstImage()->getSize('md-3-4')}
			<img class="img img img--3-4" src="{$img->src}" alt="{$person->name}" loading="lazy">
		{else}
			<img class="img img img--3-4" src="/static/img/illust/noimg.svg" alt="" loading="lazy">
		{/if}
	</div>

	<div class="b-author__content u-mb-last-0">
		<h3 class="b-author__name">
			<a n:href="$person" class="b-author__link link-mask__link">
				{$person->name}
			</a>
			{* <span class="b-author__value">
				({$person->blogsPublic->count()})
			</span> *}
		</h3>
		<p n:if="$person->cf->base?->position ?? false" class="b-author__desc">
			{$person->cf->base->position}
		</p>
		<p n:if="$person->cf->base?->annotation ?? false" class="b-author__annot">
			{$person->cf->base->annotation}
		</p>
	</div>

	<p class="b-author__tags u-mb-0">
		<span n:foreach="$person->authorTags as $tag" class="btn btn--square btn--gray">
			<span class="btn__text">
				{$tag->nameAnchor}
			</span>
		</span>
	</p>

</article>
