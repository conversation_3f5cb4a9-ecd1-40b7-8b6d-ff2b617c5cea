{default $class = false}
{default $titleTag = 'h2'}
{default $article = false}
{var $name = $article->name ?? $article->nameTitle}
{var $annotation = $article->cf->base->annotation ?? $article->annotation ?? false}

<article n:if="$article" n:class="b-feature, $class, link-mask">
	<p class="b-feature__img">
		{php $mainImage = $article->blog->cf->base->mainImage ?? $article->cf->annot->right->image ?? $article->feature->cf->base->annot->right->image ?? false}
		{php $img = $mainImage ? $mainImage->getEntity() ?? false : false}
		{if $img}
			{php $image = $img->getSize('md-3-1')}
			<img class="img img img--3-1" src="{$image->src}" alt="" loading="lazy">
		{else}
			<img class="img img--3-1" src="/static/img/illust/noimg.svg" alt="" loading="lazy">
		{/if}
	</p>
	<div class="b-feature__content u-mb-last-0">
		<h2 n:if="$name" n:tag="$titleTag" class="h5">
			<a href="{plink $article}" class="b-feature__link link-mask__link">
				{$name}
			</a>
		</h2>
		<p n:if="$annotation" class="b-feature__annot">
			{$annotation|texy|noescape}
		</p>
		<p class="u-mb-0 b-feature__btn">
			<a href="{plink $article}" class="btn btn--bd link-mask__unmask">
				<span class="btn__text">
					{_"btn_read_more"} <span class="btn__arrow u-fw-n">&rarr;</span>
				</span>
			</a>
		</p>
	</div>
</article>
