{default $class = 'u-bgc-secondary u-pt-lg u-pb-3xl'}
{default $cf = $object->cf??->bussines ?? false}
{default $items = $cf->items ?? []}

<div n:if="isset($cf) && $cf" n:class="$class">
	<div class="row-main">
		<h2 n:if="$cf->title ?? false" class="h4 u-ta-c">{$cf->title}</h2>
		<div n:if="$cf->content ?? false" class="u-maw-hp u-mx-auto u-mb-last-0 u-ta-c u-mb-mdxs">
			{$cf->content|noescape}
		</div>
		<div n:if="count($items)" n:class="b-highlights">
			<ul class="b-highlights__list grid grid--center">
				<li n:foreach="$items as $item" class="b-highlights__item grid__cell size--6-12@md size--3-12@xl">
					<div class="b-highlights__box u-mb-last-0">
						{php $image = isset($item->image) ? $item->image->getEntity() ?? false : false}
						<p n:if="$item->name ?? false" class="tag">
							<img n:if="$image" src="{$image->getSize('xs')->src}" alt="" class="tag__icon" loading="lazy">
							{$item->name}
						</p>
						<h3 n:if="$item->title" class="h6">
							{$item->title}
						</h3>
						<p n:if="$item->text" class="u-mt-auto">
							{$item->text|texy|noescape}
						</p>
						{if $item->button ?? false}
							{include $templates . '/part/core/linkChoose.latte', item: $item->button, class: "btn btn--bd", isButton: true, hasArrow: true}
						{/if}
					</div>
				</li>
			</ul>
		</div>
	</div>
</div>
