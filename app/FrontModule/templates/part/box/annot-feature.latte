{default $class = 'u-mb-sm u-mb-md@md'}
{default $name = $seoLink??->name ?? $object->name}
{default $annotation = $seoLink??->description ?? $object??->annotation ?? null}

<header n:class="b-annot-blog, $class, u-maw-9-12, u-mx-auto, u-mb-last-0">
	<div class="u-maw-7-12 u-mx-auto">
		<h1 class="h4 u-mb-xs">
			{$name}
		</h1>
		<p class="b-annot-blog__info u-mb-md" n:if="isset($object->authors) && $object->authors">

			<span class="btn btn--bd btn--square">
				<span class="btn__text">
					{$object->publicFrom|date:"j. n. Y"}
				</span>
			</span>
		</p>
	</div>

	{php $img = isset($object->blog->cf->base->mainImage) ? $object->blog->cf->base->mainImage->getEntity() ?? false : false}
	<p n:if="$img" class="b-annot-blog__img u-mb-md">
		{* TODO: srcset *}
		<img class="img img--16-9" src="{$img->getSize('xl-16-9')->src}" alt="{$img->getAlt($mutation)}" fetchpriority="high">
		<img class="img img--16-9" srcset="
			{$img->getSize('sm-16-9')->src} 350w,
			{$img->getSize('md-16-9')->src} 470w,
			{$img->getSize('lg-16-9')->src} 700w,
			{$img->getSize('xl-16-9')->src} 1000w,
			{$img->getSize('2xl-16-9')->src} 1400w,
			{$img->getSize('3xl-16-9')->src} 1920w"
		sizes="(max-width: 62.5rem) 100vw,
			103rem"
		src="{$img->getSize('3xl-16-9')->src}" alt="{$img->getAlt($mutation)}" loading="lazy">
	</p>
	<p n:if="$object->cf->base->annotation ?? false">
		{rtrim($object->cf->base->annotation ?? '', '<br>')|texy|noescape}
	</p>
</header>

