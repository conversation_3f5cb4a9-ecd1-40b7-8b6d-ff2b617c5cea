{default $class = 'u-mb-md u-mb-lg@md'}
{default $title = false}
{default $items = []}

<div n:if="count($items)" class="row-main">
	<div n:class="b-highlights, $class">
		<h2 n:if="$title" class="b-highlights__title u-ta-c h4">{$title}</h2>
		<ul class="b-highlights__list grid grid--center">
			<li n:foreach="$items as $item" class="b-highlights__item grid__cell size--6-12@md size--4-12@lg">
				<div class="b-highlights__box u-mb-last-0">
					{php $image = isset($item->image) ? $item->image->getEntity() ?? false : false}
					<p n:if="$item->name ?? false" class="tag">
						<img n:if="$image" src="{$image->getSize('xs')->src}" alt="" class="tag__icon" loading="lazy">
						{$item->name}
					</p>
					<p n:if="$item->text">
						{$item->text|texy|noescape}
					</p>
					{php $link = $item->link ?? false}
					{if $link}
						{include $templates.'/part/core/linkChoose.latte', item=>$link, class=>'b-highlights__link', hasArrow=>true, class=>'btn', isButton=>true}
					{/if}
				</div>
			</li>
		</ul>
	</div>
</div>