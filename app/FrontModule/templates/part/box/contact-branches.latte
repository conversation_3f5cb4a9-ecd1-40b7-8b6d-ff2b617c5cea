{default $class = 'u-mb-5xl@md u-mb-xl'}
{default $cf = $object->cf->branches ?? []}
{default $title = $cf->title ?? false}
{default $items = $cf->items ?? []}

<section n:if="count($items)" n:class="b-branch, $class">
	<div class="row-main">
		<h2 n:ifcontent class="b-branch__title h4 u-ta-c">{$title}</h2>
		<div class="b-branch__list">
			{foreach $items as $item}
				<div n:if="isset($item) && $item" class="b-branch__item">
					{php $image = $item->image ?? false}
					{php $city = $item->city ?? false}
					{php $address = $item->address ?? false}
					{php $openingTime = $item->hours ?? false}

					<div n:if="$image" class="b-branch__img">
						{php $imgSrc = $image->getSize('sm')->src ?? false}
						<img n:if="isset($image)" src="{$imgSrc}"  alt="{$image->getAlt($mutation)}" loading="lazy">
					</div>

					<div n:ifcontent class="b-branch__cnt">
						<h3 class="b-branch__subtitle">{$city}</h3>
						<p class="b-branch__address">
							{php $link = 'https://www.google.com/maps/search/?api=1&query=' . urlencode($address)}

							<a href="{$link}" target="_blank" rel="noopener noreferrer" class="b-branch__link">
								{$address}
							</a>
						</p>

						<p n:if="isset($openingTime) && $openingTime" class="b-branch__hours">{$openingTime|texy|noescape}</p>

						<a n:if="isset($item->map) && $item->map" href="{$item->map}" class="b-branch__btn btn btn--bd" target="_blank">
							<span class="btn__text">{_"contact_map"} &rarr;</span>
						</a>
					</div>
				</div>
			{/foreach}
		</div>
	</div>
</section>
