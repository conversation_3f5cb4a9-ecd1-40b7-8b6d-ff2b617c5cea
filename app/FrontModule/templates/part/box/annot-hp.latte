{default $class = ''}
{default $name = $seoLink??->name ?? $object->name}
{default $uid = $object->uid ?? false}
{default $annotation = $seoLink??->description ?? $object??->annotation ?? null}
{default $cf = $object->cf->annot_hp ?? false}
{default $btn = $cf->btn ?? false}
{default $awards = $cf->awards ?? false}
{default $rating = $cf->rating ?? false}

{if $uid == 'articlesMain' && isset($tagValue) && $tagValue}
	{php $name = $name . ' - ' . $tagValue->value}
{/if}

<header n:class="b-annot, b-annot--hp, u-bgc-white, $class">
	<div class="row-main">
		<div class="b-annot__box">
			<div class="b-annot__cnt">
				<h1 class="b-annot__title">
					{$name}
				</h1>
				<div n:ifcontent class="u-mb-last-0 b-annot__desc">
					<p n:if="$annotation">
						{rtrim($annotation ?? '', '<br>')|texy|noescape}
					</p>
					{block annot}{/block}
					<p n:ifcontent>
						{if $btn}
							{include $templates . '/part/core/linkChoose.latte', item: $btn, class: "b-annot__btn btn", isButton: true, hasArrow: true}
						{/if}
						{block btn}{/block}
					</p>
				</div>
				<div n:if="$awards" class="b-awards">
					<h2 n:if="$awards && $awards->title" class="h6">
						{$awards->title}
					</h2>

					<ul n:if="$awards && $awards->images" class="b-awards__list">
						<li n:foreach="$awards->images as $item" class="b-awards__item">
							<img src="{$item->getSize('xs')->src}" alt="" class="b-awards__img">
						</li>
					</ul>

					<div n:if="$rating" class="rating">
						<img src="/static/img/illust/google-logo.svg" alt="Google" class="rating__logo" width="35" height="35" loading="lazy">
						<span class="rating__text"><strong>{$rating}</strong>/5</span>
						<div class="stars">
							<div class="stars__icons">
								{('star')|icon}
								{('star')|icon}
								{('star')|icon}
								{('star')|icon}
								{('star')|icon}
							</div>
							{var $ratingNum = floatval(str_replace(',', '.', $rating))}
							<div class="stars__icons stars__icons--active" style="width: {min(100, round(($ratingNum/5)*100, 1))}%;">
								{('star')|icon}
								{('star')|icon}
								{('star')|icon}
								{('star')|icon}
								{('star')|icon}
							</div>
						</div>
					</div>
				</div>
			</div>
			<div n:if="isset($cf->authors) && $cf->authors" class="b-annot__people">
				<div class="c-cards">
					<div class="b-card link-mask" n:foreach="$cf->authors as $author">
						<h2 n:if="$author->getLocalization($mutation)" class="b-card__title">
							<a n:href="$author->getLocalization($mutation)" class="b-card__link link-mask__link">
								<span class="tag tag--blue">{$author->getLocalization($mutation)->name}</span>
							</a>
						</h2>
						{php $img = isset($author->cf->base->mainImage) ? $author->cf->base->mainImage->getEntity() ?? false : false}
						{if $img}
							{php $image = $img->getSize('md-3-5')}
							<img class="img img--3-5" src="{$image->src}" alt="{$author->internalName}" width="{$image->width}" height="{$image->height}">
						{else}
							<img class="img img--3-5" src="/static/img/illust/noimg.svg" alt="" loading="lazy">
						{/if}
						<div n:if="count($author->tags) > 0" class="c-tags">
							<span n:foreach="$author->tags as $tag" class="tag tag--yellow">{$tag->internalName}</span>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</header>
