{default $class = false}
{default $material = false}

<article n:if="$material" n:class="b-article, $class, link-mask">
	<p class="b-article__tags u-mb-xs">
		{* <p>
			{$material->publicFrom|date:"j. n. Y"}
			{if count($material->authors->fetchAll())}
				| {foreach $material->authors as $author}{$author->nameAnchor}{sep}, {/sep}{/foreach}
			{/if}
		</p> *}
		<span n:foreach="$material->materialTags as $tag" class="btn btn--square btn--gray">
			<span class="btn__text">
				{$tag->nameAnchor}
			</span>
		</span>
	</p>

	<div class="b-article__img">
		<div class="b-article__img">
			{php $image = $material->material->cf->base->annot->right->image ?? false}
			{if $image}
				{php $img = $image->getEntity()->getSize('md-16-9')}
				<img class="img img--16-9" src="{$img->src}" alt="{$material->name}" width="{$img->width}" height="{$img->height}" loading="lazy">
			{else}
				<img class="img img--16-9" src="/static/img/illust/noimg.svg" alt="" loading="lazy">
			{/if}

			{* {if $material->getFirstImage() !== null}
				{php $img = $material->getFirstImage()->getSize('md')}
				<img class="img img--16-9" src="{$img->src}" alt="{$material->name}" loading="lazy">
			{else}
				<img class="img img--16-9" src="/static/img/illust/noimg.svg" alt="" loading="lazy">
			{/if} *}
		</div>
	</div>

	<div class="b-article__content u-pt-sm u-mb-last-0">
		<h3 class="b-article__title">
			<a n:href="$material" class="b-article__link link-mask__link">
				{$material->name}
			</a>
			<span class="b-article__value">
			</span>
		</h3>
		<p n:if="$material->cf->base?->position ?? false" class="b-article__desc">
			{$material->cf->base->position}
		</p>
		<p n:if="$material->cf->base?->annotation ?? false" class="b-article__annot">
			{$material->cf->base->annotation}
		</p>
		<p class="u-mb-0 b-article__btn">
			<a href="{plink $material}" class="btn link-mask__unmask">
				<span class="btn__text">
					{_"download_webinar"} <span class="u-fw-n">&rarr;</span>
				</span>
			</a>
		</p>
	</div>
</article>
