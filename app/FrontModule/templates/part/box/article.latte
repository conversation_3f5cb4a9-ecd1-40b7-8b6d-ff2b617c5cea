{default $class = false}
{default $titleTag = 'h2'}
{default $article = false}

<article n:if="$article" n:class="b-article, $class, link-mask">
	<p class="b-article__tags u-mb-xs">
		<span n:foreach="$article->blogTags as $tag" class="btn btn--square btn--gray">
			<span class="btn__text">
				{$tag->nameAnchor}
			</span>
		</span>
	</p>
	<p class="b-article__img">
		{php $img = isset($article->blog->cf->base->mainImage) ? $article->blog->cf->base->mainImage->getEntity() ?? false : false}
		{if $img}
			<img class="img img--16-9" srcset="
					{$img->getSize('sm-16-9')->src} 350w,
					{$img->getSize('md-16-9')->src} 470w,
					{$img->getSize('lg-16-9')->src} 700w,
					{$img->getSize('xl-16-9')->src} 1000w,
					{$img->getSize('2xl-16-9')->src} 1400w"
				sizes="(max-width: 46.875rem) 100vw,
					(max-width: 90rem) 50vw,
					59.4rem"
				src="{$img->getSize('lg-16-9')->src}" alt="{$img->getAlt($mutation)}" loading="lazy">
		{else}
			<img class="img img--16-9" src="/static/img/illust/noimg.svg" alt="" loading="lazy">
		{/if}
	</p>
	<div class="b-article__content u-mb-last-0">
		<p>
			{$article->publicFrom|date:"j. n. Y"}
			{if count($article->authors->fetchAll())}
				| {foreach $article->authors as $author}{$author->nameAnchor}{sep}, {/sep}{/foreach}
			{/if}
		</p>
		<h2 n:tag="$titleTag" class="h5">
			<a href="{plink $article}" class="b-article__link link-mask__link">
				{$article->nameTitle}
			</a>
		</h2>
		<p n:if="$article->cf->base->annotation ?? false" class="b-article__annot">
			{$article->cf->base->annotation|texy|noescape}
		</p>
		<p class="u-mb-0 b-article__btn">
			<a href="{plink $article}" class="btn link-mask__unmask">
				<span class="btn__text">
					{_"btn_read_more"} <span class="btn__arrow u-fw-n">&rarr;</span>
				</span>
			</a>
		</p>
	</div>
</article>
