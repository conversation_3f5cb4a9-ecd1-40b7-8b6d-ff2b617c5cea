{default $snippetSuffix = ""}

{snippet form}
	{php $control['form']->action .= "#frm-contactForm-form"}
	{form form class: 'f-contact block-loader u-maw-7-12 u-mx-auto', data-naja: '', novalidate: "novalidate"}

		{control messageForForm, $flashes, $form}

		<div class="u-mb-xs">
			<div class="grid grid--y-0">
				<div class="grid__cell size--6-12@md">
					{include '../inp.latte', class: 'u-mb-sm', form: $form, name: name, labelLang: 'form_label_firstname', placeholderLang: 'form_placeholder_firstname',  validate: true, error: true}
				</div>
				<div class="grid__cell size--6-12@md">
					{include '../inp.latte', class: 'u-mb-sm', form: $form, name: surname, labelLang: 'form_label_surname', placeholderLang: 'form_placeholder_surname',  validate: true, error: true}
				</div>
				<div class="grid__cell size--6-12@md">
					{include '../inp.latte', class: 'u-mb-sm', form: $form, name: email, labelLang: 'form_label_email', placeholderLang: 'form_placeholder_email', validate: true, error: true}
				</div>
				<div class="grid__cell size--6-12@md">
					{include '../inp.latte', class: 'u-mb-sm', form: $form, name: phone, labelLang: 'form_label_phone', type: 'tel', validate: true, error: true}
				</div>
				<div class="grid__cell">
					{include '../inp.latte', class: 'u-mb-sm', form: $form, name: text, labelLang: 'form_label_text', placeholderLang: 'form_placeholder_text', cols: 40, rows: 6, validate: true, error: true}
				</div>
			</div>
		</div>

		<div class="grid grid--middle grid--y-sm">
			<div class="grid__cell size--auto@md u-mb-last-0">
				{include '../inp.latte', class: 'u-mb-xs', form: $form, name: agree, agreeLabel: true, type: 'checkbox', validate: true}
				{include '../inp.latte', class: 'u-mb-xs', form: $form, name: newsletter, type: 'checkbox', labelLang: 'form_label_newsletter', validate: true}
			</div>
			<div class="grid__cell size--autogrow@md">
				<p class="u-mb-0">
					<button type="submit" class="btn btn--lg">
						<span class="btn__text">
							{_"btn_send_arrow"} <span class="btn__arrow u-fw-n">&rarr;</span>
						</span>
					</button>
				</p>
			</div>
		</div>

		{*ANTISPAM*}
		{if isset($form['antispamNoJs'])}
			<p n:class="$form['antispamNoJs']->hasErrors() ? 'has-error' : 'u-js-hide'" data-controller="antispam">
				<label n:name="antispamNoJs">
					{_$form['antispamNoJs']->caption|noescape} {if $form['antispamNoJs']->isRequired()}*{/if}
				</label>
				<span class="inp-fix">
					<input n:name="antispamNoJs" class="inp-text" data-antispam-target="input">
				</span>
			</p>
		{/if}
		{*/ANTISPAM*}

		<div class="block-loader__loader"></div>

	{/form}

{/snippet}
