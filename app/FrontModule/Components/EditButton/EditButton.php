<?php declare(strict_types = 1);

namespace App\FrontModule\Components\EditButton;

use App\Model\Orm\ProductLocalization\ProductLocalization;
use App\Model\Orm\Routable;
use App\PostType\Feature\Model\Orm\FeatureLocalization;
use App\PostType\Material\Model\Orm\MaterialLocalization;
use App\PostType\Page\Model\Orm\CatalogTree;
use App\PostType\Page\Model\Orm\CommonTree;
use App\Model\Orm\User\User;
use App\Model\TranslatorDB;
use App\PostType\Author\Model\Orm\AuthorLocalization;
use App\PostType\Blog\Model\Orm\BlogLocalization;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTagLocalization;
use App\PostType\Reference\Model\Orm\ReferenceLocalization;
use App\PostType\ReferenceTag\Model\Orm\ReferenceTag\ReferenceTagLocalization;
use LogicException;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;

/**
 * @property-read DefaultTemplate $template
 */
final class EditButton extends UI\Control
{

	public function __construct(
		private readonly ?Routable $routable,
		private readonly ?User $user,
		private readonly TranslatorDB $translator,
	)
	{
	}


	public function render(): void
	{
		$this->template->setTranslator($this->translator);
		$this->template->add('showEditButton', $this->routable !== null && $this->user !== null && in_array($this->user->role, [User::ROLE_ADMIN, User::ROLE_DEVELOPER]));
		if ($this->routable !== null) {
			$this->template->add('link', $this->getLink($this->routable));
		}

		$this->template->render(__DIR__ . '/editButton.latte');
	}


	private function getLink(Routable $routable): string
	{
		return match (get_class($routable)) {
			CatalogTree::class, CommonTree::class => $this->presenter->link(':Page:Admin:Page:default', ['id' => $routable->getId()]),
			BlogTagLocalization::class =>  $this->presenter->link(':BlogTag:Admin:BlogTag:edit', ['id' => $routable->getId()]),
			BlogLocalization::class =>  $this->presenter->link(':Blog:Admin:Blog:edit', ['id' => $routable->getId()]),
			FeatureLocalization::class =>  $this->presenter->link(':Feature:Admin:Feature:edit', ['id' => $routable->getId()]),
			MaterialLocalization::class =>  $this->presenter->link(':Material:Admin:Material:edit', ['id' => $routable->getId()]),
			ReferenceTagLocalization::class =>  $this->presenter->link(':ReferenceTag:Admin:ReferenceTag:edit', ['id' => $routable->getId()]),
			ReferenceLocalization::class =>  $this->presenter->link(':Reference:Admin:Reference:edit', ['id' => $routable->getId()]),
			AuthorLocalization::class =>  $this->presenter->link(':Author:Admin:Author:edit', ['id' => $routable->getId()]),
			ProductLocalization::class => $this->getLinkForProductVariant($routable),
			default => throw new LogicException(sprintf("Unknown Routable object class '%s' for EditButton.", get_class($routable)))
		};
	}


	private function getLinkForProductVariant(Routable $routable): string
	{
		assert($routable instanceof ProductLocalization);
		$parent = $routable->getParent();

		return $this->presenter->link(':Admin:Product:edit', ['id' => $parent->id]);
	}

}
