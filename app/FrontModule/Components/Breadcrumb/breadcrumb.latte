{default $class = 'u-mb-sm'}

<nav n:if="count($breadcrumbs) > 0" n:class="m-breadcrumb, $class">
	{define #bc}
		{default $class = false}
		<p n:class="m-breadcrumb__wrap, u-mb-0">
			<strong class="u-vhide">
				{_breadcrumb_title}
			</strong>

			{foreach $breadcrumbs as $key=>$i}
				{if !$iterator->first}
					{('test-chevron-right')|icon, 'm-breadcrumb__separator'}
				{/if}
				{if $iterator->last}
					{if $object instanceOf App\Model\ProductVariant}
						<a href="{plink $i, category: null}" class="m-breadcrumb__link">{$i->nameAnchor}</a>
						{('test-chevron-right')|icon, 'm-breadcrumb__separator'}
						<span class="m-breadcrumb__link">{$object->name}</span>
					{else}
						<span class="m-breadcrumb__link">{$i->nameAnchor}{if isset($_GET[search]) && $iterator->isLast() && $object->uid == 'search'}: {$_GET[search]}{/if}</span>
					{/if}
				{else}
					<a href="{plink $i, category: null}" class="m-breadcrumb__link">{$i->nameAnchor}</a>
				{/if}
			{/foreach}
		</p>
	{/define}


	{* {if $object->template == ':Front:Product:detail' && $variant->mainCategory}
		{include #bc, class=>'u-d-n u-d-b@md'}
		<p class="m-breadcrumb__wrap u-mb-0 u-d-n@md">
			{('angle-left-bold')|icon, 'm-breadcrumb__separator'}
			<a href="{plink $variant->mainCategory}" class="m-breadcrumb__link">{$variant->mainCategory->name}</a>
		</p>
	{else} *}
		{include #bc}
	{* {/if} *}
</nav>
