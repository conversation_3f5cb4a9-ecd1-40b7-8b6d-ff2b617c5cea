<nav n:if="isset($menu) && $menu" class="m-main" id="menu-main">
	{var $mainItems = array_slice($menu, 0, -1)}
	{var $lastItem = $menu[count($menu) - 1]}

	<ul class="m-main__list">
		{foreach $menu as $i => $m}
			{if $i < count($menu) - 1}
				{ifset $m->page->pages}
					{php $hasSubmenu = isset($m->cf->submenu->categories) && count($m->cf->submenu->categories)}
					<li class="m-main__item"
						{if $hasSubmenu}
							data-controller="hover touch-open submenu"
							data-submenu-init-value="lsUp"
						{/if}
					>
						{php $active = ($m->page->uid == 'title' && $m->selected) || ($m->page->uid != 'title' && ($m->active || $m->selected))}
						{if isset($m->cf->settings->nonclickable) && $m->cf->settings->nonclickable}
							<button type="button" n:class="m-main__link, as-link, $active ? is-active"
								{if $hasSubmenu}
									data-action="touch-open#open submenu#toggle"
									data-submenu-target="trigger"
									aria-expanded="false"
								{/if}
							>
								{$m->page->nameAnchor}
							</button>
						{else}
							<a href="{plink $m->page}"
								n:class="m-main__link, $active ? is-active"
								{if $hasSubmenu}
									data-action="touch-open#open submenu#toggle"
									data-submenu-target="trigger"
									aria-expanded="false"
								{/if}
							>
								{$m->page->nameAnchor}
							</a>
						{/if}

						{if $hasSubmenu}
							<button type="button"
								class="m-main__btn as-link"
								data-action="touch-open#open submenu#toggle"
								data-submenu-target="trigger"
								aria-expanded="false"
							>
								{('chevron-down')|icon}
								<span class="u-vhide">{_"show_more"}</span>
							</button>
							{include './submenu.latte', m => $m}
						{/if}
					</li>

				{else}
					<li class="m-main__item">
						<a href="{$m->link}" class="m-main__link" target="_blank">
							{$m->name}
						</a>
					</li>
				{/ifset}
			{/if}
		{/foreach}
	</ul>
</nav>
<nav n:if="isset($menu) && $menu" class="m-lang" id="menu-lang">
	<ul class="m-lang__list">
		<li class="m-lang__item">
			{control toggleLanguage}
		</li>
		<li class="m-lang__item">
			{php $m = $lastItem}
			{php $active = ($m->page->uid == 'title' && $m->selected) || ($m->page->uid != 'title' && $m->active || $m->selected)}
			<a href="{plink $m->page}" n:class="btn, btn--secondary, btn--square, m-lang__btn, $active ? is-active">
				<span class="btn__text">
					{$m->page->nameAnchor}
				</span>
			</a>
		</li>
	</ul>
</nav>
