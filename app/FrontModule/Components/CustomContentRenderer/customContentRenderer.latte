
{var $props = [
	templateDirectory: (isset($props['templateDirectory'])) ? $props['templateDirectory'] : $defaultTemplateDirectory,
	customContent: (isset($props['customContent'])) ? $props['customContent'] : $defaultObjectCC,
]}

{if !Nette\Utils\Strings::endsWith($props['templateDirectory'], '/')}
	{var $props['templateDirectory'] = $props['templateDirectory'].'/'}
{/if}


{var $sameTemplateCounter = 0}
{var $prevItemTemplate = ''}
{foreach $props['customContent'] as $key=>$item}
	{var $item = $item[0]} {*remove first level of group*}
	{var $templateName = substr($key, 0, strpos($key, '____'))}
	{var $niceName = ($allCustomComponentsLabels[$templateName] ?? null) ?? $templateName}

	{if $prevItemTemplate !== $templateName}
		{php $sameTemplateCounter = 0}
		{php $prevItemTemplate = $templateName}
	{else}
		{php $sameTemplateCounter++}
	{/if}

	<div n:tag-if="$isDemo" class="component u-mb-last-0">
		<strong n:if="$isDemo" class="component__title u-font-label">{$niceName}</strong>

		{if $isDev}
			{include $props['templateDirectory'].$templateName.'.latte', customContentItem=>$item, parentIterator=>$iterator, sameTemplateCounter=>$sameTemplateCounter}
		{else}
			{try}
				{include $props['templateDirectory'].$templateName.'.latte', customContentItem=>$item, parentIterator=>$iterator, sameTemplateCounter=>$sameTemplateCounter}
			{/try}
		{/if}
	</div>
{/foreach}
