{block content}
	{php $contactCf = $object->cf->contact_info ?? false}
	{embed $templates.'/part/box/annot.latte', object: $object, bg: 'u-bgc-green'}
		{block annot}
			<p n:if="$contactCf->address ?? false">
				{$contactCf->address|texy|noescape}
			</p>
			<p n:ifcontent>
				{implode('<br>', array_filter([
					($contactCf->ico ?? false) ? $translator->translate("form_label_ic") . ': ' . $contactCf->ico : false,
					($contactCf->dic ?? false) ? $translator->translate("form_label_dic") . ': ' . $contactCf->dic : false,
				]))|noescape}
			</p>
		{/block}
		{block btn}
			<p n:if="$contactCf->mail ?? false">
				<a href="mailto:{$contactCf->mail}" class="b-annot__btn btn">
					<span class="btn__text">
						{$contactCf->mail}
					</span>
				</a>
			</p>
			<p n:if="$contactCf->phone ?? false">
				<a href="tel:{$contactCf->phone|replace:' ',''}" class="b-annot__btn btn">
					<span class="btn__text">
						{$contactCf->phone}
					</span>
				</a>
			</p>
		{/block}
		{block right}
			<div class="b-contact">
				{control contactForm}
			</div>
		{/block}
	{/embed}

	{include $templates.'/part/box/contact-branches.latte'}
{/block}


{* <div class="b-map img img--2-1">
	<div class="img__media is-loading" data-controller="gmap" data-gmap-markers-value='[{"position": {"lat": 49.169766, "lng": 16.628796}}]'></div>
</div> *}
{* {include $templates.'/part/box/content.latte'} *}
