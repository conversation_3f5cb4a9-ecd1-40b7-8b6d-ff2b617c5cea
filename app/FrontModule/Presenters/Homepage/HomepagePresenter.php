<?php declare(strict_types = 1);

namespace App\FrontModule\Presenters\Homepage;

use App\FrontModule\Presenters\BasePresenter;
use App\Model\Orm\Routable;
use App\PostType\Page\Model\Orm\Tree;

final class HomepagePresenter extends BasePresenter
{

	public function __construct()
	{
		parent::__construct();
	}

	public function actionDefault(Tree $object): void
	{
		$this->setObject($this->mutationHolder->getMutation()->rootPage);

	}

	public function renderDefault(): void
	{
		$this->template->isHomepage = true;
	}

}
