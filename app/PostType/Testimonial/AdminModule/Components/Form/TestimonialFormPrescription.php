<?php declare(strict_types=1);

namespace App\PostType\Testimonial\AdminModule\Components\Form;

use App\Model\CustomField\SuggestUrls;
use App\PostType\Testimonial\AdminModule\Components\Form\FormData\TestimonialFormData;
use App\PostType\Testimonial\Model\Orm\TestimonialLocalization;
use App\PostType\Testimonial\Model\Orm\TestimonialLocalizationModel;
use App\PostType\Core\AdminModule\Components\Form\Builder;
use App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\CustomFormExtender;
use App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\Templates\CommonTemplatePart;
use App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\Templates\RelationTemplatePart;
use App\PostType\Core\AdminModule\Components\Form\Definition\FormDefinition;
use App\PostType\Core\AdminModule\Components\Form\Definition\RelationInfoFactory;
use App\PostType\Core\AdminModule\Components\Form\Handler;
use Nette\Application\UI\Form;
use Nette\Http\Request;

class TestimonialFormPrescription
{
	public function __construct(
//		private readonly TestimonialLocalizationModel $testimonialLocalizationModel,
//		private readonly RelationInfoFactory $relationInfoFactory,
//		private readonly Request $request,
//		private readonly Builder $coreBuilder,
//		private readonly Handler $coreHandler,
//		private readonly SuggestUrls $urls,
//		private readonly string $coreFormPath,

	)
	{}

	public function get(TestimonialLocalization $testimonialLocalization): FormDefinition
	{
		$extenders = [];
//		$extenders[] = $this->addTags($testimonialLocalization);
//		$extenders[] = $this->addAuthors($testimonialLocalization);
//		$extenders[] = $this->addCategories($testimonialLocalization);

//		$extenders[] = $this->addIsTop($testimonialLocalization);

		$form = new Form();
		$form->setMappedType(TestimonialFormData::class);

		return new FormDefinition(
			form: $form,
			extenders: $extenders,
		);
	}

//	public function addTags(TestimonialLocalization $testimonialLocalization): CustomFormExtender
//	{
//		$tagsRelationsInfo = $this->relationInfoFactory->create(
//			sourceEntity: $testimonialLocalization->getParent(),
//			propertyName: 'tags',
//			suggestUrl: $this->urls['searchTestimonialTag'],
//		);
//
//		return new CustomFormExtender(
//			addHandler: function (Form $form) use ($tagsRelationsInfo) {
//				$this->coreBuilder->addHasManyRelation($form, $tagsRelationsInfo, $this->request->getPost());
//			},
//			successHandler: function (Form $form, TestimonialFormData $data) use ($tagsRelationsInfo) {
//				$this->coreHandler->handleHasManyRelation($data->{$tagsRelationsInfo->propertyName}, $tagsRelationsInfo);
//			},
//			templateParts: [
//				new RelationTemplatePart(
//					relationInfo: $tagsRelationsInfo,
//					templateFile: $this->coreFormPath . '/parts/relation.latte',
//					type: RelationTemplatePart::TYPE_RELATION),
//				new RelationTemplatePart(
//					relationInfo: $tagsRelationsInfo,
//					templateFile: $this->coreFormPath . '/parts/newItemTemplate.latte',
//					type: RelationTemplatePart::TYPE_RELATION_PRESCRIPTION),
//			]
//		);
//
//	}
//
//	public function addIsTop(TestimonialLocalization $testimonialLocalization): CustomFormExtender
//	{
//		return new CustomFormExtender(
//			function (Form $form) use ($testimonialLocalization) {
//				$form->addCheckbox('isTop', 'Top')->setDefaultValue($testimonialLocalization->isTop);
//			},
//			function (Form $form, TestimonialFormData $data) use ($testimonialLocalization) {
//				$testimonialLocalization->isTop = $data->isTop;
//			},
//			[
//				new CommonTemplatePart(__DIR__ . '/settings.latte',
//					CommonTemplatePart::TYPE_SIDE,
//					['langCodeIsTop' => $testimonialLocalization->mutation->langCode],
//				)
//			]
//		);
//	}

//	private function addAuthors(TestimonialLocalization $testimonialLocalization): CustomFormExtender
//	{
//		$authorsRelationsInfo = $this->relationInfoFactory->create(
//			sourceEntity: $testimonialLocalization->getParent(),
//			propertyName: 'authors',
//			suggestUrl: $this->urls['searchAuthors'],
//		);
//
//		return new CustomFormExtender(
//			addHandler: function (Form $form) use ($authorsRelationsInfo) {
//				$this->coreBuilder->addHasManyRelation($form, $authorsRelationsInfo, $this->request->getPost());
//			},
//			successHandler: function (Form $form, TestimonialFormData $data) use ($authorsRelationsInfo) {
//				$this->coreHandler->handleHasManyRelation($data->{$authorsRelationsInfo->propertyName}, $authorsRelationsInfo);
//			},
//			templateParts: [
//				new RelationTemplatePart(
//					relationInfo: $authorsRelationsInfo,
//					templateFile: $this->coreFormPath . '/parts/relation.latte',
//					type: RelationTemplatePart::TYPE_RELATION),
//				new RelationTemplatePart(
//					relationInfo: $authorsRelationsInfo,
//					templateFile: $this->coreFormPath . '/parts/newItemTemplate.latte',
//					type: RelationTemplatePart::TYPE_RELATION_PRESCRIPTION),
//			]
//		);
//	}

//	private function addCategories(TestimonialLocalization $testimonialLocalization): CustomFormExtender
//	{
//		$url = $this->urls['searchMutationPage'];
//		$url->params['mutationId'] = $testimonialLocalization->mutation->id;
//		$url->params['templates'] = [':Testimonial:Front:Testimonial:default'];
//
//		$categoriesRelationsInfo = $this->relationInfoFactory->create(
//			sourceEntity: $testimonialLocalization,
//			propertyName: 'testimonialLocalizationTrees',
//			suggestUrl: $url,
//			inputSuggestPropertyName: 'name',
//			toggleName: 'categories',
//			dragAndDrop: true,
//			builderCollection: $testimonialLocalization->categories,
//
//		);
//
//		return new CustomFormExtender(
//			addHandler: function (Form $form) use ($categoriesRelationsInfo) {
//				$this->coreBuilder->addHasManyRelation($form, $categoriesRelationsInfo, $this->request->getPost());
//			},
//			successHandler: function (Form $form, TestimonialFormData $data) use ($categoriesRelationsInfo, $testimonialLocalization) {
//				$ids = Handler::readOnlyValidIds($data->{$categoriesRelationsInfo->propertyName});
//				$this->testimonialLocalizationModel->setCategoriesByIds($testimonialLocalization, $ids);
//			},
//			templateParts: [
//				new RelationTemplatePart(
//					relationInfo: $categoriesRelationsInfo,
//					templateFile: $this->coreFormPath . '/parts/relation.latte',
//					type: RelationTemplatePart::TYPE_RELATION),
//				new RelationTemplatePart(
//					relationInfo: $categoriesRelationsInfo,
//					templateFile:$this->coreFormPath . '/parts/newItemTemplate.latte',
//					type: RelationTemplatePart::TYPE_RELATION_PRESCRIPTION),
//			]
//		);
//	}
}
