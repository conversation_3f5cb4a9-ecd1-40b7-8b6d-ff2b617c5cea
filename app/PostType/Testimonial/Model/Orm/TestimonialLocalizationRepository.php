<?php declare(strict_types = 1);

namespace App\PostType\Testimonial\Model\Orm;

use App\Model\Orm\CollectionById;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Repository\QueryForIdsByMutation;
use App\Model\Orm\Searchable;
use App\Model\Orm\Traits\HasPublicParameter;
use App\PostType\Page\Model\Orm\CommonTree;
use Nextras\Dbal\Result\Result;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method TestimonialLocalization getById($id)
 * @method TestimonialLocalization[]|ICollection searchByName(string $q, array $excluded = [])
 * @method TestimonialLocalization[]|ICollection findByIdInPathString(CommonTree $commonTree)
 * @method TestimonialLocalization[]|ICollection findFiltered(array $ids)
 * @method array findAllIds(?int $limit)
 * @method TestimonialLocalization[]|ICollection findByExactOrder(array $ids)
 */
final class TestimonialLocalizationRepository extends Repository implements QueryForIdsByMutation, CollectionById, Searchable
{

	use HasPublicParameter;

	public static function getEntityClassNames(): array
	{
		return [TestimonialLocalization::class];
	}


	public function getPublicOnlyWhereParams(): array
	{
		$ret = [
			'public' => 1,
		];

		$now = $this->getNowDateTime();

		$ret['publicFrom<='] = $now;
		$ret['publicTo>='] = $now;

		return $ret;
	}


	public function findAllIdsInMutation(Mutation $mutation, ?int $limit = null): Result
	{
		$mapper = $this->mapper;
		assert($mapper instanceof TestimonialLocalizationMapper);

		return $mapper->findAllIdsInMutation($mutation, $limit);
	}


	public function findByIdOrder(array $ids): ICollection
	{
		return $this->findByExactOrder($ids);
	}

	public function searchByName(string $string, array $excludedIds = []): ICollection
	{
		return $this->getMapper()->searchByName($string, $excludedIds);
	}


	public function getMapper(): TestimonialLocalizationMapper
	{
		$mapper = parent::getMapper();
		assert($mapper instanceof TestimonialLocalizationMapper);
		return $mapper;
	}

}
