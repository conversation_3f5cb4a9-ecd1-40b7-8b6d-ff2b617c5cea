<?php declare(strict_types = 1);

namespace App\PostType\Testimonial\Model\Orm;

use App\Model\Orm\BaseEntity;
use App\Model\Orm\JsonContainer;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Traits\HasCustomFields;
use App\PostType\Core\Model\ParentEntity;
use Nette\Utils\ArrayHash;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Entity\Entity;
use Nextras\Orm\Exception\NoResultException;
use Nextras\Orm\Relationships\ManyHasMany;
use Nextras\Orm\Relationships\OneHasMany;

/**
 * @property int $id {primary}
 * @property string $internalName {default ''}
 * @property ArrayHash $customFieldsJson {container JsonContainer}
 *
 * RELATIONS
 * @property TestimonialLocalization[]|OneHasMany $localizations {1:M TestimonialLocalization::$testimonial}
// * @property Testimonial[]|ManyHasMany $parentTestimonials {m:m Testimonial::$attachedTestimonials, isMain=true, orderBy=[internalName=ASC]}
 *
 * VIRTUAL
 * @property ArrayHash|null $cf {virtual}
 */
class Testimonial extends BaseEntity implements ParentEntity
{

	use HasCustomFields;

	public function getInternalName(): string
	{
		return $this->internalName;
	}

	public function setInternalName(string $internalName): void
	{
		$this->internalName = $internalName;
	}

	public function getLocalizations(): ICollection
	{
		return $this->localizations->toCollection();
	}

	/**
	 * @throws NoResultException
	 */
	public function getLocalization(Mutation $mutation): TestimonialLocalization
	{
		$localization = $this->getLocalizations()->getByChecked(['mutation' => $mutation]);
		assert($localization instanceof TestimonialLocalization);
		return $localization;
	}

}
