<?php declare(strict_types = 1);

namespace App\PostType\Material\Model;

use App\PostType\Material\Model\Orm\Material;
use App\PostType\Material\Model\Orm\MaterialLocalization;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\PostType\Core\Model\EntityLocalizationFacade;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Core\Model\ParentEntity;

final class MaterialLocalizationFacade implements EntityLocalizationFacade
{

	public function __construct(
		private Orm $orm,
	)
	{
	}


	public function create(Mutation $mutation, ParentEntity|null $localizableEntity): MaterialLocalization
	{
		$materialLocalization = new MaterialLocalization();
		$this->orm->materialLocalization->attach($materialLocalization);
		$materialLocalization->mutation = $mutation;

		if ($localizableEntity === null) {
			$localizableEntity = new Material();
			$materialLocalization->material = $localizableEntity;
		} else {
			assert($localizableEntity instanceof Material);
			$materialLocalization->material = $localizableEntity;
		}

		$this->orm->persistAndFlush($materialLocalization);

		return $materialLocalization;
	}


	public function remove(LocalizationEntity $localizableEntity): void
	{
		assert($localizableEntity instanceof MaterialLocalization);

		$parent = $localizableEntity->getParent();
		assert($parent instanceof Material);
		$this->orm->materialLocalization->remove($localizableEntity);

		if ($parent->getLocalizations()->count() === 0) {
			$this->orm->material->remove($parent);
		}

		$this->orm->flush();
	}

}
