<?php declare(strict_types = 1);

namespace App\PostType\Material\Model\Orm;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Repository\QueryForIdsByMutation;
use App\Model\Orm\Traits\HasPublicParameter;
use App\PostType\Page\Model\Orm\CommonTree;
use Nextras\Dbal\Result\Result;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method MaterialLocalization getById($id)
 * @method MaterialLocalization[]|ICollection searchByName(string $q, array $excluded = [])
 * @method MaterialLocalization[]|ICollection findByIdInPathString(CommonTree $commonTree)
 * @method MaterialLocalization[]|ICollection findFiltered(array $ids)
 * @method array findAllIds(?int $limit)
 */
final class MaterialLocalizationRepository extends Repository implements QueryForIdsByMutation
{

	use HasPublicParameter;

	public static function getEntityClassNames(): array
	{
		return [MaterialLocalization::class];
	}


	public function getPublicOnlyWhereParams(): array
	{
		$ret = [
			'public' => 1,
		];

		$now = $this->getNowDateTime();

		$ret['publicFrom<='] = $now;
		$ret['publicTo>='] = $now;

		return $ret;
	}


	public function findAllIdsInMutation(Mutation $mutation, ?int $limit = null): Result
	{
		$mapper = $this->mapper;
		assert($mapper instanceof MaterialLocalizationMapper);

		return $mapper->findAllIdsInMutation($mutation, $limit);
	}

}
