<?php declare(strict_types = 1);

namespace App\PostType\Material\Model\Orm;

use App\Model\Orm\HasImages;
use App\Model\Orm\ImageEntity;
use App\Model\Orm\JsonContainer;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\RoutableEntity;
use App\Model\Orm\Traits\HasCustomContent;
use App\Model\Orm\Traits\HasCustomFields;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTagLocalization;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTagLocalizationRepository;
use App\PostType\Core\Model\Editable;
use App\PostType\Core\Model\ParentEntity;
use App\PostType\Page\Model\Orm\Tree;
use App\PostType\Page\Model\Orm\TreeRepository;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Core\Model\Publishable;
use App\PostType\Core\Model\Validatable;
use Nette\Utils\ArrayHash;
use Nette\Utils\Strings;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Collection\EmptyCollection;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Relationships\OneHasMany;
use stdClass;

/**
 * @property int $id {primary}
 * @property string $name  {default ''}
 * @property bool $public {default false}
 * @property bool $isTop {default false}
 * @property DateTimeImmutable|null $publicFrom {default now}
 * @property DateTimeImmutable|null $publicTo {default '+100 year'}
 * @property DateTimeImmutable|null $editedTime
 * @property int|null $edited
 *
 * @property ArrayHash $customFieldsJson {container JsonContainer}
 * @property ArrayHash $customContentJson {container JsonContainer}
 * @property int $viewsNumber {default 0}
 *
 * RELATIONS
 * @property Mutation $mutation {m:1 Mutation, oneSided=true}
 * @property Material $material {M:1 Material::$localizations}
 *
 * VIRTUAL
 * @property ArrayHash|null $cf {virtual}
 * @property ArrayHash|null $cc {virtual}
 * @property-read string $template {virtual}
 * @property-read  MaterialLocalization[]|ICollection $attachedMaterials {virtual}
 * @property-read  string $annotation {virtual}
 * @property-read  BlogTagLocalization[]|ICollection $materialTags {virtual}
 * @property int $readingTime {virtual}
 */
class MaterialLocalization extends RoutableEntity implements LocalizationEntity, Publishable, Validatable, HasImages, Editable
{

	use HasCustomFields;
	use HasCustomContent;



	private BlogTagLocalizationRepository $blogTagLocalizationRepository;


	public function injectRepository(
		BlogTagLocalizationRepository $blogTagLocalizationRepository
	): void
	{
		$this->blogTagLocalizationRepository = $blogTagLocalizationRepository;
	}


	protected function getterMaterialTags(): ICollection
	{
		$tagIds = $this->material->tags->toCollection()->fetchPairs(null, 'id');
		return $this->blogTagLocalizationRepository
			->findBy([
				'blogTag->id' => $tagIds,
				'mutation' => $this->mutation,
			]);
	}

	protected function getterTemplate(): string
	{
		return ':Material:Front:Material:detail';
	}

	protected function getterPath(): array
	{
//		$categoryIds = $this->materialLocalizationTrees->toCollection()->fetchPairs(null, 'tree->id');
//		if ($categoryIds === []) {
//			return [];
//		}

		$path = [];
//		$mainCategory = $this->treeRepository->findFilteredPages($categoryIds)->findBy(['rootId' => $this->getMutation()->id])->fetch();
//
//		if ($mainCategory !== null) {
//			$mainPath = $mainCategory->path;
//			if ($mainPath !== null) {
//				$path = $mainPath;
//				$path[] = $mainCategory->id;
//			}
//		}

		return $path;
	}



	protected function getterAttachedMaterials(): ICollection
	{
		$repository = $this->getRepository();
		assert($repository instanceof MaterialLocalizationRepository);

		$attachedMaterialIds = $this->material->attachedMaterials->toCollection()->fetchPairs(null, 'id');
		return $repository
			->findBy([
				'id' => $attachedMaterialIds,
				'mutation' => $this->mutation,
			]);
	}


	protected function getterAnnotation(): string
	{
		$annotation = '';
		if (isset($this->cf->annotationDetail) && $this->cf->annotationDetail) {
			$annotation = $this->cf->annotationDetail;
		} elseif (isset($this->cf->annotation) && $this->cf->annotation) {
			$annotation = $this->cf->annotation;
		}

		return $annotation;
	}

	protected function getterReadingTime(): int
	{
		$words_per_minute = 220;
		$words_per_second = $words_per_minute / 60;
		$content = '';

		if ($this->cc !== null) {
			foreach ($this->cc as $key => $c) {
				if (isset($c[0]->content) && Strings::startsWith($key, 'content_')) {
					$content .= ' ' . $c[0]->content;
				}
			}
		}

		// $word_count = count(explode(" ", strip_tags($content)));
		$word_count = str_word_count(strip_tags($content));

		// How many seconds (total)?
		$seconds = ceil($word_count / $words_per_second);
		$minutes = ceil($seconds / 60);

		return (int) $minutes;
	}

	public function getEsContent(): string
	{
		$content = '';
		foreach ($this->cc as $item) {
			foreach ($item as $parts) {
				if ($parts instanceof stdClass) {
					foreach ((array) $parts as $part) {
						if (is_string($part)) {
							$content .= ' ' . strip_tags($part);
						}
					}
				}
			}
		}

		return $content;
	}

	public function getId(): int
	{
		return $this->id;
	}

	public function getMutation(): Mutation
	{
		return $this->mutation;
	}


	public function setMutation(Mutation $mutation): void
	{
		$this->mutation = $mutation;
	}


	public function getParent(): Material
	{
		return $this->material;
	}


	public function setParent(ParentEntity $parentEntity): void
	{
		assert($parentEntity instanceof Material);
		$this->material = $parentEntity;
	}


	public function getIsPublic(): bool
	{
		return $this->public;
	}

	public function setIsPublic(bool $isPublic): void
	{
		$this->public = $isPublic;
	}

	public function getPublicFrom(): DateTimeImmutable|null
	{
		return $this->publicFrom;
	}

	public function getPublicTo(): DateTimeImmutable|null
	{
		return $this->publicTo;
	}

	public function setPublicFrom(?DateTimeImmutable $publicFrom): void
	{
		$this->publicFrom = $publicFrom;
	}

	public function setPublicTo(?DateTimeImmutable $publicTo): void
	{
		$this->publicTo = $publicTo;
	}

	public function getName(): string
	{
		return $this->name;
	}

	public function setName(string $name): void
	{
		$this->name = $name;
	}

	public function getFirstImage(): ImageEntity|null
	{
		return isset($this->cf->base->mainImage) ? $this->cf->base->mainImage->getEntity() : null;
	}


	public function setEditorId(int $id): void
	{
		$this->edited = $id;
	}

	public function setEditedTime(DateTimeImmutable $editedTime): void
	{
		$this->editedTime = $editedTime;
	}
}
