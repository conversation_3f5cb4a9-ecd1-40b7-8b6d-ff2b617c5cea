<?php declare(strict_types = 1);

namespace App\PostType\Material\Model\Orm;

use App\Model\Orm\Traits\HasCamelCase;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

class MaterialMapper extends DbalMapper
{

	use HasCamelCase;

	protected $tableName = 'material';

	public function searchByName(string $q, array $excluded = []): ICollection
	{
		$builder = $this->builder()
			->andWhere('internalName LIKE %_like_', $q);

		if ($excluded !== []) {
			$builder->andWhere('id not in %i[]', $excluded);
		}

		return $this->toCollection($builder);
	}

}
