<?php declare(strict_types = 1);

namespace App\PostType\Material\Model\Orm;

use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method Material getById($id)
 * @method Material[]|ICollection searchByName(string $q, array $excluded = [])
 */
final class MaterialRepository extends Repository
{

	public static function getEntityClassNames(): array
	{
		return [Material::class];
	}

}
