<?php declare(strict_types = 1);

namespace App\PostType\Material\Model\Orm;

use Jaybizzle\CrawlerDetect\CrawlerDetect;

class MaterialLocalizationModel
{

	public function __construct(
		private readonly MaterialLocalizationRepository $materialLocalizationRepository,
	)
	{}


	public function increaseViews(MaterialLocalization $materialLocalization): bool
	{
		$crawlerDetect = new CrawlerDetect();
		if ($crawlerDetect->isCrawler()) {
			return false;
		}

		$materialLocalization->viewsNumber++;
		$this->materialLocalizationRepository->persistAndFlush($materialLocalization);
		return true;
	}

}
