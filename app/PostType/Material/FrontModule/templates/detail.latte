{varType App\PostType\Material\Model\Orm\MaterialLocalization $object}
{default $material = $object->material->cf->base ?? false}

{block content}
	{include $templates.'/part/box/annot.latte', context: 'material'}

	<div class="u-mb-last-0 u-mb-lg u-mb-2xl@md">
		<div class="row-main">
			{default $highlights = $material ? $material->highlights ?? false : false}
			{if $highlights}
				{include $templates.'/part/box/highlights.latte', title: $highlights->title ?? false, items: $highlights->items ?? []}
			{/if}
			
			{default $hasForm = $material ? $material->content->showForm ?? false : false}
			<div class="grid grid--x-lg">
				<div n:if="$material ? $material->content->content ?? false : false" n:class="$hasForm ? 'size--6-12@lg' : 'size--12-12', grid__cell">
					{$material ? $material->content->content ?? false : false|noescape}
				</div>
				{if $hasForm}
					<div class="size--6-12@lg grid__cell">
						{include $templates.'/part/box/contact.latte', class: 'u-pt-xl u-bgc-white'}
					</div>
				{/if}
			</div>
		</div>
		
		{control customContentRenderer}
	</div>
{/block}
