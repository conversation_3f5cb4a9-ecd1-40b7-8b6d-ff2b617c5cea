<?php declare(strict_types = 1);

namespace App\PostType\Material\FrontModule\Components\MaterialLocalizationStructuredData;

use App\PostType\Material\Model\Orm\MaterialLocalization;
use App\Model\Orm\Mutation\Mutation;
use Nette\Application\UI\Control;
use App\Model\TranslatorDB;

class MaterialLocalizationStructuredData extends Control
{

	public function __construct(
		private MaterialLocalization $materialLocalization,
		private Mutation $mutation,
		private TranslatorDB $translator
	)
	{
	}


	public function render(): void
	{
		$this->template->setTranslator($this->translator);

		$this->template->materialLocalization = $this->materialLocalization;
		$this->template->mutation = $this->mutation;
		$this->template->templates = FE_TEMPLATE_DIR;

		$this->template->content = (isset($this->materialLocalization->cf->base->annotation)) ? $this->materialLocalization->cf->base->annotation : '';
		$this->template->publisher = 'superadmin';


		$this->template->render(__DIR__ . '/materialLocalizationStructuredData.latte');
	}

}
