<?php declare(strict_types = 1);

namespace App\PostType\Material\FrontModule\Components\Attached;

use App\Model\TranslatorDB;
use App\PostType\Material\Model\Orm\Material;
use Nette\Application\UI\Control;
use Nextras\Orm\Collection\EmptyCollection;
use Nextras\Orm\Collection\ICollection;

class AttachedMaterials extends Control
{

	public function __construct(
		private Material $object,
		private TranslatorDB $translator
	)
	{
	}


	public function render(): void
	{
		$this->template->setTranslator($this->translator);

		$this->template->object = $this->object;
		$this->template->materials = $this->getMaterials();
		$this->template->templates = FE_TEMPLATE_DIR;

		$this->template->render(__DIR__ . '/attachedMaterials.latte');
	}


	protected function getMaterials(): ICollection
	{
//		TODO FIX @vojta

//		$withoutMaterialIds = [$this->object->id];
//
//		$materials = $this->object->attachedMaterials->toCollection()->findBy([])->limitBy($this->limit)->fetchPairs('id');
//		$withoutMaterialIds = array_merge($withoutMaterialIds, array_keys($materials));
//
//
//		if (count($materials) < $this->limit) { // ostatni clanky tagu
//			foreach ($this->object->materialTagsPublic as $tag) {
//				$materials += $tag->materialsPublic->findBy(['id!=' => $withoutMaterialIds])->fetchPairs('id');
//			}
//		}
//
//		if (count($materials) < $this->limit) { // ostatni clanky kategorii
//			foreach ($this->object->categoriesPublic as $category) {
//				$materials += $category->materialsPublic->findBy(['id!=' => $withoutMaterialIds])->fetchPairs('id');
//			}
//		}
//
//		$materials = array_slice($materials, 0, $this->limit, true);
//		return new ArrayIterator($materials);
		return new EmptyCollection();
	}

}
