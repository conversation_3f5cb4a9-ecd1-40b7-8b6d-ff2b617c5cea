<?php declare(strict_types=1);

namespace App\PostType\Reference\AdminModule\Components\Form;

use App\Model\CustomField\SuggestUrls;
use App\PostType\Reference\AdminModule\Components\Form\FormData\ReferenceFormData;
use App\PostType\Reference\Model\Orm\ReferenceLocalization;
use App\PostType\Reference\Model\Orm\ReferenceLocalizationModel;
use App\PostType\Core\AdminModule\Components\Form\Builder;
use App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\CustomFormExtender;
use App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\Templates\CommonTemplatePart;
use App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\Templates\RelationTemplatePart;
use App\PostType\Core\AdminModule\Components\Form\Definition\FormDefinition;
use App\PostType\Core\AdminModule\Components\Form\Definition\RelationInfoFactory;
use App\PostType\Core\AdminModule\Components\Form\Handler;
use Nette\Application\UI\Form;
use Nette\Http\Request;

class ReferenceFormPrescription
{
	public function __construct(
		private readonly ReferenceLocalizationModel $referenceLocalizationModel,
		private readonly RelationInfoFactory $relationInfoFactory,
		private readonly Request $request,
		private readonly Builder $coreBuilder,
		private readonly Handler $coreHandler,
		private readonly SuggestUrls $urls,
		private readonly string $coreFormPath,

	)
	{}

	public function get(ReferenceLocalization $referenceLocalization): FormDefinition
	{
		$extenders = [];
		$extenders[] = $this->addTags($referenceLocalization);
//		$extenders[] = $this->addAuthors($referenceLocalization);
//		$extenders[] = $this->addCategories($referenceLocalization);

		$extenders[] = $this->addIsTop($referenceLocalization);

		$form = new Form();
		$form->setMappedType(ReferenceFormData::class);

		return new FormDefinition(
			form: $form,
			extenders: $extenders,
		);
	}

	public function addTags(ReferenceLocalization $referenceLocalization): CustomFormExtender
	{
		$tagsRelationsInfo = $this->relationInfoFactory->create(
			sourceEntity: $referenceLocalization->getParent(),
			propertyName: 'tags',
			suggestUrl: $this->urls['searchReferenceTag'],
		);

		return new CustomFormExtender(
			addHandler: function (Form $form) use ($tagsRelationsInfo) {
				$this->coreBuilder->addHasManyRelation($form, $tagsRelationsInfo, $this->request->getPost());
			},
			successHandler: function (Form $form, ReferenceFormData $data) use ($tagsRelationsInfo) {
				$this->coreHandler->handleHasManyRelation($data->{$tagsRelationsInfo->propertyName}, $tagsRelationsInfo);
			},
			templateParts: [
				new RelationTemplatePart(
					relationInfo: $tagsRelationsInfo,
					templateFile: $this->coreFormPath . '/parts/relation.latte',
					type: RelationTemplatePart::TYPE_RELATION),
				new RelationTemplatePart(
					relationInfo: $tagsRelationsInfo,
					templateFile: $this->coreFormPath . '/parts/newItemTemplate.latte',
					type: RelationTemplatePart::TYPE_RELATION_PRESCRIPTION),
			]
		);

	}

	public function addIsTop(ReferenceLocalization $referenceLocalization): CustomFormExtender
	{
		return new CustomFormExtender(
			function (Form $form) use ($referenceLocalization) {
				$form->addCheckbox('isTop', 'Top')->setDefaultValue($referenceLocalization->isTop);
			},
			function (Form $form, ReferenceFormData $data) use ($referenceLocalization) {
				$referenceLocalization->isTop = $data->isTop;
			},
			[
				new CommonTemplatePart(__DIR__ . '/settings.latte',
					CommonTemplatePart::TYPE_SIDE,
					['langCodeIsTop' => $referenceLocalization->mutation->langCode],
				)
			]
		);
	}

//	private function addAuthors(ReferenceLocalization $referenceLocalization): CustomFormExtender
//	{
//		$authorsRelationsInfo = $this->relationInfoFactory->create(
//			sourceEntity: $referenceLocalization->getParent(),
//			propertyName: 'authors',
//			suggestUrl: $this->urls['searchAuthors'],
//		);
//
//		return new CustomFormExtender(
//			addHandler: function (Form $form) use ($authorsRelationsInfo) {
//				$this->coreBuilder->addHasManyRelation($form, $authorsRelationsInfo, $this->request->getPost());
//			},
//			successHandler: function (Form $form, ReferenceFormData $data) use ($authorsRelationsInfo) {
//				$this->coreHandler->handleHasManyRelation($data->{$authorsRelationsInfo->propertyName}, $authorsRelationsInfo);
//			},
//			templateParts: [
//				new RelationTemplatePart(
//					relationInfo: $authorsRelationsInfo,
//					templateFile: $this->coreFormPath . '/parts/relation.latte',
//					type: RelationTemplatePart::TYPE_RELATION),
//				new RelationTemplatePart(
//					relationInfo: $authorsRelationsInfo,
//					templateFile: $this->coreFormPath . '/parts/newItemTemplate.latte',
//					type: RelationTemplatePart::TYPE_RELATION_PRESCRIPTION),
//			]
//		);
//	}

//	private function addCategories(ReferenceLocalization $referenceLocalization): CustomFormExtender
//	{
//		$url = $this->urls['searchMutationPage'];
//		$url->params['mutationId'] = $referenceLocalization->mutation->id;
//		$url->params['templates'] = [':Reference:Front:Reference:default'];
//
//		$categoriesRelationsInfo = $this->relationInfoFactory->create(
//			sourceEntity: $referenceLocalization,
//			propertyName: 'referenceLocalizationTrees',
//			suggestUrl: $url,
//			inputSuggestPropertyName: 'name',
//			toggleName: 'categories',
//			dragAndDrop: true,
//			builderCollection: $referenceLocalization->categories,
//
//		);
//
//		return new CustomFormExtender(
//			addHandler: function (Form $form) use ($categoriesRelationsInfo) {
//				$this->coreBuilder->addHasManyRelation($form, $categoriesRelationsInfo, $this->request->getPost());
//			},
//			successHandler: function (Form $form, ReferenceFormData $data) use ($categoriesRelationsInfo, $referenceLocalization) {
//				$ids = Handler::readOnlyValidIds($data->{$categoriesRelationsInfo->propertyName});
//				$this->referenceLocalizationModel->setCategoriesByIds($referenceLocalization, $ids);
//			},
//			templateParts: [
//				new RelationTemplatePart(
//					relationInfo: $categoriesRelationsInfo,
//					templateFile: $this->coreFormPath . '/parts/relation.latte',
//					type: RelationTemplatePart::TYPE_RELATION),
//				new RelationTemplatePart(
//					relationInfo: $categoriesRelationsInfo,
//					templateFile:$this->coreFormPath . '/parts/newItemTemplate.latte',
//					type: RelationTemplatePart::TYPE_RELATION_PRESCRIPTION),
//			]
//		);
//	}
}
