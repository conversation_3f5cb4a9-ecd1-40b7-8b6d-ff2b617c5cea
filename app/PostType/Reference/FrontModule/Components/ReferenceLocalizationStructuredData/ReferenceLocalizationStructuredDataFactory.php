<?php declare(strict_types = 1);

namespace App\PostType\Reference\FrontModule\Components\ReferenceLocalizationStructuredData;

use App\PostType\Reference\Model\Orm\ReferenceLocalization;
use App\Model\Orm\Mutation\Mutation;

interface ReferenceLocalizationStructuredDataFactory
{

	public function create(
		ReferenceLocalization $referenceLocalization,
		Mutation $mutation,
	): ReferenceLocalizationStructuredData;

}


