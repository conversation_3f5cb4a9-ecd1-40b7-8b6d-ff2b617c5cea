<?php declare(strict_types = 1);

namespace App\PostType\Reference\FrontModule\Components\ReferenceLocalizationStructuredData;

use App\PostType\Reference\Model\Orm\ReferenceLocalization;
use App\Model\Orm\Mutation\Mutation;
use Nette\Application\UI\Control;
use App\Model\TranslatorDB;

class ReferenceLocalizationStructuredData extends Control
{

	public function __construct(
		private ReferenceLocalization $referenceLocalization,
		private Mutation $mutation,
		private TranslatorDB $translator
	)
	{
	}


	public function render(): void
	{
		$this->template->setTranslator($this->translator);

		$this->template->referenceLocalization = $this->referenceLocalization;
		$this->template->mutation = $this->mutation;
		$this->template->templates = FE_TEMPLATE_DIR;

		$this->template->content = (isset($this->referenceLocalization->cf->base->annotation)) ? $this->referenceLocalization->cf->base->annotation : '';
		$this->template->publisher = 'superadmin';


		$this->template->render(__DIR__ . '/referenceLocalizationStructuredData.latte');
	}

}
