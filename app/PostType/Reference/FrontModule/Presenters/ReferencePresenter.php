<?php declare(strict_types = 1);

namespace App\PostType\Reference\FrontModule\Presenters;

use App\FrontModule\Components\CustomContentRenderer\HasCustomContentRenderer;
use App\FrontModule\Presenters\BasePresenter;
use App\PostType\BlogTag\Model\BlogTagModel;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTag;
use App\PostType\Reference\FrontModule\Components\Attached\AttachedReferences;
use App\PostType\Reference\FrontModule\Components\Attached\AttachedReferencesFactory;
use App\PostType\Reference\Model\Orm\Reference;
use App\PostType\Reference\Model\Orm\ReferenceLocalization;
use App\PostType\Reference\Model\Orm\ReferenceLocalizationModel;
use App\PostType\Page\Model\Orm\CommonTree;
use App\PostType\Reference\FrontModule\Components\ReferenceLocalizationStructuredData\ReferenceLocalizationStructuredData;
use App\PostType\Reference\FrontModule\Components\ReferenceLocalizationStructuredData\ReferenceLocalizationStructuredDataFactory;

/**
 * @method Reference getObject()
 */
final class ReferencePresenter extends BasePresenter
{

	use HasCustomContentRenderer;

	private ReferenceLocalization $referenceLocalization;

	public function __construct(
		private AttachedReferencesFactory $attachedReferencesFactory,
		private ReferenceLocalizationModel $referenceLocalizationModel,
		private ReferenceLocalizationStructuredDataFactory $referenceLocalizationStructuredDataFactory,
		private BlogTagModel $blogTagModel,

	)
	{
		parent::__construct();
	}

	public function startup(): void
	{
		parent::startup();
	}


	public function actionDefault(CommonTree $object): void
	{
		$this->setObject($object);
	}


	public function renderDefault(CommonTree $object): void
	{
		$this->addComponent($this->visualPaginatorFactory->create(), 'pager');
		$this['pager']->object = $object;
		$this['pager']->special = true;

		$paginator = $this['pager']->getPaginator();
		$paginator->itemsPerPage = $this->configService->get('reference', 'paging');

		$referenceLocalizations = $this->orm->referenceLocalization->findBy(['mutation' => $this->mutation])->orderBy('publicFrom');

		$totalCount = $referenceLocalizations->countStored();
		$paginator->itemCount = $totalCount;

		$this->template->tagsWithCount = $this->blogTagModel->getTagsWithCount($this->mutation, BlogTag::TYPE_REFERENCE);
		$this->template->totalCount = $totalCount;
		$this->template->referenceLocalizations = $referenceLocalizations->limitBy($paginator->itemsPerPage, $paginator->offset);

		if ($this->isAjax()) {
			if ($this['pager']->getParameter('more')) {
				$this->redrawControl('articlesInner');
				$this->redrawControl('articlesPagerBottom');
				$this->redrawControl('articleList');
			} else {
				if (!$this->getSignal()) {
					$this->redrawControl();
				}
			}
		}
	}

	public function actionDetail(ReferenceLocalization $object): void
	{
		$this->setObject($object);
		$this->referenceLocalization = $object;
	}


	public function renderDetail(): void
	{
		$this->referenceLocalizationModel->increaseViews($this->referenceLocalization);

		$this->template->referenceLocalization = $this->referenceLocalization;
		$this->template->tagsWithCount = $this->blogTagModel->getTagsWithCount($this->mutation, BlogTag::TYPE_REFERENCE);
	}

	public function beforeRender(): void
	{
		parent::beforeRender();
		$this->template->setFile(__DIR__ . '/../templates/' . $this->getAction() . '.latte');
	}





	protected function createComponentAttachedReferences(): AttachedReferences
	{
		return $this->attachedReferencesFactory->create($this->referenceLocalization->reference);
	}

	protected function createComponentReferenceLocalizationStructuredData(): ReferenceLocalizationStructuredData
	{
		return $this->referenceLocalizationStructuredDataFactory->create($this->referenceLocalization, $this->mutation);
	}

}
