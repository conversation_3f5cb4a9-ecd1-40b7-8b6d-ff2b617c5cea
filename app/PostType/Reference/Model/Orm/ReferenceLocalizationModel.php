<?php declare(strict_types = 1);

namespace App\PostType\Reference\Model\Orm;

use Jaybizzle\CrawlerDetect\CrawlerDetect;

class ReferenceLocalizationModel
{

	public function __construct(
		private readonly ReferenceLocalizationRepository $referenceLocalizationRepository,
	)
	{}


	public function increaseViews(ReferenceLocalization $referenceLocalization): bool
	{
		$crawlerDetect = new CrawlerDetect();
		if ($crawlerDetect->isCrawler()) {
			return false;
		}

		$referenceLocalization->viewsNumber++;
		$this->referenceLocalizationRepository->persistAndFlush($referenceLocalization);
		return true;
	}

}
