<?php declare(strict_types=1);

namespace App\PostType\Feature\AdminModule\Components\Form;

use App\Model\CustomField\SuggestUrls;
use App\PostType\Feature\AdminModule\Components\Form\FormData\FeatureFormData;
use App\PostType\Feature\Model\Orm\FeatureLocalization;
use App\PostType\Feature\Model\Orm\FeatureLocalizationModel;
use App\PostType\Core\AdminModule\Components\Form\Builder;
use App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\CustomFormExtender;
use App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\Templates\CommonTemplatePart;
use App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\Templates\RelationTemplatePart;
use App\PostType\Core\AdminModule\Components\Form\Definition\FormDefinition;
use App\PostType\Core\AdminModule\Components\Form\Definition\RelationInfoFactory;
use App\PostType\Core\AdminModule\Components\Form\Handler;
use Nette\Application\UI\Form;
use Nette\Http\Request;

class FeatureFormPrescription
{
	public function __construct(
		private readonly FeatureLocalizationModel $featureLocalizationModel,
		private readonly RelationInfoFactory $relationInfoFactory,
		private readonly Request $request,
		private readonly Builder $coreBuilder,
		private readonly Handler $coreHandler,
		private readonly SuggestUrls $urls,
		private readonly string $coreFormPath,

	)
	{}

	public function get(FeatureLocalization $featureLocalization): FormDefinition
	{
		$extenders = [];
//		$extenders[] = $this->addTags($featureLocalization);
//		$extenders[] = $this->addAuthors($featureLocalization);
		$extenders[] = $this->addCategories($featureLocalization);

//		$extenders[] = $this->addIsTop($featureLocalization);

		$form = new Form();
		$form->setMappedType(FeatureFormData::class);

		return new FormDefinition(
			form: $form,
			extenders: $extenders,
		);
	}

	public function addTags(FeatureLocalization $featureLocalization): CustomFormExtender
	{
		$tagsRelationsInfo = $this->relationInfoFactory->create(
			sourceEntity: $featureLocalization->getParent(),
			propertyName: 'tags',
			suggestUrl: $this->urls['searchFeatureTag'],
		);

		return new CustomFormExtender(
			addHandler: function (Form $form) use ($tagsRelationsInfo) {
				$this->coreBuilder->addHasManyRelation($form, $tagsRelationsInfo, $this->request->getPost());
			},
			successHandler: function (Form $form, FeatureFormData $data) use ($tagsRelationsInfo) {
				$this->coreHandler->handleHasManyRelation($data->{$tagsRelationsInfo->propertyName}, $tagsRelationsInfo);
			},
			templateParts: [
				new RelationTemplatePart(
					relationInfo: $tagsRelationsInfo,
					templateFile: $this->coreFormPath . '/parts/relation.latte',
					type: RelationTemplatePart::TYPE_RELATION),
				new RelationTemplatePart(
					relationInfo: $tagsRelationsInfo,
					templateFile: $this->coreFormPath . '/parts/newItemTemplate.latte',
					type: RelationTemplatePart::TYPE_RELATION_PRESCRIPTION),
			]
		);

	}

//	public function addIsTop(FeatureLocalization $featureLocalization): CustomFormExtender
//	{
//		return new CustomFormExtender(
//			function (Form $form) use ($featureLocalization) {
//				$form->addCheckbox('isTop', 'Top')->setDefaultValue($featureLocalization->isTop);
//			},
//			function (Form $form, FeatureFormData $data) use ($featureLocalization) {
//				$featureLocalization->isTop = $data->isTop;
//			},
//			[
//				new CommonTemplatePart(__DIR__ . '/settings.latte',
//					CommonTemplatePart::TYPE_SIDE,
//					['langCodeIsTop' => $featureLocalization->mutation->langCode],
//				)
//			]
//		);
//	}

//	private function addAuthors(FeatureLocalization $featureLocalization): CustomFormExtender
//	{
//		$authorsRelationsInfo = $this->relationInfoFactory->create(
//			sourceEntity: $featureLocalization->getParent(),
//			propertyName: 'authors',
//			suggestUrl: $this->urls['searchAuthors'],
//		);
//
//		return new CustomFormExtender(
//			addHandler: function (Form $form) use ($authorsRelationsInfo) {
//				$this->coreBuilder->addHasManyRelation($form, $authorsRelationsInfo, $this->request->getPost());
//			},
//			successHandler: function (Form $form, FeatureFormData $data) use ($authorsRelationsInfo) {
//				$this->coreHandler->handleHasManyRelation($data->{$authorsRelationsInfo->propertyName}, $authorsRelationsInfo);
//			},
//			templateParts: [
//				new RelationTemplatePart(
//					relationInfo: $authorsRelationsInfo,
//					templateFile: $this->coreFormPath . '/parts/relation.latte',
//					type: RelationTemplatePart::TYPE_RELATION),
//				new RelationTemplatePart(
//					relationInfo: $authorsRelationsInfo,
//					templateFile: $this->coreFormPath . '/parts/newItemTemplate.latte',
//					type: RelationTemplatePart::TYPE_RELATION_PRESCRIPTION),
//			]
//		);
//	}

	private function addCategories(FeatureLocalization $featureLocalization): CustomFormExtender
	{
		$url = $this->urls['searchMutationPage'];
		$url->params['mutationId'] = $featureLocalization->mutation->id;
//		$url->params['templates'] = [':Feature:Front:Feature:default'];
		$url->params['templates'] = [':Front:Page:servicesCategory', ':Front:Page:servicesLP', ':Feature:Front:Feature:servicesMain', ':Feature:Front:Feature:servicesCategory', ':Feature:Front:Feature:servicesLP'];

		$categoriesRelationsInfo = $this->relationInfoFactory->create(
			sourceEntity: $featureLocalization,
			propertyName: 'featureLocalizationTrees',
			suggestUrl: $url,
			inputSuggestPropertyName: 'name',
			toggleName: 'categories',
			dragAndDrop: true,
			builderCollection: $featureLocalization->categories,

		);

		return new CustomFormExtender(
			addHandler: function (Form $form) use ($categoriesRelationsInfo) {
				$this->coreBuilder->addHasManyRelation($form, $categoriesRelationsInfo, $this->request->getPost());
			},
			successHandler: function (Form $form, FeatureFormData $data) use ($categoriesRelationsInfo, $featureLocalization) {
				$ids = Handler::readOnlyValidIds($data->{$categoriesRelationsInfo->propertyName});
				$this->featureLocalizationModel->setCategoriesByIds($featureLocalization, $ids);
			},
			templateParts: [
				new RelationTemplatePart(
					relationInfo: $categoriesRelationsInfo,
					templateFile: $this->coreFormPath . '/parts/relation.latte',
					type: RelationTemplatePart::TYPE_RELATION),
				new RelationTemplatePart(
					relationInfo: $categoriesRelationsInfo,
					templateFile:$this->coreFormPath . '/parts/newItemTemplate.latte',
					type: RelationTemplatePart::TYPE_RELATION_PRESCRIPTION),
			]
		);
	}
}
