<?php declare(strict_types = 1);

namespace App\PostType\Feature\Model\Orm;

use App\PostType\Page\Model\Orm\Tree;
use Nextras\Orm\Entity\Entity;

/**
 * @property int $id {primary}
 * @property int $sort
 *
 * RELATIONS
 * @property FeatureLocalization $featureLocalization {m:1 FeatureLocalization::$featureLocalizationTrees}
 * @property Tree $tree {m:1 Tree::$featureLocalizationTrees}
 */
class FeatureLocalizationTree extends Entity
{

}
