<?php declare(strict_types = 1);

namespace App\PostType\Feature\Model\Orm;

use App\Model\Orm\CollectionById;
use App\Model\Orm\Searchable;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method Feature getById($id)
 * @method Feature[]|ICollection searchByName(string $q, array $excluded = [])
 * @method Feature[]|ICollection findByExactOrder(array $ids)
 */
final class FeatureRepository extends Repository implements CollectionById, Searchable
{

	public static function getEntityClassNames(): array
	{
		return [Feature::class];
	}

	public function findByIdOrder(array $ids): ICollection
	{
		return $this->findByExactOrder($ids);
	}


	public function searchByName(string $string, array $excludedIds = []): ICollection
	{
		return $this->getMapper()->searchByName($string, $excludedIds);
	}


	public function getMapper(): FeatureMapper
	{
		$mapper = parent::getMapper();
		assert($mapper instanceof FeatureMapper);
		return $mapper;
	}

}
