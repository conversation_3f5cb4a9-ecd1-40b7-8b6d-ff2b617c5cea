{block content}
	{snippet content}
		{include $templates.'/part/box/annot.latte'}

		<div class="row-main">
			{snippet articles}
				{snippetArea articlesInner}
					{include $templates.'/part/crossroad/features.latte', items: $featureLocalizations}
				{/snippetArea}
			{/snippet}
		</div>

		{control customContentRenderer}
		{include $templates.'/part/box/contact-person.latte'}
		{include $templates.'/part/crossroad/casestudies.latte', class: 'u-pb-md u-pb-lg@md u-pt-md u-pt-lg@md u-bgc-yellow', classGrid: 'grid grid--center', classItem: 'grid__cell grid__cell--eq size--6-12@sm size--4-12@lg'}
{* 		
		{foreach $object->cc as $key => $value}
			{if strpos($key, 'casestudies') === 0}
				{var $cc = $value}
				{breakIf true}
			{/if}
		{/foreach} *}
	{/snippet}
{/block}
