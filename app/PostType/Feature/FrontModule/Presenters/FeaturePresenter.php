<?php declare(strict_types = 1);

namespace App\PostType\Feature\FrontModule\Presenters;

use App\FrontModule\Components\CustomContentRenderer\HasCustomContentRenderer;
use App\FrontModule\Presenters\BasePresenter;
use App\PostType\Feature\FrontModule\Components\Attached\AttachedFeatures;
use App\PostType\Feature\FrontModule\Components\Attached\AttachedFeaturesFactory;
use App\PostType\Feature\Model\Orm\Feature;
use App\PostType\Feature\Model\Orm\FeatureLocalization;
use App\PostType\Feature\Model\Orm\FeatureLocalizationModel;
use App\PostType\Page\Model\Orm\CommonTree;

/**
 * @method Feature getObject()
 */
final class FeaturePresenter extends BasePresenter
{

	use HasCustomContentRenderer;

	private FeatureLocalization $featureLocalization;

	public function __construct(
		private FeatureLocalizationModel $featureLocalizationModel,
	)
	{
		parent::__construct();
	}

	public function startup(): void
	{
		parent::startup();
	}


	public function actionDefault(CommonTree $object): void
	{
		$this->setObject($object);
	}

	public function actionServicesMain(CommonTree $object): void
	{
		$this->setObject($object);
	}

	public function actionServicesCategory(CommonTree $object): void
	{
		$this->setObject($object);
	}

	public function actionServicesLP(CommonTree $object): void
	{
		$this->setObject($object);
	}




	public function renderServicesMain(CommonTree $object): void
	{

	}


	public function renderServicesCategory(CommonTree $object): void
	{
		$this->addComponent($this->visualPaginatorFactory->create(), 'pager');
		$this['pager']->object = $object;
		$this['pager']->special = true;

		$paginator = $this['pager']->getPaginator();
		$paginator->itemsPerPage = $this->configService->get('feature', 'paging');

		$possibleFeatureLocalizationIds = $this->orm->featureLocalizationTree->findBy(['tree->id' => $object->id])->fetchPairs(null, 'featureLocalization->id');
		$featureLocalizations = $this->orm->featureLocalization->findBy(['id' => $possibleFeatureLocalizationIds])->orderBy('publicFrom');

		$totalCount = $featureLocalizations->countStored();
		$paginator->itemCount = $totalCount;

		$this->template->totalCount = $totalCount;
		$this->template->featureLocalizations = $featureLocalizations->limitBy($paginator->itemsPerPage, $paginator->offset);

		if ($this->isAjax()) {
			if ($this['pager']->getParameter('more')) {
				$this->redrawControl('articlesInner');
				$this->redrawControl('articlesPagerBottom');
				$this->redrawControl('articleList');
			} else {
				if (!$this->getSignal()) {
					$this->redrawControl();
				}
			}
		}
	}

//	public function renderServicesLP(CommonTree $object): void
//	{
//
//	}

	public function actionDetail(FeatureLocalization $object): void
	{
		$this->setObject($object);
		$this->featureLocalization = $object;
	}


	public function renderDetail(): void
	{
		$this->featureLocalizationModel->increaseViews($this->featureLocalization);

		bd("cats:");
		bd($this->featureLocalization->categories);

		/** @var \ArrayObject<int, \App\PostType\Blog\Model\Orm\BlogLocalization> */
		$articles = new \ArrayObject();
		// nacteni clanku...
		foreach ($this->featureLocalization->categories as $cat) {
			bd("kat name: ".$cat->name);
			// najdeme stítky s těmito kategoriemi
			if ($cat->blogTagLocalizationTrees->count()) {
				foreach ($cat->blogTagLocalizationTrees as $blogTagLocalizationTree) {
					bd("počet článků na šítek: ".$blogTagLocalizationTree->blogTagLocalization->name.": ".$blogTagLocalizationTree->blogTagLocalization->blogsPublic->count());
					$blogs = $blogTagLocalizationTree->blogTagLocalization->blogsPublic;
					foreach ($blogs as $blog) {
						$articles[] = $blog;
					}
				}
			}
		}

		$this->template->articles = $articles;
		$this->template->featureLocalization = $this->featureLocalization;
	}

	public function beforeRender(): void
	{
		parent::beforeRender();
		$this->template->setFile(__DIR__ . '/../templates/' . $this->getAction() . '.latte');
	}


}
