<?php declare(strict_types = 1);

namespace App\PostType\Author\AdminModule\Presenters;

use App\AdminModule\Presenters\BasePresenter;
use App\Model\Orm\User\User;
use App\PostType\Author\AdminModule\Components\Form\AuthorFormPrescription;
use App\PostType\Author\Model\AuthorLocalizationFacade;
use App\PostType\Author\Model\Orm\AuthorLocalization;
use App\PostType\Core\AdminModule\Components\DataGrid\DataGrid;
use App\PostType\Core\AdminModule\Components\DataGrid\DataGridFactory;
use App\PostType\Core\AdminModule\Components\Form\Form;
use App\PostType\Core\AdminModule\Components\Form\FormData\BaseFormData;
use App\PostType\Core\AdminModule\Components\Form\FormFactory;
use App\PostType\Core\AdminModule\Components\ShellForm\ShellForm;
use App\PostType\Core\AdminModule\Components\ShellForm\ShellFormFactory;

final class AuthorPresenter extends BasePresenter
{

	private const ORM_REPOSITORY_NAME = 'author';

	private AuthorLocalization $authorLocalization;

	public function __construct(
		private DataGridFactory $dataGridFactory,
		private FormFactory $authorFormFactory,
		private ShellFormFactory $shellFormFactory,
		private AuthorLocalizationFacade $authorLocalizationFacade,
		private AuthorFormPrescription $authorFormPrescription,
	)
	{
		parent::__construct();
	}

	public function renderDefault(): void
	{
	}


	public function actionEdit(int $id): void
	{
		$authorLocalization = $this->orm->authorLocalization->getById($id);
		if ($authorLocalization === null) {
			$this->redirect('default');
		}

		$this->authorLocalization = $authorLocalization;
	}


	public function renderEdit(int $id): void
	{
	}


	protected function createComponentGrid(): DataGrid
	{
		return $this->dataGridFactory->create(self::ORM_REPOSITORY_NAME, $this->orm->authorLocalization->findAll());
	}


	protected function createComponentForm(): Form
	{
		/** @var User $userEntity */
		$userEntity = $this->userEntity;
		return $this->authorFormFactory->create($this->authorLocalizationFacade, $this->authorLocalization, $userEntity, $this->authorFormPrescription->get($this->authorLocalization));
	}

	public function createComponentShellForm(): ShellForm
	{
		return $this->shellFormFactory->create(entity:null, entityLocalizationFacade: $this->authorLocalizationFacade);
	}

	public function beforeRender(): void
	{
		parent::beforeRender();
		$this->template->setFile(__DIR__ . '/../templates/' . $this->getAction() . '.latte');
	}

}
