<?php declare(strict_types = 1);

namespace App\PostType\ReferenceTag\FrontModule\Presenters;

use App\FrontModule\Presenters\BasePresenter;
use App\PostType\BlogTag\Model\BlogTagModel;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTag;
use App\PostType\ReferenceTag\Model\Orm\ReferenceTag\ReferenceTag;
use App\PostType\ReferenceTag\Model\Orm\ReferenceTag\ReferenceTagLocalization;

/**
 * @method ReferenceTag getObject()
 */
final class ReferenceTagPresenter extends BasePresenter
{

	private ReferenceTagLocalization $referenceTagLocalization;

	public function __construct(
		private BlogTagModel $blogTagModel,
	)
	{
		parent::__construct();
	}

	public function startup(): void
	{
		parent::startup();
	}


	public function actionDetail(ReferenceTagLocalization $object): void
	{
		$this->setObject($object);
		$this->referenceTagLocalization = $object;
	}


	public function renderDetail(): void
	{
		$this->addComponent($this->visualPaginatorFactory->create(), 'pager');
		$this['pager']->object = $this->referenceTagLocalization;
		$this['pager']->special = true;

		$paginator = $this['pager']->getPaginator();
		$paginator->itemsPerPage = 1;
		$allPublicReferences = $this->referenceTagLocalization->referencesPublic;
		$paginator->itemCount = $allPublicReferences->count();

		$this->template->referenceTag = $this->referenceTagLocalization;
		$this->template->references = $allPublicReferences->limitBy($paginator->itemsPerPage, $paginator->offset);

		$this->template->tagsWithCount = $this->blogTagModel->getTagsWithCount($this->mutation, BlogTag::TYPE_REFERENCE);


		if ($this->isAjax()) {
			$this->redrawControl('articles');
		}
	}

	public function beforeRender(): void
	{
		parent::beforeRender();
		$this->template->setFile(__DIR__ . '/../templates/' . $this->getAction() . '.latte');
	}

}
