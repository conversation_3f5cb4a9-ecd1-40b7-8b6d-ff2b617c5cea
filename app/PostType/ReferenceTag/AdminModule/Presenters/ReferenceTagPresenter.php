<?php declare(strict_types = 1);

namespace App\PostType\ReferenceTag\AdminModule\Presenters;

use App\AdminModule\Presenters\BasePresenter;
use App\Model\Orm\User\User;
use App\PostType\ReferenceTag\Model\Orm\ReferenceTag\ReferenceTagLocalization;
use App\PostType\ReferenceTag\Model\ReferenceTagLocalizationFacade;
use App\PostType\Core\AdminModule\Components\DataGrid\DataGrid;
use App\PostType\Core\AdminModule\Components\DataGrid\DataGridFactory;
use App\PostType\Core\AdminModule\Components\Form\Form;
use App\PostType\Core\AdminModule\Components\Form\FormFactory;
use App\PostType\Core\AdminModule\Components\ShellForm\ShellForm;
use App\PostType\Core\AdminModule\Components\ShellForm\ShellFormFactory;

final class ReferenceTagPresenter extends BasePresenter
{

	private const ORM_REPOSITORY_NAME = 'referenceTag';

	private ReferenceTagLocalization $referenceTagLocalization;

	public function __construct(
		private DataGridFactory $dataGridFactory,
		private FormFactory $referenceTagFormFactory,
		private ShellFormFactory $shellFormFactory,
		private ReferenceTagLocalizationFacade $referenceTagLocalizationFacade,
	)
	{
		parent::__construct();
	}

	public function renderDefault(): void
	{
	}


	public function actionEdit(int $id): void
	{
		$referenceTagLocalization = $this->orm->referenceTagLocalization->getById($id);
		if ($referenceTagLocalization === null) {
			$this->redirect('default');
		}

		$this->referenceTagLocalization = $referenceTagLocalization;
	}


	public function renderEdit(int $id): void
	{
	}


	protected function createComponentGrid(): DataGrid
	{
		return $this->dataGridFactory->create(self::ORM_REPOSITORY_NAME, $this->orm->referenceTagLocalization->findAll());
	}


	protected function createComponentForm(): Form
	{
		/** @var User $userEntity */
		$userEntity = $this->userEntity;
		return $this->referenceTagFormFactory->create($this->referenceTagLocalizationFacade, $this->referenceTagLocalization, $userEntity);
	}

	public function createComponentShellForm(): ShellForm
	{
		return $this->shellFormFactory->create(entity:null, entityLocalizationFacade: $this->referenceTagLocalizationFacade);
	}

	public function beforeRender(): void
	{
		parent::beforeRender();
		$this->template->setFile(__DIR__ . '/../templates/' . $this->getAction() . '.latte');
	}

}
