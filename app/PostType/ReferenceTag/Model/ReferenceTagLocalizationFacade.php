<?php declare(strict_types = 1);

namespace App\PostType\ReferenceTag\Model;


use App\PostType\ReferenceTag\Model\Orm\ReferenceTag\ReferenceTag;
use App\PostType\ReferenceTag\Model\Orm\ReferenceTag\ReferenceTagLocalization;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\PostType\Core\Model\EntityLocalizationFacade;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Core\Model\ParentEntity;

class ReferenceTagLocalizationFacade implements EntityLocalizationFacade
{

	public function __construct(
		private Orm $orm,
	)
	{
	}


	public function create(Mutation $mutation, ParentEntity|null $localizableEntity): LocalizationEntity
	{
		$localization = new ReferenceTagLocalization();
		$this->orm->referenceTagLocalization->attach($localization);
		$localization->mutation = $mutation;

		if ($localizableEntity === null) {
			$localizableEntity = new ReferenceTag();
			$localization->referenceTag = $localizableEntity;
		} else {
			assert($localizableEntity instanceof ReferenceTag);
			$localization->referenceTag = $localizableEntity;
		}

		$this->orm->persistAndFlush($localization);

		return $localization;
	}


	public function remove(LocalizationEntity $localizableEntity): void
	{
		assert($localizableEntity instanceof ReferenceTagLocalization);

		$parent = $localizableEntity->getParent();
		$this->orm->referenceTagLocalization->remove($localizableEntity);

		if ($parent->localizations->count() === 0) {
			$this->orm->referenceTag->remove($parent);
		}

		$this->orm->flush();
	}

}
