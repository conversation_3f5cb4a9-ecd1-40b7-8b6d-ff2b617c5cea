<?php declare(strict_types = 1);

namespace App\PostType\ReferenceTag\Model\Orm\ReferenceTag;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Repository\QueryForIdsByMutation;
use App\Model\Orm\Traits\HasPublicParameter;
use Nextras\Dbal\Result\Result;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method ReferenceTagLocalization getById($id)
 * @method ReferenceTagLocalization[]|ICollection searchByName(string $q, array $excluded)
 * @method array findAllIds(?int $limit)
 */
final class ReferenceTagLocalizationRepository extends Repository implements QueryForIdsByMutation
{

	use HasPublicParameter;

	public static function getEntityClassNames(): array
	{
		return [ReferenceTagLocalization::class];
	}


	public function getPublicOnlyWhereParams(): array
	{
		return [
			'public' => 1,
		];
	}

	public function findAllIdsInMutation(Mutation $mutation, ?int $limit = null): Result
	{
		$mapper = $this->mapper;
		assert($mapper instanceof ReferenceTagLocalizationMapper);

		return $mapper->findAllIdsInMutation($mutation, $limit);
	}

}
