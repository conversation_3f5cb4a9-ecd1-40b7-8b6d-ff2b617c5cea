<?php declare(strict_types = 1);

namespace App\PostType\ReferenceTag\Model\Orm\ReferenceTag;

use App\Model\Orm\JsonContainer;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\RoutableEntity;
use App\Model\Orm\Traits\HasCustomContent;
use App\Model\Orm\Traits\HasCustomFields;
use App\PostType\Reference\Model\Orm\ReferenceLocalization;
use App\PostType\Reference\Model\Orm\ReferenceLocalizationRepository;
use App\PostType\Core\Model\Editable;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Core\Model\ParentEntity;
use App\PostType\Core\Model\Publishable;
use Nette\Utils\ArrayHash;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Collection\ICollection;

/**
 * @property int $id {primary}
 * @property string $name  {default ''}
 * @property bool $public {default false}
 * @property DateTimeImmutable|null $editedTime
 * @property int|null $edited
 * @property int $sort {default 0}
 *
 * @property ArrayHash $customFieldsJson {container JsonContainer}
 * @property ArrayHash $customContentJson {container JsonContainer}
 *
 * RELATIONS
 * @property ReferenceTag $referenceTag {M:1 ReferenceTag::$localizations}
 * @property Mutation $mutation {m:1 Mutation, oneSided=true}
 *
 *
 * VIRTUAL
 * @property ArrayHash|null $cf {virtual}
 * @property ArrayHash|null $cc {virtual}
 * @property-read ReferenceLocalization[]|ICollection $referencesPublic {virtual}
 * @property-read string $template {virtual}
 */
class ReferenceTagLocalization extends RoutableEntity implements LocalizationEntity, Publishable, Editable
{

	use HasCustomFields;
	use HasCustomContent;

	private ReferenceLocalizationRepository $referenceLocalizationRepository;

	public function injectReferenceRepository(ReferenceLocalizationRepository $referenceLocalizationRepository): void
	{
		$this->referenceLocalizationRepository = $referenceLocalizationRepository;
	}


	protected function getterTemplate(): string
	{
		return ':ReferenceTag:Front:ReferenceTag:detail';
	}


	protected function getterPath(): array
	{
		$referencePage = $this->mutation->pages->references;
		$path = $referencePage->path;
		$path[] = $referencePage->id;
		return $path;
	}


	protected function getterReferencesPublic(): ICollection
	{
//		$referenceIds = $this->referenceTag->references->toCollection()->fetchPairs(null, 'id');
		return $this->referenceLocalizationRepository
			->findBy([
					'reference' => 1,
					'mutation' => $this->mutation,
				])
			->findBy($this->referenceLocalizationRepository->getPublicOnlyWhereParams());
	}

	public function getId(): int
	{
		return $this->id;
	}

	public function getMutation(): Mutation
	{
		return $this->mutation;
	}


	public function setMutation(Mutation $mutation): void
	{
		$this->mutation = $mutation;
	}


	public function getParent(): ReferenceTag
	{
		return $this->referenceTag;
	}


	public function setParent(ParentEntity $parentEntity): void
	{
		assert($parentEntity instanceof ReferenceTag);
		$this->referenceTag = $parentEntity;
	}

	public function getIsPublic(): bool
	{
		return $this->public;
	}

	public function setIsPublic(bool $isPublic): void
	{
		$this->public = $isPublic;
	}

	public function getName(): string
	{
		return $this->name;
	}

	public function setName(string $name): void
	{
		$this->name = $name;
	}

	public function setEditorId(int $id): void
	{
		$this->edited = $id;
	}

	public function setEditedTime(DateTimeImmutable $editedTime): void
	{
		$this->editedTime = $editedTime;
	}
}
