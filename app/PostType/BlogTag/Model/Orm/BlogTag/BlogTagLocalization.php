<?php declare(strict_types = 1);

namespace App\PostType\BlogTag\Model\Orm\BlogTag;

use App\Model\Orm\JsonContainer;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\RoutableEntity;
use App\Model\Orm\Traits\HasCustomContent;
use App\Model\Orm\Traits\HasCustomFields;
use App\PostType\Author\Model\Orm\AuthorLocalization;
use App\PostType\Author\Model\Orm\AuthorLocalizationRepository;
use App\PostType\Blog\Model\Orm\BlogLocalization;
use App\PostType\Blog\Model\Orm\BlogLocalizationRepository;
use App\PostType\Blog\Model\Orm\BlogLocalizationTree;
use App\PostType\Core\Model\Editable;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Core\Model\ParentEntity;
use App\PostType\Core\Model\Publishable;
use App\PostType\Material\Model\Orm\MaterialLocalization;
use App\PostType\Material\Model\Orm\MaterialLocalizationRepository;
use App\PostType\Page\Model\Orm\Tree;
use App\PostType\Page\Model\Orm\TreeRepository;
use App\PostType\Reference\Model\Orm\ReferenceLocalization;
use App\PostType\Reference\Model\Orm\ReferenceLocalizationRepository;
use Nette\Utils\ArrayHash;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Collection\EmptyCollection;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Relationships\OneHasMany;

/**
 * @property int $id {primary}
 * @property string $name  {default ''}
 * @property bool $public {default false}
 * @property DateTimeImmutable|null $editedTime
 * @property int|null $edited
 * @property int $sort {default 0}
 *
 * @property ArrayHash $customFieldsJson {container JsonContainer}
 * @property ArrayHash $customContentJson {container JsonContainer}
 *
 * RELATIONS
 * @property BlogTag $blogTag {M:1 BlogTag::$localizations}
 * @property Mutation $mutation {m:1 Mutation, oneSided=true}
 * @property BlogTagLocalizationTree[]|OneHasMany $blogTagLocalizationTrees {1:m BlogTagLocalizationTree::$blogTagLocalization, orderBy=[sort=ASC], cascade=[persist, remove]}
 *
 * VIRTUAL
 * @property-read  Tree[]|ICollection $categories {virtual}
 * @property ArrayHash|null $cf {virtual}
 * @property ArrayHash|null $cc {virtual}
 * @property-read  BlogLocalization[]|ICollection $blogsPublic {virtual}
 * @property-read  ReferenceLocalization[]|ICollection $referencesPublic {virtual}
 * @property-read  AuthorLocalization[]|ICollection $authorsPublic {virtual}
 * @property-read  MaterialLocalization[]|ICollection $materialsPublic {virtual}
 * @property-read string $template {virtual}
 */
class BlogTagLocalization extends RoutableEntity implements LocalizationEntity, Publishable, Editable
{

	use HasCustomFields;
	use HasCustomContent;

	private BlogLocalizationRepository $blogLocalizationRepository;
	private ReferenceLocalizationRepository $referenceLocalizationRepository;
	private AuthorLocalizationRepository $authorLocalizationRepository;
	private MaterialLocalizationRepository $materialLocalizationRepository;
	private TreeRepository $treeRepository;


	public function injectBlogRepository(
		BlogLocalizationRepository $blogLocalizationRepository,
		ReferenceLocalizationRepository $referenceLocalizationRepository,
		AuthorLocalizationRepository $authorLocalizationRepository,
		MaterialLocalizationRepository $materialLocalizationRepository,
		TreeRepository $treeRepository,
	): void
	{
		$this->blogLocalizationRepository = $blogLocalizationRepository;
		$this->referenceLocalizationRepository = $referenceLocalizationRepository;
		$this->authorLocalizationRepository = $authorLocalizationRepository;
		$this->materialLocalizationRepository = $materialLocalizationRepository;
		$this->treeRepository = $treeRepository;
	}


	protected function getterTemplate(): string
	{
		if ($this->blogTag->type === BlogTag::TYPE_REFERENCE) {
			return ':BlogTag:Front:BlogTag:referenceTagDetail';
		} elseif ($this->blogTag->type === BlogTag::TYPE_AUTHOR) {
			return ':BlogTag:Front:BlogTag:authorTagDetail';
		} elseif ($this->blogTag->type === BlogTag::TYPE_MATERIAL) {
			return ':BlogTag:Front:BlogTag:materialTagDetail';
		} else {
			return ':BlogTag:Front:BlogTag:blogTagDetail';
		}
	}


	protected function getterPath(): array
	{
		$blogPage = $this->mutation->pages->blog;
		$path = $blogPage->path;
		$path[] = $blogPage->id;
		return $path;
	}


	protected function getterBlogsPublic(): ICollection
	{
		$blogIds = $this->blogTag->blogs->toCollection()->fetchPairs(null, 'id');
		return $this->blogLocalizationRepository
			->findBy([
					'blog' => $blogIds,
					'mutation' => $this->mutation,
				])
			->findBy($this->blogLocalizationRepository->getPublicOnlyWhereParams());
	}


	protected function getterReferencesPublic(): ICollection
	{
		$blogIds = $this->blogTag->references->toCollection()->fetchPairs(null, 'id');
		return $this->referenceLocalizationRepository
			->findBy([
				'reference' => $blogIds,
				'mutation' => $this->mutation,
			])
			->findBy($this->referenceLocalizationRepository->getPublicOnlyWhereParams());
	}


	protected function getterAuthorsPublic(): ICollection
	{
		$blogIds = $this->blogTag->references->toCollection()->fetchPairs(null, 'id');
		return $this->authorLocalizationRepository
			->findBy([
				'author' => $blogIds,
				'mutation' => $this->mutation,
			])
			->findBy($this->authorLocalizationRepository->getPublicOnlyWhereParams());
	}

	protected function getterMaterialsPublic(): ICollection
	{
		$blogIds = $this->blogTag->materials->toCollection()->fetchPairs(null, 'id');
		return $this->materialLocalizationRepository
			->findBy([
				'material' => $blogIds,
				'mutation' => $this->mutation,
			])
			->findBy($this->materialLocalizationRepository->getPublicOnlyWhereParams());
	}




	public function getId(): int
	{
		return $this->id;
	}

	public function getMutation(): Mutation
	{
		return $this->mutation;
	}


	public function setMutation(Mutation $mutation): void
	{
		$this->mutation = $mutation;
	}


	public function getParent(): BlogTag
	{
		return $this->blogTag;
	}


	public function setParent(ParentEntity $parentEntity): void
	{
		assert($parentEntity instanceof BlogTag);
		$this->blogTag = $parentEntity;
	}

	public function getIsPublic(): bool
	{
		return $this->public;
	}

	public function setIsPublic(bool $isPublic): void
	{
		$this->public = $isPublic;
	}

	public function getName(): string
	{
		return $this->name;
	}

	public function setName(string $name): void
	{
		$this->name = $name;
	}

	public function setEditorId(int $id): void
	{
		$this->edited = $id;
	}

	public function setEditedTime(DateTimeImmutable $editedTime): void
	{
		$this->editedTime = $editedTime;
	}

	protected function getterCategories(): ICollection
	{
		$categoryIds = $this->blogTagLocalizationTrees->toCollection()->fetchPairs(null, 'tree->id');
		if ($categoryIds === []) {
			return new EmptyCollection();
		}

		return $this->treeRepository->findFilteredPages($categoryIds)->findBy(['rootId' => $this->getMutation()->id]);
	}

}
