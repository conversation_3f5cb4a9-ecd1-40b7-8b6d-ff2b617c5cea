{varType App\PostType\BlogTag\Model\Orm\BlogTag\BlogTag $object}
{*detail tagu reference*}
{block content}
	{snippet content}
		{include $templates.'/part/box/annot.latte', class: 'u-mb-0'}
		<div class="row-main">
			{snippet tags}
			{include $templates.'/part/crossroad/tags.latte', class: 'u-pt-md u-pt-lg@md u-mb-md u-mb-lg@md', btnClass: '', btnClassActive: '', items: $tagsWithCount, linkAllTags=>$pages->references}
		{/snippet}
		{snippet articles}
			{include $templates.'/part/crossroad/references.latte', items: $items}
		{/snippet}
		</div>
		{control customContentRenderer}
	{/snippet}
{/block}
