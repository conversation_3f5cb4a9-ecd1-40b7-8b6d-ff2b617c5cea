<?php declare(strict_types=1);

namespace App\PostType\BlogTag\AdminModule\Components\Form;

use App\Model\CustomField\SuggestUrls;
use App\PostType\BlogTag\AdminModule\Components\Form\FormData\BlogTagFormData;
use App\PostType\BlogTag\Model\BlogTagModel;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTag;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTagLocalization;
use App\PostType\Core\AdminModule\Components\Form\Builder;
use App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\CustomFormExtender;
use App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\Templates\CommonTemplatePart;
use App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\Templates\RelationTemplatePart;
use App\PostType\Core\AdminModule\Components\Form\Definition\FormDefinition;
use App\PostType\Core\AdminModule\Components\Form\Definition\RelationInfoFactory;
use App\PostType\Core\AdminModule\Components\Form\Handler;
use Nette\Application\UI\Form;
use Nette\Http\Request;

class BlogTagFormPrescription
{
	public function __construct(
		private readonly RelationInfoFactory $relationInfoFactory,
		private readonly Request $request,
		private readonly Builder $coreBuilder,
		private readonly SuggestUrls $urls,
		private readonly string $coreFormPath,
		private readonly BlogTagModel $blogTagModel,
	)
	{}

	public function get(BlogTagLocalization $blogTagLocalization): FormDefinition
	{
		$extenders = [];
		$extenders[] = $this->addTypeInfo($blogTagLocalization->blogTag);
		if ($blogTagLocalization->blogTag->type === BlogTag::TYPE_BLOG) {
			$extenders[] = $this->addCategories($blogTagLocalization);
		}

		$form = new Form();
		$form->setMappedType(BlogTagFormData::class);

		return new FormDefinition(
			form: $form,
			extenders: $extenders,
		);
	}

	public function addTypeInfo(BlogTag $blogTag): CustomFormExtender
	{
		return new CustomFormExtender(
			addHandler: function (Form $form) {},
			successHandler: function (Form $form, BlogTagFormData $data)  {},
			templateParts: [
				new CommonTemplatePart(__DIR__ . '/settings.latte',
					CommonTemplatePart::TYPE_SIDE,
					['type' => $blogTag->type],
				)

			]
		);

	}


	private function addCategories(BlogTagLocalization $blogTagLocalization): CustomFormExtender
	{
		$url = $this->urls['searchMutationPage'];
		$url->params['mutationId'] = $blogTagLocalization->mutation->id;
		$url->params['templates'] = [':Feature:Front:Feature:servicesCategory', ':Feature:Front:Feature:servicesLP'];

		$categoriesRelationsInfo = $this->relationInfoFactory->create(
			sourceEntity: $blogTagLocalization,
			propertyName: 'blogTagLocalizationTrees',
			suggestUrl: $url,
			inputSuggestPropertyName: 'name',
			toggleName: 'categories',
			dragAndDrop: true,
			builderCollection: $blogTagLocalization->categories,

		);

		return new CustomFormExtender(
			addHandler: function (Form $form) use ($categoriesRelationsInfo) {
				$this->coreBuilder->addHasManyRelation($form, $categoriesRelationsInfo, $this->request->getPost());
			},
			successHandler: function (Form $form, BlogTagFormData $data) use ($categoriesRelationsInfo, $blogTagLocalization) {
				$ids = Handler::readOnlyValidIds($data->{$categoriesRelationsInfo->propertyName});
				$this->blogTagModel->setCategoriesByIds($blogTagLocalization, $ids);
			},
			templateParts: [
				new RelationTemplatePart(
					relationInfo: $categoriesRelationsInfo,
					templateFile: $this->coreFormPath . '/parts/relation.latte',
					type: RelationTemplatePart::TYPE_RELATION),
				new RelationTemplatePart(
					relationInfo: $categoriesRelationsInfo,
					templateFile:$this->coreFormPath . '/parts/newItemTemplate.latte',
					type: RelationTemplatePart::TYPE_RELATION_PRESCRIPTION),
			]
		);
	}

}
