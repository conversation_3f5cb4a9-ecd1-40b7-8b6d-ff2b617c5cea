<?php declare(strict_types = 1);

namespace App\PostType\BlogTag\AdminModule\Presenters;

use App\AdminModule\Presenters\BasePresenter;
use App\Model\Orm\User\User;
use App\PostType\BlogTag\AdminModule\Components\DataGrid\BlogTagDataGridPrescription;
use App\PostType\BlogTag\AdminModule\Components\Form\BlogTagFormPrescription;
use App\PostType\BlogTag\AdminModule\Components\ShellForm\ShellForm;
use App\PostType\BlogTag\AdminModule\Components\ShellForm\ShellFormFactory;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTagLocalization;
use App\PostType\BlogTag\Model\BlogTagLocalizationFacade;
use App\PostType\Core\AdminModule\Components\DataGrid\DataGrid;
use App\PostType\Core\AdminModule\Components\DataGrid\DataGridFactory;
use App\PostType\Core\AdminModule\Components\Form\Form;
use App\PostType\Core\AdminModule\Components\Form\FormFactory;

final class BlogTagPresenter extends BasePresenter
{

	private const ORM_REPOSITORY_NAME = 'blogTag';

	private BlogTagLocalization $blogTagLocalization;

	public function __construct(
		private DataGridFactory $dataGridFactory,
		private FormFactory $blogTagFormFactory,
//		private ShellFormFactory $shellFormFactory,
		private ShellFormFactory $shellFormFactory,
		private BlogTagLocalizationFacade $blogTagLocalizationFacade,
		private BlogTagFormPrescription $blogTagFormPrescription,
		private BlogTagDataGridPrescription $blogTagDataGridPrescription,

	)
	{
		parent::__construct();
	}

	public function renderDefault(): void
	{
	}


	public function actionEdit(int $id): void
	{
		$blogTagLocalization = $this->orm->blogTagLocalization->getById($id);
		if ($blogTagLocalization === null) {
			$this->redirect('default');
		}

		$this->blogTagLocalization = $blogTagLocalization;
	}


	public function renderEdit(int $id): void
	{
	}


	protected function createComponentGrid(): DataGrid
	{
//		return $this->dataGridFactory->create(
//			self::ORM_REPOSITORY_NAME,
//			$this->orm->blogTagLocalization->findAll());

		return $this->dataGridFactory->create(
			baseEntityName: self::ORM_REPOSITORY_NAME,
			collection: $this->orm->blogTagLocalization->findAll(),
			dataGridDefinition: $this->blogTagDataGridPrescription->get(),
		);

	}


	protected function createComponentForm(): Form
	{
		/** @var User $userEntity */
		$userEntity = $this->userEntity;
		return $this->blogTagFormFactory->create($this->blogTagLocalizationFacade, $this->blogTagLocalization, $userEntity, $this->blogTagFormPrescription->get($this->blogTagLocalization));
	}

	public function createComponentShellForm(): ShellForm
	{
		return $this->shellFormFactory->create();
	}

	public function beforeRender(): void
	{
		parent::beforeRender();
		$this->template->setFile(__DIR__ . '/../templates/' . $this->getAction() . '.latte');
	}

}
