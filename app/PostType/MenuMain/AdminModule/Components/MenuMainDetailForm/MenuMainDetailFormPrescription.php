<?php declare(strict_types = 1);

namespace App\PostType\MenuMain\AdminModule\Components\MenuMainDetailForm;

use App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\CustomFormExtender;
use App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\Templates\CommonTemplatePart;
use App\PostType\Core\AdminModule\Components\Form\Definition\FormDefinition;
use App\PostType\MenuMain\Model\Orm\MenuMainLocalization\MenuMainLocalization;
use Nette\Application\UI\Form;

class MenuMainDetailFormPrescription
{

	public function getPrescription(MenuMainLocalization $menuMainLocalization): FormDefinition
	{
		$extenders = [];
		$extenders[] = $this->addSetting($menuMainLocalization);

		return new FormDefinition(
			form: $this->createForm(),
			extenders: $extenders,
		);
	}

	private function createForm(): Form
	{
		$form = new Form();
		$form->setMappedType(MenuMainLocalizationFormData::class);

		return $form;
	}

	private function addSetting(MenuMainLocalization $menuMainLocalization): CustomFormExtender
	{
		return new CustomFormExtender(
			addHandler: function (Form $form) use ($menuMainLocalization) {
				$form->addInteger('order', 'order')
					->setDefaultValue($menuMainLocalization->menuMain->order);

				// $form->addCheckbox('isBig', 'isBig')
					// ->setDefaultValue($menuMainLocalization->isBig);
			},
			successHandler: function (Form $form, MenuMainLocalizationFormData $formData) use ($menuMainLocalization) {
				$menuMainLocalization->menuMain->order = $formData->order;
				// $menuMainLocalization->isBig = $formData->isBig;
			},
			templateParts: [
				new CommonTemplatePart(
					templateFile: __DIR__ . '/templates/settings.latte',
					type: CommonTemplatePart::TYPE_MAIN,
				),
			],
		);
	}

}
