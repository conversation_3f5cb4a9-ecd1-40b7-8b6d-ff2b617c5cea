<?php declare(strict_types = 1);

namespace App\Model\Image\Setup;

readonly class ImageSizeConfiguration
{

	public function __construct(
		public int $width,
		public int $height,
		public bool $keepRatio,
		public bool $fill,
		public bool $square,
		public int $quality,
		public ?string $watermark,
		public ?string $type = null,
	)
	{
	}


	public static function fromArray(string $type, array $setup): self
	{
		$width = $setup['width'];
		$height = $setup['height'];
		$keepRatio = isset($setup['keepRatio']) && $setup['keepRatio'];
		$fill = isset($setup['fill']) && $setup['fill'];
		$square = isset($setup['square']) && $setup['square'];
		$watermark = (isset($setup['watermark'])) ? (string) $setup['watermark'] : '';
		$quality = (isset($setup['quality'])) ? (int) $setup['quality'] : 100;

		return new self($width, $height, $keepRatio, $fill, $square, $quality, $watermark, $type);
	}

}
