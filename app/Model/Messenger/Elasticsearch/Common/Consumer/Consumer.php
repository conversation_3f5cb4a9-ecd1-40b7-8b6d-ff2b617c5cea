<?php declare(strict_types = 1);

namespace App\Model\Messenger\Elasticsearch\Common\Consumer;

use App\PostType\Page\Model\Orm\CatalogTree;
use App\PostType\Blog\Model\Orm\BlogLocalization;
use App\PostType\Blog\Model\Orm\BlogLocalizationRepository;
use App\PostType\Page\Model\Orm\CommonTree;
use App\PostType\Page\Model\Orm\Tree;
use App\PostType\Page\Model\Orm\TreeRepository;
use LogicException;

abstract class Consumer
{

	public function __construct(
		private TreeRepository $treeRepository,
		private BlogLocalizationRepository $blogLocalizationRepository,
	)
	{
	}

	protected function getObjectByClass(string $class, int $objectId): ?object
	{
		$this->blogLocalizationRepository->setPublicOnly(false);
		$this->treeRepository->setPublicOnly(false);

		return match ($class) {
			BlogLocalization::class => $this->blogLocalizationRepository->getById($objectId),
			Tree::class, CatalogTree::class, CommonTree::class=> $this->treeRepository->getById($objectId),
			default => throw new LogicException(sprintf("Missing definition for '%s' class", $class))
		};
	}

}
