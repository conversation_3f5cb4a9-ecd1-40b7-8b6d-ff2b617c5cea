<?php declare(strict_types = 1);

namespace App\Model\Orm\Parameter;

use App\AdminModule\Presenters\Parameter\Components\ShellForm\FormData\BaseFormData;
use App\Model\CustomField\CustomFields;
use App\Model\Orm\Orm;
use App\Model\Orm\ParameterValue\ParameterValue;
use App\Model\Orm\ParameterValue\ParameterValueModel;
use App\Model\Orm\Product\Product;
use Nette\Utils\Strings;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Entity\IEntity;
use RuntimeException;
use function assert;

final class ParameterModel
{

	public function __construct(
		private readonly Orm $orm,
		private readonly ParameterValueModel $parameterValueModel,
		private readonly CustomFields $customFields,
	)
	{
	}


	public function createNew(BaseFormData $data): Parameter
	{
		$parameter = new Parameter();
		$this->orm->parameter->attach($parameter);

		$parameter->name = $data->name;
		$parameter->type = $data->parameterType;
		$this->orm->parameter->persistAndFlush($parameter);

		return $parameter;
	}


	public function save(Parameter $parameter, mixed $data, mixed $valuesAll): Parameter
	{
		// tyto hodnoty jsou bool a DB je chce v integeru
		$intValues = ['paramSearch', 'extra', 'image', 'paramPrice', 'hasImage', 'isInFilter', 'isProtected'];
		$boolValues = [];
		$noSave = ['uid', 'type'];

		//$data['editedTime'] = new DateTimeImmutable();

		foreach ($parameter->getMetadata()->getProperties() as $i) {
			$col = (string) $i->name;
			if (isset($data[$col])) {
				if (in_array($col, $intValues)) {
					$data[$col] = (int) $data[$col];
				}

				if (!in_array($col, $noSave)) {
					$parameter->$col = $data[$col];
				}
			} else {
				if (in_array($col, $boolValues)) {
					$parameter->$col = 0;
				}
			}
		}

		$this->handleUid($parameter, $data->uid);
		if (isset($valuesAll['customFields'])) {
			$parameter->cf = $this->customFields->prepareDataToSave($valuesAll['customFields']);
		}

		if (in_array($parameter->type, [Parameter::TYPE_SELECT, Parameter::TYPE_MULTISELECT, Parameter::TYPE_NUMBER])) {
			// option editovat pouze pokud se jedna o select nebo multiselect
			// u ostatních typu parametru by doslo k promazani
			$this->handleOptions($parameter, $valuesAll);
		}

		$this->orm->persistAndFlush($parameter);

		// po ulozeni prekontrolovat aliasy
		$this->parameterValueModel->handleParameterValuesAlias($parameter->options->toCollection()->orderBy('id', ICollection::DESC));
		$this->orm->persistAndFlush($parameter);
		return $parameter;
	}


	private function handleUid(Parameter $parameter, string $uid, int $k = 0): void
	{
		if ($uid === '') {
			// aut. gener. UID
			$str = Strings::toAscii($parameter->name);
			$parameter->uid = lcfirst(str_replace(' ', '', ucwords(strtr($str, '_-', ' '))));
		}

		if ($uid !== '') {
			if ($k) {
				$uidToTest = $uid . '-' . $k;
			} else {
				$uidToTest = $uid;
			}

			$entityWithUid = $this->orm->parameter->findBy(['uid' => $uidToTest, 'id!=' => $parameter->id]);

			if ($entityWithUid->count()) {
				$this->handleUid($parameter, $uid, $k + 1);
			} else {
				// SAVE
				$parameter->uid = $uidToTest;
			}
		}
	}


	private function handleOptions(Parameter $parameter, array $post): void
	{
		if ($parameter->hasEditableValues()) {
			$originalParameterValues = $parameter->options->toCollection()->fetchPairs('id', null);

			$counter = 0;
			if (isset($post['valueName']) && $post['valueName']) {
				foreach ($post['valueName'] as $optionId => $tmp) {
					$counter++;
					if (isset($originalParameterValues[$optionId])) {
						// edituji starou hodnotu
						// aktualizuj stare
						/** @var ParameterValue $option */
						$option = $originalParameterValues[$optionId];
						$option->internalAlias = Strings::webalize($post['aliasName'][$option->id]);
						$option->internalValue = $post['valueName'][$option->id];

						$option->sort = $counter;
						$option->parameterSort = $parameter->sort;
						if (!$option->internalAlias) {
							$option->internalAlias = Strings::webalize($option->internalValue);
						}

						unset($originalParameterValues[$optionId]);

					} else {
						$option = new ParameterValue();
						$option->parameter = $parameter;
						$option->internalAlias = Strings::webalize($post['aliasName'][$optionId]);
						$option->internalValue = $post['valueName'][$optionId];
						$option->sort = $counter;
						$option->parameterSort = $parameter->sort;

						if (!$option->internalAlias) {
							$option->internalAlias = Strings::webalize($option->internalValue);
						}

					}

					$this->orm->parameterValue->persistAndFlush($option);
				}
			}

			foreach ($originalParameterValues as $originalParameterValue) {
				$this->orm->parameterValue->remove($originalParameterValue);
			}
		}
	}


	public function remove(Parameter $object, bool $flush = true): void
	{
		if ($object->isProtected) {
			throw new RuntimeException('Cannot delete a protected parameter.');
		}

		foreach ($object->options as $option) {
			$this->orm->remove($option);
		}

		$this->orm->parameter->remove($object);

		if ($flush) {
			$this->orm->flush();
		}
	}


	public function getUidToUnitMap(): array
	{
		$ret = [];
		foreach ($this->orm->parameter->findBy([]) as $parameter) {
			$ret[$parameter->uid] = $parameter->unit;
		}

		return $ret;
	}



	public function getUidToNameMap(): array
	{
		$ret = [];
		foreach ($this->orm->parameter->findBy([]) as $parameter) {
			$ret[$parameter->uid] = $parameter->name;
		}

		return $ret;
	}

	public function removeParameterFrom(IEntity $holder, Parameter $parameter): void
	{
		assert($holder instanceof Product);
		$this->orm->product->removeParameter($holder, $parameter);
	}

}
