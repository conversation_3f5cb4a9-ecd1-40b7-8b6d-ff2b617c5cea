<?php declare(strict_types = 1);

namespace App\Model\Orm\LibraryImage;

use App\Model\ConfigService;
use App\Model\ElasticSearch\All\Repository;
use App\Model\Image\Storage\BasicStorage;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use Nette\Http\FileUpload;
use Nette\Utils\Image;
use Nette\Utils\ImageException;
use Nette\Utils\Strings;
use Nette\Utils\UnknownImageFileException;

final class LibraryImageModel
{

	public function __construct(
		private readonly Orm           $orm,
		private readonly ConfigService $configService,
		private readonly BasicStorage  $imageStorage,
		private readonly Repository  $esAllRepository,
	)
	{
	}

	/**
	 * @throws ImageException
	 * @throws UnknownImageFileException
	 */
	public function addFromFileUpload(FileUpload $file, int $cat, bool $isCopy = false, ?string $sourceImage = null, ?string $md5 = null): LibraryImage
	{
		$name = $file->getUntrustedName();
		$image = $this->addFromTmpFile($file->getTemporaryFile(), $cat, $name, $isCopy, $sourceImage, $md5);
		$this->orm->flush();
		return $image;
	}


	/**
	 * @throws ImageException
	 * @throws UnknownImageFileException
	 */
	public function addFromTmpFile(string $file, ?int $cat, ?string $name = null, bool $isCopy = false, ?string $sourceImage = null, ?string $md5 = null): LibraryImage
	{
		$fileName = $name ?? basename($file);
		$name = Strings::substring($fileName, 0, Strings::indexOf($fileName, '.', -1));

		$newImage = new LibraryImage();
		$newImage->name = $name;
		$newImage->library = $this->orm->libraryTree->getById($cat);

		$this->orm->libraryImage->persist($newImage);

		$filename = $newImage->id . '-' . $this->sanitize($fileName);
		$pathInfo = pathinfo($filename);
		$filenameWithoutExtension = $pathInfo['filename'];
		$extension = $pathInfo['extension'] ?? '';

		$originalPath = $this->imageStorage->getPathToOriginalImage($filenameWithoutExtension, $extension);

		$originalDir = dirname($originalPath);
		if (!file_exists($originalDir) && !mkdir($originalDir, 0755, true) && !is_dir($originalDir)) {
			throw new \RuntimeException(sprintf('Directory "%s" was not created', $originalDir));
		}

		copy($file, $originalPath);
		if (!$isCopy && $file !== ($originalPath)) {
			unlink($file);
		}

		if ($this->configService->get('imageOriginal', 'resize')) {
			// resize Orig Image - to save space on disk
			$NewImageWidth      = $this->configService->get('imageOriginal', 'width'); //New Width of Image
			$NewImageHeight     = $this->configService->get('imageOriginal', 'height'); // New Height of Image
			$Quality        = 95; //Image Quality
			$imagePath = $originalPath;
			$destPath = $originalPath;
			if (file_exists($imagePath) && @getimagesize($imagePath) !== false) {
				//Continue only if 2 given parameters are true
				//Image looks valid, resize.
				[$width, $height] = $this->resizeImage($imagePath, $destPath, $NewImageWidth, $NewImageHeight, $Quality);

				$newImage->width = $width;
				$newImage->height = $height;
			}
		}

		$newImage->filename = $filename;
		$newImage->sourceImage = $sourceImage;
		$newImage->md5 = $md5;
		$newImage->sort = -$newImage->id;

		$this->orm->libraryImage->persist($newImage);

		return $newImage;
	}

	private function sanitize(string $filename): string
	{
		$name = Strings::webalize($filename, '.', false);
		$name = str_replace(['-.', '.-'], '.', $name);
		$name = trim($name, '.-');
		$name = mb_strtolower($name);

		return $name === '' ? 'unknown' : $name;
	}

	// TODO REF presunout - mela by existovat jedna ovecna classa na resize obrazku

	/**
	 * @throws ImageException
	 * @throws UnknownImageFileException
	 */
	private function resizeImage(string $SrcImage, string $DestImage, int $MaxWidth, int $MaxHeight, int $Quality): array
	{
		$imageSize = getimagesize($SrcImage);
		\assert($imageSize !== false);

		[$iWidth, $iHeight] = $imageSize;

		if ($iWidth <= $MaxWidth && $iHeight <= $MaxHeight) {
			return [$iWidth, $iHeight];
		}

		$ImageScale  = min($MaxWidth / $iWidth, $MaxHeight / $iHeight);
		$NewWidth    = ceil($ImageScale * $iWidth);
		$NewHeight   = ceil($ImageScale * $iHeight);

		$image = Image::fromFile($SrcImage);
		$NewWidth = (int) $NewWidth;
		$NewHeight = (int) $NewHeight;
		$image->resize($NewWidth, $NewHeight);
		$image->save($DestImage, $Quality);

		return [$NewWidth, $NewHeight];
	}

    public function deleteImage(LibraryImage $image): void
    {
		$fileInfo = pathinfo($image->filename);

		if (isset($fileInfo['extension'])) {
			$this->imageStorage->deleteOriginal($fileInfo['filename'], $fileInfo['extension']);
			$this->imageStorage->deleteAllSizes($fileInfo['filename'], $fileInfo['extension']);
		}

		$this->orm->libraryImage->removeAndFlush($image);
    }


	public function getImageUsage(LibraryImage $image, Mutation $mutation): array
	{
		$esIndex = $this->orm->esIndex->getAllLastActive($mutation);
		$usage = [];
		if ($esIndex !== null) {
			$usage = array_map(
				function (\stdClass $data) {
					return $data->name;
				},
				$this->esAllRepository->searchImageUsage($esIndex, $image)
			);
		}
		return $usage;
	}

}
