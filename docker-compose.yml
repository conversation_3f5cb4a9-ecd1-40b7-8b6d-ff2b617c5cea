networks:
    sedlakova:
    superkoders:
        external: true

volumes:
    db:
    es:

services:
    app:
        build:
            dockerfile: docker/app/Dockerfile
            context: .
        hostname: app
        container_name: sedlakova_app
        restart: unless-stopped
        ports:
            - '8080:80'
        networks:
            - sedlakova
            - superkoders
        labels:
            - 'traefik.enable=true'
            - 'traefik.docker.network=superkoders'
            - 'traefik.http.routers.sedlakova.rule=Host(`sedlakova.superkoders.test`)'
            - 'traefik.http.routers.sedlakova.tls=true'
        volumes:
            - .:/var/www/html
            - ./docker/app/php-xdebug-${SUPERADMIN_XDEBUG:-off}.ini:/usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini
        depends_on:
            - db
            - es
            - front
            - admin

    front:
        build:
            dockerfile: docker/front/Dockerfile
            context: .
        container_name: sedlakova_front
        restart: unless-stopped
        networks:
            - sedlakova
        volumes:
            - .:/app

    admin:
        build:
            dockerfile: docker/admin/Dockerfile
            context: .
        container_name: sedlakova_admin
        restart: unless-stopped
        networks:
            - sedlakova
        volumes:
            - .:/app

    adminer:
        image: adminer
        restart: always
        networks:
            - sedlakova
        ports:
            - '81:8080'

    db:
        image: mariadb:10
        hostname: sedlakova_db
        container_name: sedlakova_db
        restart: unless-stopped
        networks:
            - sedlakova
        ports:
            - '3306:3306'
        volumes:
            - db:/var/lib/mysql
            - ./docker/db:/docker/db
            - ./docker/db/compare-db.sh:/docker/db/compare-db.sh
        environment:
            MARIADB_ROOT_PASSWORD: 'root'
            MARIADB_DATABASE: 'sedlakova'
            MARIADB_USER: 'sedlakova'
            MARIADB_PASSWORD: 'sedlakova'

    db-init:
        image: mariadb:10
        container_name: sedlakova_db-init
        depends_on:
            - db
        networks:
            - sedlakova
        volumes:
            - ./docker/db/init-db.sh:/docker/db/init-db.sh
            - ./docker/db:/docker/db
        environment:
            MARIADB_HOST: 'sedlakova_db'
            MARIADB_ROOT_PASSWORD: 'root'
            MARIADB_DATABASE: 'sedlakova'
            MARIADB_USER: 'sedlakova'
            MARIADB_PASSWORD: 'sedlakova'
        entrypoint: ['/docker/db/init-db.sh']

    es:
        image: elasticsearch:7.17.6
        hostname: sedlakova_es
        container_name: sedlakova_es
        restart: unless-stopped
        networks:
            - sedlakova
        ports:
            - '9200:9200'
        volumes:
            - es:/usr/share/elasticsearch/data
        environment:
            'discovery.type': single-node

    redis:
        image: redis:latest
        hostname: sedlakova_redis
        container_name: sedlakova_redis
        restart: unless-stopped
        networks:
            - sedlakova
        ports:
            - '6379:6379'

    mailcatcher:
        image: dockage/mailcatcher
        hostname: sedlakova_mailcatcher
        container_name: sedlakova_mailcatcher
        restart: unless-stopped
        networks:
            - sedlakova
        ports:
            - '1080:1080'
