<?php

declare(strict_types=1);

use function Deployer\{after, host, import, run, set, task, upload, writeln};

import('recipe/common.php');
import('contrib/cachetool.php');

set('shared_files', [
	'app/config/config.local.neon',
	'www/sitemap.xml',
	'www/sitemap-cs.xml',
]);

set('shared_dirs', [
	'www/data',
	'www/exports',
	'documents/fileLog',
]);

$env = getenv();
host('host')
	->set('hostname', $env['DEPLOYMENT_HOSTNAME'])
	->set('port', $env['DEPLOYMENT_PORT'] ?? 22)
	->set('remote_user', $env['DEPLOYMENT_USER'])
	->set('deploy_path', $env['DEPLOYMENT_PATH'])
	->set('cachetool_args', sprintf(
		'--web=SymfonyHttpClient --web-path=%s --web-url=%s',
		escapeshellarg(sprintf('%s/current/www', rtrim($env['DEPLOYMENT_PATH'], '/'))),
		escapeshellarg($env['DEPLOYMENT_URL']),
	))
	->set('php_version', $env['DEPLOYMENT_PHP_VERSION']);

const TASK_COPY_APPLICATION = 'copy-application';
task(TASK_COPY_APPLICATION, function (): void {
	$config = ['options' => [ "--exclude-from=excludeFromDeploy" ]];
	upload('.', '{{release_path}}', $config);
});

const TASK_CLEAR_CACHE = 'clear-cache';
task(TASK_CLEAR_CACHE, function (): void {
	run('{{bin/php}} {{release_path}}/bin/console contributte:cache:clean');
});

const TASK_RUN_MIGRATIONS = 'run-migrations';
task(TASK_RUN_MIGRATIONS, function () use ($env): void {
	if (isset($env['resetDataStorage']) && trim($env['resetDataStorage']) === '1') {
		writeln('Reset DB & Elasticsearch');
		run('{{bin/php}} {{release_path}}/bin/console migrations:reset');
		run('{{bin/php}} {{release_path}}/bin/console elastic:index:purge -f');
		run('{{bin/php}} {{release_path}}/bin/console elastic:index:create -psc');
	} else {
		writeln('Update DB scheme');
		run('{{bin/php}} {{release_path}}/bin/console migrations:continue');
	}

});

const TASK_WARM_UP_CACHE = 'warm-up-cache';
task(TASK_WARM_UP_CACHE, function (): void {
	run('{{bin/php}} {{release_path}}/bin/console contributte:cache:generate');
});

set('cachetool_url', 'https://github.com/gordalina/cachetool/releases/download/9.1.0/cachetool.phar');

task('deploy', [
	// prepare
	'deploy:info',
	'deploy:setup',
	'deploy:lock',
	'deploy:release',

	// copy
	'deploy:copy_dirs',
	TASK_COPY_APPLICATION,

	// set up files and directories
	'deploy:shared',
	'deploy:writable',

	// todo maintenance mode?
	TASK_RUN_MIGRATIONS,
	TASK_WARM_UP_CACHE,


	// switch to deployed version
	'deploy:symlink',

	// cleanup
	'deploy:unlock',
	'deploy:cleanup',
	'deploy:success',
]);

after('deploy:symlink', 'cachetool:clear:opcache');
after('deploy:symlink', 'cachetool:clear:stat');
after('deploy:symlink', TASK_CLEAR_CACHE);

after('deploy:failed', 'deploy:unlock');
