-- MySQL dump 10.13  Distrib 5.7.39-42, for debian-linux-gnu (x86_64)
--
-- Host: 127.0.0.1    Database: 1691_sedlak_st
-- ------------------------------------------------------
-- Server version	5.7.39-42-log

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
/*!50717 SELECT COUNT(*) INTO @rocksdb_has_p_s_session_variables FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = 'performance_schema' AND TABLE_NAME = 'session_variables' */;
/*!50717 SET @rocksdb_get_is_supported = IF (@rocksdb_has_p_s_session_variables, 'SELECT COUNT(*) INTO @rocksdb_is_supported FROM performance_schema.session_variables WHERE VARIABLE_NAME=\'rocksdb_bulk_load\'', 'SELECT 0') */;
/*!50717 PREPARE s FROM @rocksdb_get_is_supported */;
/*!50717 EXECUTE s */;
/*!50717 DEALLOCATE PREPARE s */;
/*!50717 SET @rocksdb_enable_bulk_load = IF (@rocksdb_is_supported, 'SET SESSION rocksdb_bulk_load = 1', 'SET @rocksdb_dummy_bulk_load = 0') */;
/*!50717 PREPARE s FROM @rocksdb_enable_bulk_load */;
/*!50717 EXECUTE s */;
/*!50717 DEALLOCATE PREPARE s */;

--
-- Table structure for table `alias`
--

DROP TABLE IF EXISTS `alias`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `alias` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `alias` varchar(120) CHARACTER SET utf8mb4 NOT NULL,
  `module` varchar(30) NOT NULL,
  `referenceId` int(11) NOT NULL,
  `mutationId` int(11) NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  UNIQUE KEY `alias_lg` (`alias`,`mutationId`),
  UNIQUE KEY `idref_modul_lg` (`referenceId`,`module`,`mutationId`) USING BTREE,
  KEY `idref` (`referenceId`),
  KEY `mutationId` (`mutationId`),
  KEY `modul` (`module`) USING BTREE,
  CONSTRAINT `alias_ibfk_1` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=2305 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `alias`
--

LOCK TABLES `alias` WRITE;
/*!40000 ALTER TABLE `alias` DISABLE KEYS */;
INSERT INTO `alias` VALUES (75,'hledat','tree',42,1),(128,'obchodni-podminky','tree',40,1),(166,'profil','tree',75,1),(167,'prohlaseni-o-pouzivani-cookies','tree',12,1),(172,'registrace','tree',76,1),(211,'prihlaseny','tree',72,1),(219,'zapomenute-heslo','tree',74,1),(220,'e-shop','tree',21,1),(224,'prihlaseni','tree',37,1),(229,'reset-hesla','tree',26,1),(231,'faq','tree',8,1),(247,'produktova-kategorie-2','tree',78,1),(442,'noze','tree',94,1),(454,'o-nas','tree',102,1),(604,'zpracovanim-osobnich-udaju','tree',255,1),(615,'stranka-nenalezena','tree',262,1),(2107,'popupcontact','tree',394,1),(2110,'produktova-kategorie-1','tree',77,1),(2112,'','tree',1,1),(2139,'system-pages-only-develpers','tree',398,1),(2145,'odhlasit','tree',405,1),(2159,'zmena-hesla','tree',413,1),(2197,'styleguide','tree',445,1),(2198,'clanek-1','blogLocalization',1,1),(2199,'blog','tree',446,1),(2202,'tag-super','blogTagLocalization',1,1),(2203,'nazev-dalsiho-clanku','blogLocalization',2,1),(2204,'tag-0','blogTagLocalization',2,1),(2205,'blog-0-5','blogTagLocalization',3,1),(2206,'tomas-fuk','authorLocalization',1,1),(2207,'sluzby','tree',449,1),(2222,'jana-sedlakova','authorLocalization',2,1),(2226,'','tree',2,2),(2227,'produkt-6','productLocalization',1,1),(2228,'product-6','productLocalization',2,2),(2229,'test-product','productLocalization',5,2),(2230,'produkt-7','productLocalization',6,1),(2231,'product-7','productLocalization',7,2),(2232,'produkt-8','productLocalization',8,1),(2233,'test-11-en','productLocalization',9,2),(2234,'produkt-9','productLocalization',10,1),(2235,'test-product-7','productLocalization',13,2),(2236,'produkt-5','productLocalization',14,1),(2237,'test-product-6','productLocalization',15,2),(2238,'produkt-4','productLocalization',16,1),(2239,'test-product-5','productLocalization',17,2),(2240,'produkt-2','productLocalization',18,1),(2241,'test-product-4','productLocalization',19,2),(2242,'vzduchovka-hammerli-hunter-force-900','productLocalization',20,1),(2243,'vzduchovka-en','productLocalization',21,2),(2244,'vzduchovka-kral-arms','productLocalization',22,1),(2245,'test-product-3','productLocalization',25,2),(2246,'test-mazani','productLocalization',26,1),(2247,'test-product-2','productLocalization',27,2),(2248,'nazev','productLocalization',28,1),(2249,'predkosik','tree',450,1),(2250,'kosik','tree',451,1),(2251,'objednavka-krok-1','tree',452,1),(2252,'objednavka-krok-2','tree',453,1),(2253,'objednavka-dokoncena','tree',454,1),(2254,'moje-adresy','tree',455,1),(2255,'historie-objednavek','tree',456,1),(2256,'historie-objednavek-detail','tree',457,1),(2257,'pro-koho','tree',458,1),(2258,'kariera','tree',459,1),(2259,'reference','tree',460,1),(2260,'akce','tree',461,1),(2261,'kontakt','tree',462,1),(2262,'e-book','tree',463,1),(2263,'podcast','tree',464,1),(2264,'softwarove-pravo','tree',465,1),(2265,'kyberneticka-bezpecnost','tree',466,1),(2266,'pravo-dusevniho-vlastnictvi','tree',467,1),(2267,'nemovitosti','tree',468,1),(2268,'korporatni-pravo','tree',469,1),(2269,'pracovni-pravo','tree',470,1),(2270,'velke-a-stredni-firmy','tree',471,1),(2271,'technologicke-firmy','tree',472,1),(2272,'startupy-a-investori','tree',473,1),(2273,'realitni-kancelare-a-developeri','tree',474,1),(2274,'vyvojari-a-it','tree',475,1),(2275,'marketing','tree',476,1),(2276,'hr-a-backoffice','tree',477,1),(2277,'sales-a-finance','tree',478,1),(2278,'security-a-compliance','tree',479,1),(2279,'legal','tree',480,1),(2280,'vnitrni-oznamovaci-system','tree',482,1),(2281,'zahranici','tree',483,1),(2282,'rozcestnik-komponent','tree',484,1),(2283,'partner','blogTagLocalization',4,1),(2284,'tag-ref','blogTagLocalization',5,1),(2285,'test-reference','referenceLocalization',1,1),(2286,'tag-mat','blogTagLocalization',7,1),(2287,'test-mat','materialLocalization',1,1),(2288,'pavel-cech','authorLocalization',3,1),(2289,'korporatni-pravo-1','blogTagLocalization',10,1),(2290,'roman-tomek','authorLocalization',4,1),(2291,'michaela-puskar-garajova','authorLocalization',5,1),(2292,'jiri-hradsky','authorLocalization',6,1),(2293,'david-supej','authorLocalization',7,1),(2294,'yarmill-jak-ziskat-legal-safe-data-pro-ai-asistenta','referenceLocalization',2,1),(2296,'it-pravo-a-pravo-tmt-1','featureLocalization',2,1),(2297,'lorem-ipsum','referenceLocalization',3,1),(2298,'test','featureLocalization',3,1),(2299,'sluzby-1','blogTagLocalization',9,1),(2300,'it-pravo-a-pravo-tmt','tree',485,1),(2301,'jak-jsme-pomohli-forendors-s-rebrandingem','blogLocalization',3,1),(2302,'yarmill-jak-ziskat-legal-safe-data-pro-ai-asistenta-1','blogLocalization',4,1),(2303,'advokat-ka-na-it-pravo-a-smluvni-dokumentaci','tree',486,1),(2304,'jak-jsme-pomohli-forendors-s-rebrandingem-1','materialLocalization',2,1);
/*!40000 ALTER TABLE `alias` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `alias_history`
--

DROP TABLE IF EXISTS `alias_history`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `alias_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `alias` varchar(120) CHARACTER SET utf8mb4 NOT NULL,
  `module` varchar(30) NOT NULL,
  `referenceId` int(11) NOT NULL,
  `mutationId` int(11) NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  UNIQUE KEY `alias_lg` (`alias`,`mutationId`),
  UNIQUE KEY `alias_modul_idref_lg` (`alias`,`module`,`referenceId`,`mutationId`) USING BTREE,
  KEY `mutationId` (`mutationId`),
  CONSTRAINT `alias_history_ibfk_1` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `alias_history`
--

LOCK TABLES `alias_history` WRITE;
/*!40000 ALTER TABLE `alias_history` DISABLE KEYS */;
INSERT INTO `alias_history` VALUES (6,'it-pravo-a-pravo-tmt','featureLocalization',2,1),(5,'it-pravo-a-pravo-tmt-1','tree',485,1),(7,'it-pravo-a-pravo-tmt-2','tree',485,1),(2,'jana-sedlakova','authorLocalization',3,1),(4,'michela','authorLocalization',5,1),(3,'pepik-nepovim','authorLocalization',2,1),(1,'tag-autor','blogTagLocalization',4,1);
/*!40000 ALTER TABLE `alias_history` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `api_token`
--

DROP TABLE IF EXISTS `api_token`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `api_token` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `token` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `issuerId` int(11) NOT NULL,
  `issuedAt` datetime NOT NULL,
  `expiresAt` datetime DEFAULT NULL,
  `revokedAt` datetime DEFAULT NULL,
  `scope` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  PRIMARY KEY (`id`),
  KEY `issuerId` (`issuerId`),
  CONSTRAINT `api_token_ibfk_1` FOREIGN KEY (`issuerId`) REFERENCES `user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `api_token`
--

LOCK TABLES `api_token` WRITE;
/*!40000 ALTER TABLE `api_token` DISABLE KEYS */;
/*!40000 ALTER TABLE `api_token` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `author`
--

DROP TABLE IF EXISTS `author`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `author` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `internalName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `author`
--

LOCK TABLES `author` WRITE;
/*!40000 ALTER TABLE `author` DISABLE KEYS */;
INSERT INTO `author` VALUES (1,'Jan Novák','{}'),(2,'Jana Sedláková','{\"base\":[{\"mainImage\":\"170\",\"email\":\"<EMAIL>\",\"phone\":\"+420733555958\",\"socials\":[{\"link\":[{\"toggle\":\"customHref\",\"customHref\":[{\"href\":\"https://www.linkedin.com/in/jana-sedl%C3%A1kov%C3%A1-46a919122/\",\"hrefName\":\"LinkedIn\"}]}]},{\"link\":[{\"toggle\":\"customHref\",\"customHref\":[{\"href\":\"https://www.calendly.com/jana-sedlakovalegal\",\"hrefName\":\"Calendly\"}]}]}]}]}'),(3,'Pavel Čech','{\"base\":[{\"mainImage\":\"149\"}]}'),(4,'Roman Tomek','{\"base\":[{\"mainImage\":\"168\"}]}'),(5,'Michaela Puškár Garajová','{\"base\":[{\"mainImage\":\"169\"}]}'),(6,'Jiří Hradský','{\"base\":[{\"mainImage\":\"166\"}]}'),(7,'David Šupej','{\"base\":[{\"mainImage\":\"167\"}]}');
/*!40000 ALTER TABLE `author` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `author_localization`
--

DROP TABLE IF EXISTS `author_localization`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `author_localization` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mutationId` int(11) NOT NULL,
  `authorId` int(11) NOT NULL,
  `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `nameAnchor` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `nameTitle` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `keywords` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `public` int(11) NOT NULL DEFAULT '0',
  `forceNoIndex` int(11) NOT NULL DEFAULT '0',
  `hideInSearch` int(11) NOT NULL DEFAULT '0',
  `hideInSitemap` int(11) NOT NULL DEFAULT '0',
  `publicFrom` datetime DEFAULT NULL,
  `publicTo` datetime DEFAULT NULL,
  `edited` int(11) DEFAULT NULL,
  `editedTime` datetime DEFAULT NULL,
  `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `customContentJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `mutationId_authorId` (`mutationId`,`authorId`),
  KEY `FK_author_mutation` (`mutationId`) USING BTREE,
  KEY `FK_author_localization_author` (`authorId`) USING BTREE,
  CONSTRAINT `FK_author_localization_author` FOREIGN KEY (`authorId`) REFERENCES `author` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `FK_author_localization_mutation` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `author_localization`
--

LOCK TABLES `author_localization` WRITE;
/*!40000 ALTER TABLE `author_localization` DISABLE KEYS */;
INSERT INTO `author_localization` VALUES (1,1,1,'Jan Novák','Jan Novák','Jan Novák','','',1,0,0,0,NULL,NULL,4,'2025-05-22 09:22:32','{}','{}'),(2,1,2,'Jana Sedláková','Jana Sedláková','Jana Sedláková','','',1,0,0,0,NULL,NULL,34,'2025-06-10 12:54:57','{\"base\":[{\"content\":\"<p>SEDLAKOVA LEGAL vznikla v roce 2015 jako odpověď na otázku, zda právo dokáže držet krok se změnami kolem nás. Nové technologie a inovace posouvají naše chápání světa do jiné roviny. Zrychluje se komunikace, transakce se stávají průhlednějšími a “BIG DATA” se stala synonymem pro úspěch. To, co dnes není real-time, je pozdě. Nové trendy velí být dostupní, struční, mít nadhled a držet krok s pokrokem.</p>\\n<p>Mnoho lidí touží po tom, změnit svět. Našim cílem je pomoci těm, kteří to dokážou, přeměnit jejich nápad v udržitelnou hodnotu. Nápadem a tvrdou dřinou totiž každá změna začíná.</p>\\n<h2>Čemu se v rámci kanceláře věnuji:</h2>\\n<ul>\\n<li>Zajištění chodu kanceláře</li>\\n<li>IT a softwarové právo</li>\\n<li>Korporátní právo, due diligence</li>\\n<li>Investice</li>\\n<li>Zakladatelka projektu <a rel=\\\"noopener\\\" href=\\\"http://pravovrousce.cz/\\\" target=\\\"_blank\\\">Právo v roušce ↗</a></li>\\n</ul>\\n<h2>Publikační činnost:</h2>\\n<ul>\\n<li>Příručka <a rel=\\\"noopener\\\" href=\\\"https://www.martinus.cz/?uItem=860641&amp;utm_medium=product&amp;utm_source=zbozi.cz\\\" target=\\\"_blank\\\">Softwarové smlouvy. Jejich specifika a kontraktační proces ↗</a></li>\\n<li>Článek <a href=\\\"https://www.lidovky.cz/byznys/nejsme-advokatni-skolka-sedlakova-veri-ze-i-pravnici-mohou-prispet-ke-zlepseni-sveta.A190110_142055_ln_byznys_pravo_ssu\\\">‚Nejsme advokátní školka.‘ Sedláková věří, že i právníci mohou přispět ke zlepšení světa ↗</a></li>\\n<li>Videopřednáška na téma <a href=\\\"https://www.epravo.cz/eshop/geometricky-plan-a-jeho-pouziti-pred-katastrem-a-u-soudu-281.html\\\">Geometrický plán a jeho použití před katastrem a u soudu ↗</a></li>\\n<li>Videopřednáška na téma <a href=\\\"https://www.epravo.cz/eshop/zapisy-prav-do-katastru-nemovitosti-280.html\\\">Zápisy práv do katastru nemovitostí ↗</a></li>\\n<li>Rozhovor v pořadu <a href=\\\"https://www.ceskatelevize.cz/porady/10435049455-dobre-rano/320291310020040/\\\">České televize – Dobré ráno ↗</a></li>\\n<li>Článek <a href=\\\"https://www.linkedin.com/pulse/coronavirus-its-implications-employers-employees-czech-jana-sedl%C3%A1kov%C3%A1/\\\">Coronavirus and its implications for employers and employees in the Czech ↗</a></li>\\n</ul>\"}]}','{}'),(3,1,3,'Pavel Čech','Pavel Čech','Pavel Čech','','',1,0,0,0,'2025-05-23 02:32:00','2125-05-23 02:32:00',34,'2025-05-30 08:49:14','{}','{}'),(4,1,4,'Roman Tomek','Roman Tomek','Roman Tomek','','',1,0,0,0,'2025-05-23 03:28:00','2125-05-23 03:28:00',34,'2025-06-10 12:50:55','{}','{}'),(5,1,5,'Michaela Puškár Garajová','Michaela Puškár Garajová','Michaela Puškár Garajová','','',1,0,0,0,'2025-05-23 03:29:00','2125-05-23 03:29:00',34,'2025-06-10 12:54:32','{}','{}'),(6,1,6,'Jiří Hradský','Jiří Hradský','Jiří Hradský','','',1,0,0,0,'2025-05-27 09:28:00','2125-05-27 09:28:00',34,'2025-06-10 12:47:12','{}','{}'),(7,1,7,'David Šupej','David Šupej','David Šupej','','',1,0,0,0,'2025-05-27 09:30:00','2125-05-27 09:30:00',34,'2025-06-10 12:48:32','{}','{}');
/*!40000 ALTER TABLE `author_localization` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `author_x_blog_tag`
--

DROP TABLE IF EXISTS `author_x_blog_tag`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `author_x_blog_tag` (
  `authorId` int(11) NOT NULL,
  `blogTagId` int(11) NOT NULL,
  PRIMARY KEY (`authorId`,`blogTagId`) USING BTREE,
  KEY `FK__blog` (`authorId`) USING BTREE,
  KEY `FK__blog_tag` (`blogTagId`) USING BTREE,
  CONSTRAINT `author_x_blog_tag_ibfk_1` FOREIGN KEY (`authorId`) REFERENCES `author` (`id`) ON DELETE CASCADE,
  CONSTRAINT `author_x_blog_tag_ibfk_2` FOREIGN KEY (`blogTagId`) REFERENCES `blog_tag` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `author_x_blog_tag`
--

LOCK TABLES `author_x_blog_tag` WRITE;
/*!40000 ALTER TABLE `author_x_blog_tag` DISABLE KEYS */;
INSERT INTO `author_x_blog_tag` VALUES (2,4),(2,10),(3,4),(4,4),(4,10);
/*!40000 ALTER TABLE `author_x_blog_tag` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `blog`
--

DROP TABLE IF EXISTS `blog`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `blog` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `internalName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `blog`
--

LOCK TABLES `blog` WRITE;
/*!40000 ALTER TABLE `blog` DISABLE KEYS */;
INSERT INTO `blog` VALUES (1,'Co když AI rozhoduje o cílech nebo trasách dronů?','{\"base\":[{\"mainImage\":\"156\"}]}'),(2,'test 2',NULL),(3,'Jak jsme pomohli Forendors  s rebrandingem','{\"base\":[{\"mainImage\":\"155\"}]}'),(4,'Yarmill: Jak získat legal-safe  data pro AI asistenta','{\"base\":[{\"mainImage\":\"156\"}]}');
/*!40000 ALTER TABLE `blog` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `blog_localization`
--

DROP TABLE IF EXISTS `blog_localization`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `blog_localization` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mutationId` int(11) NOT NULL,
  `blogId` int(11) NOT NULL,
  `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `nameAnchor` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `nameTitle` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `keywords` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `isTop` int(11) NOT NULL DEFAULT '0',
  `public` int(11) NOT NULL DEFAULT '0',
  `forceNoIndex` int(11) NOT NULL DEFAULT '0',
  `hideInSearch` int(11) NOT NULL DEFAULT '0',
  `hideInSitemap` int(11) NOT NULL DEFAULT '0',
  `publicFrom` datetime DEFAULT NULL,
  `publicTo` datetime DEFAULT NULL,
  `edited` int(11) DEFAULT NULL,
  `editedTime` datetime DEFAULT NULL,
  `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `customContentSchemeJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `customContentJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `viewsNumber` int(10) unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `FK_blog_mutation` (`mutationId`) USING BTREE,
  KEY `FK_blog_localization_blog` (`blogId`) USING BTREE,
  CONSTRAINT `FK_blog_localization_blog` FOREIGN KEY (`blogId`) REFERENCES `blog` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `FK_blog_mutation` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `blog_localization`
--

LOCK TABLES `blog_localization` WRITE;
/*!40000 ALTER TABLE `blog_localization` DISABLE KEYS */;
INSERT INTO `blog_localization` VALUES (1,1,1,'Co když AI rozhoduje o cílech nebo trasách dronů?','Co když AI rozhoduje o cílech nebo trasách dronů?','Co když AI rozhoduje o cílech nebo trasách dronů?','','',0,1,0,0,0,'2021-08-16 16:39:00','2121-08-16 16:39:00',34,'2025-05-30 10:34:49','{\"base\":[{\"annotation\":\"Umělá inteligence (AI) výrazně rozšiřuje možnosti moderního vojenství: od logistiky, přes autonomní systémy, až po kybernetické útoky a dezinformační kampaně. Autonomní prostředky už dnes pomáhají s terénním průzkumem, rozpoznáváním hrozeb nebo zásobováním. Technologie se vyvíjejí rychleji než zákony, obzvlášť v oblasti obranného průmyslu. Právě tady totiž může jít o život.\"}]}','{}','{\"content____W1EHrgRQvLLyz_OMnEXa5\":[{\"content\":\"<p><strong>V tuto chvíli ale neexistuje jednotná globální úprava AI v obraně.</strong><span> </span>Proto se tento článek zaměřuje na to, co aktuálně říká právo: jak to má (nebo nemá) ošetřeno Evropská unie, jak se k tomu staví mezinárodní právo a co dělají Spojené státy. Etické otázky necháme stranou. Zajímá nás čistě to, kdo nese odpovědnost a co platí pro vývojáře, firmy a státní správu. I když je problematika poměrně složitá, chceme vám tady předat srozumitelný přehled pro prvotní porozumění tématu.?</p>\\n<blockquote>Máte konkrétní dotaz, nebo potřebujete jít víc do hloubky? Není problém,<span> </span><a rel=\\\"noopener\\\" href=\\\"https://calendly.com/pavelc-sedlakovalegal/it-konzultace?month=2025-05\\\" target=\\\"_blank\\\">domluvte si s námi schůzku</a>.</blockquote>\\n<h2 class=\\\"h2\\\">EU: AI Act se obrany (skoro) netýká</h2>\\n<p>Když se řekne „AI regulace v EU“, většina si vybaví nové nařízení<span> </span><strong>AI Act</strong>. Ale pozor, ten se na vojenské technologie téměř nevztahuje. V AI Actu je výslovně uvedeno, že systémy určené <strong>výhradně</strong><span> </span><strong>pro vojenské nebo národní bezpečnostní účely</strong> z působnosti zákona vypadávají.</p>\\n<p><em>Konkrétně článek 2 § 3 AI Act vyjímá ze své působnosti „systémy AI, které jsou určeny výhradně pro vojenské, obranné nebo národní bezpečnostní účely, bez ohledu na typ subjektu, který tyto činnosti vykonává“.</em></p>\\n<p>? Klíč k výkladu této výjimky spočívá právě v termínu „výhradně“. Pro firmy to v praxi znamená, že<span> </span><strong>vývoj AI pro armádu se sice vymyká hlavnímu rámci AI Actu, ale jen pokud jde skutečně o čistě obranné využití</strong>. Jakmile se systém použije i v civilní sféře (např. při humanitární pomoci), může spadnout pod běžnou regulaci a s tím přichází i povinnosti jako hodnocení rizik nebo transparentnost algoritmů.</p>\\n<p>Kromě AI Actu však Evropský parlament přijímá i nezávazné rezoluce. V těch zastává spíše zdrženlivý postoj, ale zároveň razí princip tzv. <strong>významné lidské kontroly</strong>, a to jako minimální standard pro autonomní zbraně.</p>\\n<p><em><strong>Lethal Autonomous Weapon Systems</strong><span> </span>(LAWS) jsou smrtící autonomní zbraňové systémy, které dokážou bez lidského zásahu samy vybrat a zničit cíl. Jde o technologii, která v sobě spojuje AI a schopnost smrtícího zásahu. Právní i etické debaty se soustředí na to, zda by měl mít člověk vždy poslední slovo při rozhodnutí o použití síly – tomu se říká<span> </span><strong>meaningful human control</strong>.</em></p>\\n<p>Zákaz smrtících autonomních systémů sice zatím nepřišel, ale debata se vede dál, třeba na půdě OSN pod hlavičkou<span> </span><strong>Úmluvy o některých konvenčních zbraních</strong><span> </span>(Convention on Certain Conventional Weapons, CCW).</p>\\n<blockquote>Ztrácíte se v evropských regulacích? Pomůžeme vám lépe se zorientovat v AI Actu s ohledem na váš byznys.<span> </span><a rel=\\\"noopener\\\" href=\\\"https://www.sedlakovalegal.cz/cs/kontakt\\\" target=\\\"_blank\\\">Napište nám</a>.</blockquote>\\n<h2 class=\\\"h2\\\">Mezinárodní právo: žádná výmluva na chybu algoritmu</h2>\\n<p>Mezinárodní humanitární právo (IHL) mluví jasně: za vojenské akce odpovídá stát. IHL definuje také individuální trestní odpovědnost a ukládá povinnosti lidem – vojákům a velitelům, kteří používají zbraně a rozhodují o útocích.<span> </span><strong>Právní odpovědnost za dodržování těchto pravidel nelze přenést na stroj, algoritmus ani autonomní systém.</strong><span> </span>A pokud takový zásah poruší pravidla mezinárodního humanitárního práva, může jít i o válečný zločin.</p>\\n<p>Právo přitom klade důraz na tři zásady:</p>\\n<ul>\\n<li><strong>rozlišení<span> </span></strong>mezi civilními a vojenskými cíli,</li>\\n<li><strong>proporcionalitu<span> </span></strong>(tedy přiměřenost útoku vůči očekávanému vojenskému zisku),</li>\\n<li>a <strong>opatrnost<span> </span></strong>při plánování a provádění útoků.</li>\\n</ul>\\n<p>☝️ Ti, kdo plánují, rozhodují a provádějí útok pomocí autonomního zbraňového systému, musí zajistit, že tento zbraňový systém a způsob jeho použití zachovávají jejich schopnost činit potřebná právní rozhodnutí a tím zajistit soulad s mezinárodním humanitárním právem. Pokud AI systém funguje tak, že znemožňuje operátorovi tato pravidla reálně vyhodnotit a aplikovat, pak jeho nasazení samo o sobě může být protiprávní.</p>\\n<p>A co víc – státy jsou povinny dělat <strong>právní přezkum nových zbraní</strong> (dle čl. 36 Dodatkového protokolu I k Ženevským úmluvám). Možnost provést takový přezkum předpokládá<strong> </strong><strong>plné pochopení schopností zbraně a předvídání jejích účinků</strong>, zejména prostřednictvím ověřování a testování.</p>\\n<p>Otázkou však zůstává, jaká omezení autonomie ve zbraňových systémech jsou nezbytná pro zajištění souladu s mezinárodním humanitárním právem. Mezi smluvními státy Úmluvy o některých konvenčních zbraních (CCW) panuje obecná shoda, že nad zbraňovými systémy a použitím síly musí být zachována významná lidská kontrola, nebo „odpovídající úroveň lidského úsudku“.</p>\\n<p>? Shrnutí předsedy setkání odborníků v rámci CCW z dubna 2016 uvádí následující:<span> </span><em>Názory na vhodnou míru lidské účasti v souvislosti se smrtící silou a otázkou delegace jejího použití jsou zásadní pro další posuzování LAWS (smrtících autonomních zbraňových systémů).</em></p>\\n<h2 class=\\\"h2\\\">USA: etika, schvalování a robustní testování</h2>\\n<p>Americké ministerstvo obrany (DoD) se k autonomním zbraním staví aktivně. Vývoj a nasazení smrtících autonomních zbraní přímo nezakazuje, ale stanovuje jasná pravidla. Klíčový dokument je <strong>Direktiva ministerstva obrany 3000.09<span> </span></strong>naposledy aktualizovaná v roce 2023.</p>\\n<h3>Zásady nasazování autonomních zbraňových systémů podle DoD</h3>\\n<p>Autonomní a poloautonomní zbraňové systémy musí být navrženy tak, aby umožňovaly velitelům a operátorům uplatňovat odpovídající lidský úsudek při použití síly.</p>\\n<ol>\\n<li><span>Testování, validace a nasazení</span><span> </span>?</li>\\n</ol>\\n<p class=\\\"par\\\">Každý systém musí projít <strong>důkladným ověřením hardwaru a softwaru</strong>, stejně jako realistickým testováním v podmínkách, které odpovídají skutečnému nasazení. Kromě toho se stanoví taktiky, výcvik a operační postupy, které zajistí, že:</p>\\n<ul>\\n<li>systém bude <strong>spolehlivě fungovat v reálných bojových podmínkách</strong>, včetně protiopatření ze strany protivníka,</li>\\n<li>zásah proběhne <strong>v souladu se záměrem velitele</strong>, v konkrétním čase a místě, nebo bude přerušen, pokud toho systém není schopen,</li>\\n<li>systém má být schopen<span> </span><strong>vyžádat si další input od člověka</strong>, pokud nedokáže zásah dokončit podle záměru,</li>\\n<li>systém bude <strong>dostatečně robustní</strong>, aby minimalizoval selhání a jejich důsledky.<strong> </strong></li>\\n</ul>\\n<ol>\\n<li><strong></strong><span>Bezpečnost a ovladatelnost</span><span> </span>?️</li>\\n</ol>\\n<p>Systémy musí být odolné vůči <strong>neoprávněnému zásahu a kybernetickým hrozbám</strong>. To zahrnuje:</p>\\n<ul>\\n<li>implementaci <strong>bezpečnostních a ochranných prvků</strong> dle technických standardů (např. DoDI 8500.01, MIL-STD-882E),</li>\\n<li><strong>transparentní a auditovatelné technologie a datové zdroje</strong>, srozumitelné pro odpovědný personál,</li>\\n<li><strong>intuitivní rozhraní</strong>, které operátorům umožní jasně rozpoznat, co dělá systém a co je jejich odpovědnost.</li>\\n</ul>\\n<ol>\\n<li><strong></strong><span>Rozhraní člověk–stroj</span><span> </span>?</li>\\n</ol>\\n<p>Pro zajištění správného rozhodování musí být rozhraní:</p>\\n<ul>\\n<li><strong>srozumitelné a předvídatelné</strong> pro vyškolené uživatele,</li>\\n<li>schopné <strong>poskytovat průběžnou zpětnou vazbu o stavu systému</strong>,</li>\\n<li>vybavené jasnými možnostmi pro <strong>zapnutí a vypnutí funkcí systému</strong> podle potřeby.</li>\\n</ul>\\n<blockquote><strong>Vyvíjíte AI pro armádu nebo bezpečnostní sektor? ?<span> </span></strong>Pomůžeme vám nastavit právní rámec, odpovědnosti a dokumentaci tak, abyste byli připraveni na kontrolu i mezinárodní spolupráci.</blockquote>\\n<p>Návrh, vývoj, nasazení a používání AI schopností v autonomních a poloautonomních zbraňových systémech se řídí také<span> </span><strong>Etickými principy Ministerstva obrany USA</strong><span> </span>(DoD AI Ethical Principles) a strategií odpovědného využití AI. Principy jsou následující:</p>\\n<ul>\\n<li>Responsible (odpovědnost).</li>\\n<li>Equitable (nestrannost).</li>\\n<li>Traceable (dohledatelnost).</li>\\n<li>Reliable (spolehlivost).</li>\\n<li>Governable (řiditelnost).</li>\\n</ul>\\n<p>A aby to nebylo málo, každý nově vyvíjený autonomní systém musí získat <strong>schválení od tří nejvyšších úředníků Pentagonu</strong><span> </span>ještě před vývojem a znovu před nasazením.</p>\\n<h2 class=\\\"h2\\\">Co z toho plyne pro vývojáře a firmy? ?</h2>\\n<p>Pokud pracujete na technologiích pro autonomní drony nebo jiné systémy v obraně, ať už jako dodavatel, vývojář nebo konzultant, je dobré myslet na těchto pár klíčových věcí:</p>\\n<ul>\\n<li>AI Act vás musí začít zajímat, pokud váš systém není<span> </span><strong>výhradně vojenský, ale dotýká se i civilní sféry</strong>.</li>\\n<li>Mezinárodní právo vás nenechá v klidu ani jako externího partnera,<span> </span><strong>odpovědnost vždy někde leží</strong>.</li>\\n<li>Etika není jen „nice to have“ –<span> </span><strong>v USA je dokonce součástí oficiálních směrnic</strong>.</li>\\n<li>A pozor na <strong>duální použití.<span> </span></strong>Systém vyvinutý pro armádu může najednou spadnout pod civilní regulaci, pokud ho použijete i jinde.</li>\\n</ul>\\n<blockquote>Máte AI systém, který by mohl být<span> </span><strong>dual-use</strong>? Nebo vyvíjíte technologie pro armádu a chcete mít jistotu, že jste krytí i právně?<span> </span><a rel=\\\"noopener\\\" href=\\\"https://www.sedlakovalegal.cz/cs/kontakt\\\" target=\\\"_blank\\\">Probereme to</a>, aby se z pokroku nestal problém.</blockquote>\\n<h2 class=\\\"h2\\\">Umělá inteligence ve zbraních: budoucnost, která už přišla</h2>\\n<p>AI v obranných systémech přináší obrovský potenciál, ale i zásadní právní rizika. V EU se regulace drží stranou, ale stále platí mezinárodní pravidla. USA jdou zase cestou přísné kontroly, testování a etických principů.</p>\\n<h3 class=\\\"h3\\\">Na co si dát pozor?</h3>\\n<p>✅ Pochopit, kdy se na AI systém vztahuje evropská regulace.</p>\\n<p>✅ Vyhodnotit právní odpovědnost ještě před nasazením, včetně té osobní.</p>\\n<p>✅ Neopomenout přezkoumání, dokumentaci a auditovatelnost systému.</p>\\n<p>✅ Vědět, co bude s AI, když se přepne z vojenské na civilní pomoc.</p>\"}]}',52),(2,1,2,'Název dalšího článku','Název dalšího článku','Název dalšího článku','','',0,1,0,0,0,'2021-08-19 13:17:00','2121-08-19 13:17:00',5,'2021-08-24 14:24:12','{}','{}','{}',9),(3,1,3,'Jak jsme pomohli Forendors  s rebrandingem','Jak jsme pomohli Forendors  s rebrandingem','Jak jsme pomohli Forendors  s rebrandingem','','',0,1,0,0,0,'2025-05-30 10:35:00','2125-05-30 10:35:00',34,'2025-05-30 10:35:37','{}',NULL,'{}',0),(4,1,4,'Yarmill: Jak získat legal-safe  data pro AI asistenta','Yarmill: Jak získat legal-safe  data pro AI asistenta','Yarmill: Jak získat legal-safe  data pro AI asistenta','','',0,1,0,0,0,'2025-05-30 10:36:00','2125-05-30 10:36:00',34,'2025-05-30 10:36:57','{}',NULL,'{}',0);
/*!40000 ALTER TABLE `blog_localization` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `blog_localization_tree`
--

DROP TABLE IF EXISTS `blog_localization_tree`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `blog_localization_tree` (
  `Id` int(11) NOT NULL AUTO_INCREMENT,
  `blogLocalizationId` int(11) NOT NULL,
  `treeId` int(11) NOT NULL,
  `sort` int(11) NOT NULL DEFAULT '1',
  PRIMARY KEY (`Id`),
  KEY `FK_blog_localization_tree_blog_localization` (`blogLocalizationId`),
  KEY `FK_blog_localization_tree_tree` (`treeId`),
  CONSTRAINT `FK_blog_localization_tree_blog_localization` FOREIGN KEY (`blogLocalizationId`) REFERENCES `blog_localization` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `FK_blog_localization_tree_tree` FOREIGN KEY (`treeId`) REFERENCES `tree` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `blog_localization_tree`
--

LOCK TABLES `blog_localization_tree` WRITE;
/*!40000 ALTER TABLE `blog_localization_tree` DISABLE KEYS */;
/*!40000 ALTER TABLE `blog_localization_tree` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `blog_tag`
--

DROP TABLE IF EXISTS `blog_tag`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `blog_tag` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `internalName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `blog_tag`
--

LOCK TABLES `blog_tag` WRITE;
/*!40000 ALTER TABLE `blog_tag` DISABLE KEYS */;
INSERT INTO `blog_tag` VALUES (1,'tag1','blog','{}'),(2,'tag2','blog',NULL),(3,'tag3','blog',NULL),(4,'partner','author','{}'),(5,'tag ref','reference','{}'),(6,'','material','{}'),(7,'tag mat','material','{}'),(8,'','blog','{}'),(9,'Služby','blog','{}'),(10,'Korporátní právo','author','{}');
/*!40000 ALTER TABLE `blog_tag` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `blog_tag_localization`
--

DROP TABLE IF EXISTS `blog_tag_localization`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `blog_tag_localization` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `blogTagId` int(11) NOT NULL,
  `mutationId` int(11) NOT NULL,
  `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `sort` int(11) NOT NULL DEFAULT '0',
  `nameAnchor` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `nameTitle` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `keywords` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `public` int(11) NOT NULL DEFAULT '0',
  `forceNoIndex` int(11) NOT NULL DEFAULT '0',
  `hideInSearch` int(11) NOT NULL DEFAULT '0',
  `hideInSitemap` int(11) NOT NULL DEFAULT '0',
  `edited` int(11) DEFAULT NULL,
  `editedTime` datetime DEFAULT NULL,
  `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `customContentJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `FK_blog_tag_mutation` (`mutationId`) USING BTREE,
  KEY `FK_blog_tag_localization_blog_tag` (`blogTagId`),
  CONSTRAINT `FK_blog_tag_localization_blog_tag` FOREIGN KEY (`blogTagId`) REFERENCES `blog_tag` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `FK_blog_tag_mutation` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `blog_tag_localization`
--

LOCK TABLES `blog_tag_localization` WRITE;
/*!40000 ALTER TABLE `blog_tag_localization` DISABLE KEYS */;
INSERT INTO `blog_tag_localization` VALUES (1,1,1,'Tag 1 - pořadí 10',10,'Tag 1 - pořadí 10','super super tag','','',1,0,0,0,34,'2025-05-30 06:04:28','{}','{}'),(2,2,1,'X Tag - pořadí 0',0,'X Tag - pořadí 0','X Tag - pořadí 0','','',0,0,0,0,9,'2021-08-19 13:49:32','{}',NULL),(3,3,1,'Tag - pořadí 5',5,'Tag - pořadí 5','Tag - pořadí 5','','',0,0,0,0,9,'2021-08-19 13:22:54','{}',NULL),(4,4,1,'partner',0,'partner','partner','','',1,0,0,0,34,'2025-05-23 02:34:56','{}','{}'),(5,5,1,'tag ref',0,'tag ref','tag ref','','',1,0,0,0,4,'2025-05-21 17:50:58','{}','{}'),(6,6,1,'',0,'','','','',0,0,0,0,NULL,NULL,'{}','{}'),(7,7,1,'tag mat',0,'tag mat','tag mat','','',1,0,0,0,4,'2025-05-22 11:12:29','{}','{}'),(8,8,1,'',0,'','','','',0,0,0,0,NULL,NULL,'{}','{}'),(9,9,1,'Služby',0,'Služby','Služby','','',1,0,0,0,34,'2025-05-29 10:46:59','{}','{}'),(10,10,1,'Korporátní právo',0,'Korporátní právo','Korporátní právo','','',1,0,0,0,34,'2025-05-23 03:06:27','{}','{}');
/*!40000 ALTER TABLE `blog_tag_localization` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `blog_tag_localization_tree`
--

DROP TABLE IF EXISTS `blog_tag_localization_tree`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `blog_tag_localization_tree` (
  `Id` int(11) NOT NULL AUTO_INCREMENT,
  `blogTagLocalizationId` int(11) NOT NULL,
  `treeId` int(11) NOT NULL,
  `sort` int(11) NOT NULL DEFAULT '1',
  PRIMARY KEY (`Id`),
  KEY `FK_blog_localization_tree_blog_localization` (`blogTagLocalizationId`),
  KEY `FK_blog_localization_tree_tree` (`treeId`),
  CONSTRAINT `blog_tag_localization_tree_ibfk_1` FOREIGN KEY (`blogTagLocalizationId`) REFERENCES `blog_tag_localization` (`id`) ON DELETE CASCADE,
  CONSTRAINT `blog_tag_localization_tree_ibfk_2` FOREIGN KEY (`treeId`) REFERENCES `tree` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `blog_tag_localization_tree`
--

LOCK TABLES `blog_tag_localization_tree` WRITE;
/*!40000 ALTER TABLE `blog_tag_localization_tree` DISABLE KEYS */;
INSERT INTO `blog_tag_localization_tree` VALUES (1,1,485,0);
/*!40000 ALTER TABLE `blog_tag_localization_tree` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `blog_x_author`
--

DROP TABLE IF EXISTS `blog_x_author`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `blog_x_author` (
  `blogId` int(11) NOT NULL,
  `authorId` int(11) NOT NULL,
  PRIMARY KEY (`blogId`,`authorId`) USING BTREE,
  KEY `FK_blog_x_author_blog` (`blogId`) USING BTREE,
  KEY `FK_blog_x_author_author` (`authorId`) USING BTREE,
  CONSTRAINT `FK_blog_x_author_author` FOREIGN KEY (`authorId`) REFERENCES `author` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `FK_blog_x_author_blog` FOREIGN KEY (`blogId`) REFERENCES `blog` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `blog_x_author`
--

LOCK TABLES `blog_x_author` WRITE;
/*!40000 ALTER TABLE `blog_x_author` DISABLE KEYS */;
INSERT INTO `blog_x_author` VALUES (1,3);
/*!40000 ALTER TABLE `blog_x_author` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `blog_x_blog`
--

DROP TABLE IF EXISTS `blog_x_blog`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `blog_x_blog` (
  `blogId` int(11) NOT NULL,
  `attachedBlogId` int(11) NOT NULL,
  PRIMARY KEY (`blogId`,`attachedBlogId`) USING BTREE,
  KEY `FK_blog_x_blog_blog` (`blogId`) USING BTREE,
  KEY `FK_blog_x_blog_blog_2` (`attachedBlogId`) USING BTREE,
  CONSTRAINT `FK_blog_x_blog_blog` FOREIGN KEY (`blogId`) REFERENCES `blog` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `FK_blog_x_blog_blog_2` FOREIGN KEY (`attachedBlogId`) REFERENCES `blog` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `blog_x_blog`
--

LOCK TABLES `blog_x_blog` WRITE;
/*!40000 ALTER TABLE `blog_x_blog` DISABLE KEYS */;
/*!40000 ALTER TABLE `blog_x_blog` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `blog_x_blog_tag`
--

DROP TABLE IF EXISTS `blog_x_blog_tag`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `blog_x_blog_tag` (
  `blogId` int(11) NOT NULL,
  `blogTagId` int(11) NOT NULL,
  PRIMARY KEY (`blogId`,`blogTagId`) USING BTREE,
  KEY `FK__blog` (`blogId`) USING BTREE,
  KEY `FK__blog_tag` (`blogTagId`) USING BTREE,
  CONSTRAINT `FK_blog_x_blog_tag_blog` FOREIGN KEY (`blogId`) REFERENCES `blog` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `FK_blog_x_blog_tag_blog_tag` FOREIGN KEY (`blogTagId`) REFERENCES `blog_tag` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `blog_x_blog_tag`
--

LOCK TABLES `blog_x_blog_tag` WRITE;
/*!40000 ALTER TABLE `blog_x_blog_tag` DISABLE KEYS */;
INSERT INTO `blog_x_blog_tag` VALUES (1,1),(1,2);
/*!40000 ALTER TABLE `blog_x_blog_tag` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `card_payment`
--

DROP TABLE IF EXISTS `card_payment`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `card_payment` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `cardPaymentInformationId` int(11) NOT NULL,
  `paymentGatewayUniqueIdentifier` varchar(255) NOT NULL,
  `externalId` varchar(255) NOT NULL,
  `externalUrl` varchar(255) DEFAULT NULL,
  `status` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `paymentGatewayUniqueIdentifier` (`paymentGatewayUniqueIdentifier`,`externalId`),
  KEY `cardPaymentInformationId` (`cardPaymentInformationId`),
  CONSTRAINT `card_payment_ibfk_1` FOREIGN KEY (`cardPaymentInformationId`) REFERENCES `order_payment_information` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `card_payment`
--

LOCK TABLES `card_payment` WRITE;
/*!40000 ALTER TABLE `card_payment` DISABLE KEYS */;
/*!40000 ALTER TABLE `card_payment` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `card_payment_status_change`
--

DROP TABLE IF EXISTS `card_payment_status_change`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `card_payment_status_change` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `paymentId` int(11) NOT NULL,
  `changedAt` datetime NOT NULL,
  `from` varchar(255) DEFAULT NULL,
  `to` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `paymentId` (`paymentId`),
  CONSTRAINT `card_payment_status_change_ibfk_1` FOREIGN KEY (`paymentId`) REFERENCES `card_payment` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `card_payment_status_change`
--

LOCK TABLES `card_payment_status_change` WRITE;
/*!40000 ALTER TABLE `card_payment_status_change` DISABLE KEYS */;
/*!40000 ALTER TABLE `card_payment_status_change` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `delivery_method`
--

DROP TABLE IF EXISTS `delivery_method`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `delivery_method` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `externalId` varchar(50) DEFAULT NULL,
  `deliveryMethodUniqueIdentifier` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `desc` varchar(255) NOT NULL,
  `tooltip` varchar(255) DEFAULT NULL,
  `sort` int(11) NOT NULL,
  `public` tinyint(1) NOT NULL,
  `isRecommended` tinyint(1) NOT NULL,
  `deliveryDayFrom` int(11) NOT NULL,
  `deliveryDayTo` int(11) DEFAULT NULL,
  `deliveryHourByStock` text NOT NULL,
  `mutationId` int(11) NOT NULL,
  `vats` text NOT NULL,
  `customFieldsJson` longtext,
  PRIMARY KEY (`id`),
  KEY `mutationId` (`mutationId`),
  CONSTRAINT `delivery_method_ibfk_1` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `delivery_method`
--

LOCK TABLES `delivery_method` WRITE;
/*!40000 ALTER TABLE `delivery_method` DISABLE KEYS */;
/*!40000 ALTER TABLE `delivery_method` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `delivery_method_price`
--

DROP TABLE IF EXISTS `delivery_method_price`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `delivery_method_price` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `deliveryMethodId` int(11) NOT NULL,
  `priceLevelId` int(11) NOT NULL,
  `stateId` int(11) NOT NULL,
  `price_amount` decimal(18,4) NOT NULL,
  `price_currency` char(3) NOT NULL,
  `freeFrom` decimal(18,4) DEFAULT NULL,
  `maxWeight` int(11) DEFAULT NULL,
  `maxCodPrice` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `deliveryMethodId` (`deliveryMethodId`),
  KEY `priceLevelId` (`priceLevelId`),
  KEY `stateId` (`stateId`),
  CONSTRAINT `delivery_method_price_ibfk_1` FOREIGN KEY (`deliveryMethodId`) REFERENCES `delivery_method` (`id`),
  CONSTRAINT `delivery_method_price_ibfk_2` FOREIGN KEY (`priceLevelId`) REFERENCES `price_level` (`id`),
  CONSTRAINT `delivery_method_price_ibfk_3` FOREIGN KEY (`stateId`) REFERENCES `state` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `delivery_method_price`
--

LOCK TABLES `delivery_method_price` WRITE;
/*!40000 ALTER TABLE `delivery_method_price` DISABLE KEYS */;
/*!40000 ALTER TABLE `delivery_method_price` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `delivery_method_x_payment_method`
--

DROP TABLE IF EXISTS `delivery_method_x_payment_method`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `delivery_method_x_payment_method` (
  `deliveryMethodId` int(11) NOT NULL,
  `paymentMethodId` int(11) NOT NULL,
  KEY `deliveryMethodId` (`deliveryMethodId`),
  KEY `paymentMethodId` (`paymentMethodId`),
  CONSTRAINT `delivery_method_x_payment_method_ibfk_1` FOREIGN KEY (`deliveryMethodId`) REFERENCES `delivery_method` (`id`),
  CONSTRAINT `delivery_method_x_payment_method_ibfk_2` FOREIGN KEY (`paymentMethodId`) REFERENCES `payment_method` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `delivery_method_x_payment_method`
--

LOCK TABLES `delivery_method_x_payment_method` WRITE;
/*!40000 ALTER TABLE `delivery_method_x_payment_method` DISABLE KEYS */;
/*!40000 ALTER TABLE `delivery_method_x_payment_method` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `delivery_method_x_state`
--

DROP TABLE IF EXISTS `delivery_method_x_state`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `delivery_method_x_state` (
  `deliveryMethodId` int(11) NOT NULL,
  `stateId` int(11) NOT NULL,
  PRIMARY KEY (`deliveryMethodId`,`stateId`),
  KEY `stateId` (`stateId`),
  CONSTRAINT `delivery_method_x_state_ibfk_1` FOREIGN KEY (`deliveryMethodId`) REFERENCES `delivery_method` (`id`),
  CONSTRAINT `delivery_method_x_state_ibfk_2` FOREIGN KEY (`stateId`) REFERENCES `state` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `delivery_method_x_state`
--

LOCK TABLES `delivery_method_x_state` WRITE;
/*!40000 ALTER TABLE `delivery_method_x_state` DISABLE KEYS */;
/*!40000 ALTER TABLE `delivery_method_x_state` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `elastic_search_index`
--

DROP TABLE IF EXISTS `elastic_search_index`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `elastic_search_index` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mutationId` int(11) DEFAULT NULL,
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '',
  `createdTime` datetime DEFAULT NULL,
  `startTime` datetime DEFAULT NULL,
  `finishTime` datetime DEFAULT NULL,
  `recreate` tinyint(1) DEFAULT '0',
  `status` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `active` int(11) DEFAULT '0',
  `errorCount` int(11) NOT NULL DEFAULT '0',
  `errorDetail` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `mutationId` (`mutationId`) USING BTREE,
  CONSTRAINT `elastic_search_index_ibfk_2` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_czech_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `elastic_search_index`
--

LOCK TABLES `elastic_search_index` WRITE;
/*!40000 ALTER TABLE `elastic_search_index` DISABLE KEYS */;
INSERT INTO `elastic_search_index` VALUES (1,1,'all','superadmin_stage_all_cs','2025-05-16 14:33:48','2025-05-16 14:33:58','2025-05-16 14:33:59',0,NULL,0,0,'{}');
/*!40000 ALTER TABLE `elastic_search_index` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `email_template`
--

DROP TABLE IF EXISTS `email_template`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `email_template` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mutationId` int(11) NOT NULL DEFAULT '1',
  `key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `isDeveloper` tinyint(1) NOT NULL DEFAULT '0',
  `isHidden` tinyint(1) NOT NULL DEFAULT '0',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `subject` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `body` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  PRIMARY KEY (`id`),
  KEY `mutationId` (`mutationId`),
  CONSTRAINT `email_template_ibfk_1` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=63 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `email_template`
--

LOCK TABLES `email_template` WRITE;
/*!40000 ALTER TABLE `email_template` DISABLE KEYS */;
INSERT INTO `email_template` VALUES (1,1,'order',0,0,'Objednávka','Potvrzení objednávky: [NUMBER]','<h2>Děkujeme za Vaši objednávku na superadmin.cz</h2>\r\n<p> </p>\r\n<p>Dobrý den,<br />děkujeme za Vaši objednávku. Tímto potvrzujeme, že jsme Vaši objednávku přijali a evidujeme ji pod číslem [NUMBER]. O stavu objednávky Vás budeme dále informovat.</p>\r\n<p>[RECAPITULATION]</p>\r\n<p>________</p>\r\n<p>Příjemný den a spokojený nákup přeje<br /><span>tým superadmin.</span></p>'),(2,1,'orderPreparedPersonal',0,0,'Objednávka - připraveno k odběru',NULL,'<h2>Vaše objednávka na superadmin.cz byla zpracována</h2>\n<p> </p>\n<p>Dobrý den,<br />Vaše objednávka s číslem [NUMBER] byla zpracována a je připravena k vyzvednutí na naší pobočce.</p>\n<p> </p>\n<p>[RECAPITULATION]</p>\n<p> </p>\n<p>Úspěšný den a spokojený nákup přeje<br /><span>tým superadmin.</span></p>'),(3,1,'orderPreparedPpl',0,0,'Objednávka - PPL připraveno',NULL,'<h2>Vaše objednávka byla zpracována</h2>\n<p>Dobrý den,<br /> vaše objednávka s číslem [NUMBER] byla zpracována a je připravena k odeslání na vaši adresu.<br />O stavu objednávky vás budeme dále informovat.</p>\n<p><span>Tým superadmin.</span></p>\n<p> </p>\n<p>[RECAPITULATION]</p>'),(4,1,'orderSent',0,0,'Objednávka - odeslána',NULL,'<h2>Vaše objednávka na superadmin.cz byla odeslána</h2>\n<p> </p>\n<p>Dobrý den,<br />Vaše objednávka s číslem [NUMBER] byla právě odeslána na Vaši adresu.</p>\n<p>Děkujeme za Váš nákup!<br /><span>Tým superadmin.</span></p>\n<p>[RECAPITULATION]</p>\n<p> </p>'),(6,1,'lostPassword',0,0,'Zap. heslo','Změna hesla','<h2>Změna hesla</h2>\n<h4>Změňte svoje heslo kliknutím na odkaz:</h4>\n<p><span style=\"color: #848587; font-family: GTAmerica, sans-serif;\"><a href=\"[DATA-link]\">[DATA-link]</a></span></p>\n<p>________<strong></strong></p>\n<p>Úspěšný den přeje</p>\n<p>tým superadmin</p>'),(7,1,'login',0,0,'Přihlášení',NULL,'<p class=\"p1\"><strong>Nová registrace</strong></p>\r\n<p class=\"p2\">Vítejte na našem webu.</p>\r\n<p class=\"p2\"><strong>Vaši registraci evidujeme pod e-mailem: </strong>[DATA-email]</p>\r\n<p class=\"p2\">________</p>\r\n<p class=\"p2\">Spokojený nákup přeje,<br /> tým superadmin</p>'),(13,1,'sendOnEmail',1,0,'Odeslat na email',NULL,'<h2>Zpráva z webu</h2>\r\n<p><strong>Jméno: </strong> [DATA-name]<br /> <strong>E-mail: </strong> [DATA-email]</p>\r\n<p><strong>text: </strong><br /> [DATA-text]</p>\r\n<p>________</p>\r\n<p>Úspěšný den přeje,<br />tým superadmin</p>'),(15,1,'newsletterInfo',0,0,'Odběr novinek',NULL,'<h2>Přihlášení k odběru novinek od superadmin.cz</h2>\r\n<p>Dobrý den,</p>\r\n<p>moc nás těší, že s námi chcete zůstat ve spojení. Jako první budete dostávat informace o novinkách, akcích a slevách, zkrátka o všem, co se děje na superadmin.cz</p>\r\n<p>Brzy u čerstvých novinek!<br />Tým superadmin</p>'),(21,1,'career',1,0,'Kariéra',NULL,'<h2>Nový životopis</h2>\r\n<p><strong>Jméno:<span> </span></strong>[DATA-name]<br /><strong>E-mail:<span> </span></strong>[DATA-email]</p>\r\n<p><strong>Telefon:<span> </span></strong>[DATA-phone]</p>\r\n<p><strong>Zpráva:<span> </span></strong><br />[DATA-message]</p>\r\n<p><span><strong>Životopis :</strong><span> </span></span><a href=\"[DATA-pageLink]\">[DATA-page]</a></p>\r\n<p><strong>Soubor</strong>: <span>[DATA-file]<br /><br />Děkujeme za váš zájem o práci u nás,<br />tým superadmin</span></p>'),(45,1,'passwordChanged',0,0,'Heslo bylo změněno','','<h2>Vaše heslo bylo změněno</h2>\r\n<h4><strong style=\"color: #848587; font-family: GTAmerica, sans-serif;\"></strong><span style=\"color: #848587; font-family: GTAmerica, sans-serif;\">Heslo k e-mailu [DATA-email] bylo změněno.</span></h4>\r\n<p><span style=\"color: #848587; font-family: GTAmerica, sans-serif;\">Pokud jste změnu provedli vy, tak tento e-mail <span>můžete</span> ignorovat.</span></p>\r\n<p><span style=\"color: #848587; font-family: GTAmerica, sans-serif;\">Pokud ne, tak si zkontrolujte zabezpečení svého účtu a případně si <a href=\"[DATA-link]\">nechte vygenerovat nové heslo</a>.</span></p>\r\n<p>________</p>\r\n<p>Pěkný den přeje<br />tým superadmin</p>'),(50,1,'softBlock',0,0,'Prodloužení platnosti účtu',NULL,'<h2>Prodloužení platnosti účtu</h2>\r\n<h4><strong style=\"color: #848587; font-family: GTAmerica, sans-serif;\"></strong><span style=\"color: #848587; font-family: GTAmerica, sans-serif;\">Pro prodloužení platnosti účtu stačí kliknout na tento odkaz: <a href=\"[DATA-link]\" target=\"_blank\">[DATA-link]</a></span></h4>\r\n<p><span style=\"color: #848587; font-family: GTAmerica, sans-serif;\">Na prodloužení platnosti máte 7 dní od obdržení tohoto e-mailu.</span></p>\r\n<p>V případě, že se vyskytnou problémy nebo uplynula 7 denní lhůta nás kontaktujte na:</p>\r\n<p>e-mail: <a href=\"mailto:<EMAIL>\"><EMAIL></a><br /><span>tel.:<span> </span></span><a href=\"tel:+420286880161\">+420 286 880 161</a></p>\r\n<p>________</p>\r\n<p>Pěkný den přeje<br />tým superadmin</p>'),(55,1,'availability',0,0,'Produkt není skladem - dostupnost',NULL,'<p>Dostupnost - dotaz</p>\n<p><strong>E-mail: </strong>[DATA-email]</p>\n<p><span><strong>Produkt: </strong></span><a href=\"[DATA-pageLink]\">[DATA-page]</a></p>\n<p><a href=\"[DATA-pageLink]\"></a>Děkujeme, že jste využili upozornění na naskladnění zboží, o které máte zájem. Upozorníme vás e-mailem, hned jak k nám dorazí. Vážíme si vaší trpělivosti a budeme rádi, když si čekání na zboží zpříjmeníte s články z našeho <a href=\"https://superadmin.www5.superkoderi.cz/blog\">blogu</a>. </p>\n<p>Letu Zdar!</p>\n<p dir=\"ltr\">Nikola z superadmin</p>\n<p dir=\"ltr\"> </p>\n<p dir=\"ltr\"><span>Nikola Dedíková</span></p>\n<p dir=\"ltr\"><span>CEO &amp; Co-Founder</span></p>\n<p dir=\"ltr\"><span>+420 776 868 886</span></p>\n<p dir=\"ltr\"><span>www.superadmin.cz</span></p>\n<p dir=\"ltr\"><span><a href=\"http://www.facebook.com/superadmincz\">www.facebook.com/superadmincz</a></span></p>\n<p> </p>'),(56,1,'contact',0,0,'contact','contact1','<h2><strong>Zpráva z konktaktního formuláře</strong></h2>\n<p> </p>\n<p><span><strong>Téma: </strong></span>[DATA-type]</p>\n<p><span><strong>Jméno: </strong>[DATA-name]</span></p>\n<p><strong>E-mail: </strong>[DATA-email]</p>\n<p><strong>Telefon: </strong>[DATA-phone]</p>\n<p><strong>Zpráva:</strong><br />[DATA-text]</p>\n<p> </p>\n<p><span> </span></p>'),(57,1,'voucher',0,0,'Odeslání slevového poukazu na e-mail','Superadmin.cz - Dárkový poukaz z objednávky č. [DATA-orderNumber]','<p>Dobrý den,</p>\r\n<p>posíláme kód pro dárkový poukaz z objednávky č. [DATA-orderNumber]</p>\r\n<h2>[DATA-code]</h2>\r\n<p>platnost do: <span>[DATA-validTo]</span></p>\r\n<p><span><span>hodnota: </span><span>[DATA-value] Kč</span></span></p>\r\n<p><span><span>název: <span>[DATA-name]</span></span></span></p>'),(58,1,'hotline',0,0,'Napište našemu specialistovi','Napište našemu specialistovi 11','<h2><strong>Žádost o radu</strong></h2>\n<p> </p>\n<p><span><strong>Jméno: </strong>[DATA-name]</span></p>\n<p><strong>E-mail: </strong>[DATA-email]</p>\n<p><strong>Zpráva:</strong><br />[DATA-text]</p>\n<p><strong>Na stránce: </strong><a href=\"[DATA-pageLink]\">[DATA-page]</a></p>\n<p> </p>\n<p> </p>\n<p> </p>'),(59,1,'inquiry',0,0,'Nezávazná poptávka','Nezávazná poptávka1 [DATA-name]','<h2><strong>Nezávazná poptávka</strong></h2>\n<p> </p>\n<p><span><strong>Jméno: </strong>[DATA-name]</span></p>\n<p><strong>E-mail: </strong>[DATA-email]</p>\n<p><strong>Telefon: </strong>[DATA-phone]</p>\n<p><strong>Zpráva:</strong><br />[DATA-text]</p>\n<p> </p>\n<p> </p>\n<p> </p>'),(61,1,'inquiry',0,0,'Nezávazná poptávka',NULL,'<h2><strong>Nezávazná poptávka</strong></h2>\n<p> </p>\n<p><span><strong>Jméno: </strong>[DATA-name]</span></p>\n<p><strong>E-mail: </strong>[DATA-email]</p>\n<p><strong>Telefon: </strong>[DATA-phone]</p>\n<p><strong>Zpráva:</strong><br />[DATA-text]</p>\n<p> </p>\n<p> </p>\n<p> </p>'),(62,1,'orderStorno',0,0,'orderStorno','orderStorno','<pre><span>orderStorno</span></pre>');
/*!40000 ALTER TABLE `email_template` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `email_template_file`
--

DROP TABLE IF EXISTS `email_template_file`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `email_template_file` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `fileId` int(11) NOT NULL COMMENT 'idfile',
  `emailTemplateId` int(11) NOT NULL,
  `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `url` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `sort` mediumint(9) DEFAULT NULL,
  `size` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `fileId_emailTemplateId` (`fileId`,`emailTemplateId`),
  KEY `emailTemplateId` (`emailTemplateId`),
  KEY `fileId` (`fileId`),
  CONSTRAINT `email_template_file_ibfk_1` FOREIGN KEY (`fileId`) REFERENCES `file` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `email_template_file_ibfk_2` FOREIGN KEY (`emailTemplateId`) REFERENCES `email_template` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `email_template_file`
--

LOCK TABLES `email_template_file` WRITE;
/*!40000 ALTER TABLE `email_template_file` DISABLE KEYS */;
/*!40000 ALTER TABLE `email_template_file` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `feature`
--

DROP TABLE IF EXISTS `feature`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `feature` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `internalName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `feature`
--

LOCK TABLES `feature` WRITE;
/*!40000 ALTER TABLE `feature` DISABLE KEYS */;
INSERT INTO `feature` VALUES (2,'IT právo a právo TMT','{\"base\":[{\"annot\":[{\"bg\":\"u-bgc-yellow\",\"btn\":[{\"toggle\":\"systemHref\",\"systemHref\":[{\"page\":449,\"hrefName\":\"Potřebuji licenční podmínky\"}]}],\"right\":[{\"image\":\"87\"}]}]}]}'),(3,'Test','{\"base\":[{\"mainImage\":\"87\"}]}');
/*!40000 ALTER TABLE `feature` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `feature_localization`
--

DROP TABLE IF EXISTS `feature_localization`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `feature_localization` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mutationId` int(11) NOT NULL,
  `featureId` int(11) NOT NULL,
  `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `nameAnchor` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `nameTitle` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `keywords` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `isTop` int(11) NOT NULL DEFAULT '0',
  `public` int(11) NOT NULL DEFAULT '0',
  `forceNoIndex` int(11) NOT NULL DEFAULT '0',
  `hideInSearch` int(11) NOT NULL DEFAULT '0',
  `hideInSitemap` int(11) NOT NULL DEFAULT '0',
  `publicFrom` datetime DEFAULT NULL,
  `publicTo` datetime DEFAULT NULL,
  `edited` int(11) DEFAULT NULL,
  `editedTime` datetime DEFAULT NULL,
  `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `customContentSchemeJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `customContentJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `viewsNumber` int(10) unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `FK_blog_mutation` (`mutationId`) USING BTREE,
  KEY `FK_blog_localization_blog` (`featureId`) USING BTREE,
  CONSTRAINT `feature_localization_ibfk_1` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE,
  CONSTRAINT `feature_localization_ibfk_2` FOREIGN KEY (`featureId`) REFERENCES `feature` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `feature_localization`
--

LOCK TABLES `feature_localization` WRITE;
/*!40000 ALTER TABLE `feature_localization` DISABLE KEYS */;
INSERT INTO `feature_localization` VALUES (2,1,2,'Licenční podmínky pro software a autorská díla, EULA','Licenční podmínky pro software a autorská díla, EULA','Licenční podmínky pro software a autorská díla, EULA','a','',0,1,0,0,0,'2025-05-28 08:15:00','2125-05-28 08:15:00',34,'2025-05-30 10:42:09','{\"base\":[{\"annotation\":\"Jasně a srozumitelně formulované licenční podmínky chrání autorská práva a nastavují pravidla užívání pro software i jiná díla. Nepodceňujte jejich význam – správně nastavená licence předejde mnoha problémům v budoucnosti.\"}]}',NULL,'{\"highlights____mB_jCBXvNJZVRkql9XVnJ\":[{\"title\":\"Jak jsme pomohli Forendors  s rebrandingem\",\"items\":[{\"image\":\"135\",\"name\":\"Highlight 1\",\"text\":\"Solve a problem or close a sale in real-time with chat. If no one is available, customers are seamlessly routed to email without confusion.\"},{\"image\":\"153\",\"name\":\"Highlight 2\",\"text\":\"Solve a problem or close a sale in real-time with chat. If no one is available, customers are seamlessly routed to email without confusion.\"},{\"image\":\"134\",\"name\":\"Highlight 3\",\"text\":\"Solve a problem or close a sale in real-time with chat. If no one is available, customers are seamlessly routed to email without confusion.\"}]}],\"testimonial____oPzQXSG2X8S2bEGLvYneh\":[{\"testimonial\":2}],\"tabs____hFtERLP0prriegtJ11Wu0\":[{\"title\":\"Jak s námi probíhá spolupráce na licenčních podmínkách, EULA?\",\"items\":[{\"name\":\"Poznáme\",\"content\":\"<p><span>V první fázi nám poskytnete základní údaje o vašem softwaru, zákaznících a způsobu prodeje licence. Tím zjistíme, jaký typ licenční smlouvy je pro vás vhodný – zda potřebujete individuální dokument nebo jednoduché podmínky k odsouhlasení online.</span></p>\"},{\"name\":\"Zpracujeme\",\"content\":\"<p><span class=\\\"TextRun SCXW121858675 BCX4\\\" lang=\\\"CS-CZ\\\" xml:lang=\\\"CS-CZ\\\" data-contrast=\\\"auto\\\"><span class=\\\"NormalTextRun SCXW121858675 BCX4\\\">Na základě vašich požadavků připravíme stručné, přehledné a právně přesné licenční podmínky, které se snadno čtou. Zajistíme, aby dokument reflektoval specifické podmínky vašeho odvětví a případné použití open source softwaru.</span></span><span class=\\\"EOP SCXW121858675 BCX4\\\" data-ccp-props=\\\"{}\\\"> </span></p>\"}]}]}',54),(3,1,3,'Test','Test','Test','popis','',0,1,0,0,0,'2025-05-29 10:33:00','2125-05-29 10:33:00',34,'2025-05-29 11:14:50','{\"base\":[{\"annotation\":\"anotace\"}]}',NULL,'{\"tabs____FgBdozPERre4h8tzUNe3E\":[{\"title\":\"nadpis\",\"items\":[{\"name\":\"a\",\"content\":\"<p>b</p>\"}]}]}',0);
/*!40000 ALTER TABLE `feature_localization` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `feature_localization_tree`
--

DROP TABLE IF EXISTS `feature_localization_tree`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `feature_localization_tree` (
  `Id` int(11) NOT NULL AUTO_INCREMENT,
  `featureLocalizationId` int(11) NOT NULL,
  `treeId` int(11) NOT NULL,
  `sort` int(11) NOT NULL DEFAULT '1',
  PRIMARY KEY (`Id`),
  KEY `FK_blog_localization_tree_blog_localization` (`featureLocalizationId`),
  KEY `FK_blog_localization_tree_tree` (`treeId`),
  CONSTRAINT `feature_localization_tree_ibfk_1` FOREIGN KEY (`featureLocalizationId`) REFERENCES `feature_localization` (`id`) ON DELETE CASCADE,
  CONSTRAINT `feature_localization_tree_ibfk_2` FOREIGN KEY (`treeId`) REFERENCES `tree` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `feature_localization_tree`
--

LOCK TABLES `feature_localization_tree` WRITE;
/*!40000 ALTER TABLE `feature_localization_tree` DISABLE KEYS */;
INSERT INTO `feature_localization_tree` VALUES (1,3,449,0),(2,2,485,0);
/*!40000 ALTER TABLE `feature_localization_tree` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `file`
--

DROP TABLE IF EXISTS `file`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `file` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `filename` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `size` int(11) DEFAULT NULL,
  `isDeleted` tinyint(1) DEFAULT '0',
  `createdTime` datetime DEFAULT CURRENT_TIMESTAMP,
  `deletedTime` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=107 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `file`
--

LOCK TABLES `file` WRITE;
/*!40000 ALTER TABLE `file` DISABLE KEYS */;
INSERT INTO `file` VALUES (1,'superkoderi@oldrichhrb_1200px-01.jpg','1-superkoderi-oldrichhrb-1200px-01.jpg',813255,0,'2025-04-23 11:48:36',NULL),(2,'calibra_cn_8578680.pdf','2-calibra-cn-8578680.pdf',84226,0,'2025-04-23 11:48:36',NULL),(3,'395046CZK120201BRM_20201231_1 (1).pdf','3-395046czk120201brm-20201231-1-1.pdf',1250815,1,'2025-04-23 11:48:36','2025-05-13 14:22:18'),(4,'annot-products.jpg','4-annot-products.jpeg',76762,0,'2025-04-23 11:48:36',NULL),(5,'2101010939_eshop_snauwaert_cz (2).pdf','5-2101010939-eshop-snauwaert-cz-2.pdf',67631,0,'2025-04-23 11:48:36',NULL),(6,'2.png','6-2.png',365705,0,'2025-04-23 11:48:36',NULL),(7,'[SkT]Rytiri_spravedlnosti___Retf&#230;rdighedens_ryttere_(2020)[WebRip][1080p]_=_CSFD_83%.torrent','7-skt-rytiri-spravedlnosti-retf-230-rdighedens-ryttere-2020-webrip-1080p-csfd-83.torrent',26101,0,'2025-04-23 11:48:36',NULL),(8,'Ryzlink_Rynsky.png','8-ryzlink-rynsky.png',4055271,0,'2025-04-23 11:48:36',NULL),(9,'image (10).png','9-image-10.png',9141,0,'2025-04-23 11:48:36',NULL),(10,'brtnik 01-2021.pdf','10-brtnik-01-2021.pdf',63704,0,'2025-04-23 11:48:36',NULL),(11,'Eu0gZ5_XMAABywS.jfif','11-eu0gz5-xmaabyws.jpeg',63479,0,'2025-04-23 11:48:36',NULL),(12,'image (11).png','12-image-11.png',20042,0,'2025-04-23 11:48:36',NULL),(13,'ats-pumpa---navod---1-2-3-eh-te.pdf','13-ats-pumpa-navod-1-2-3-eh-te.pdf',4104733,0,'2025-04-23 11:48:36',NULL),(14,'ats-pumpa---navod---1-2-3-eh-te.pdf','14-ats-pumpa-navod-1-2-3-eh-te.pdf',4104733,0,'2025-04-23 11:48:36',NULL),(15,'ats-pumpa---navod---1-2-3-eh-te.pdf','15-ats-pumpa-navod-1-2-3-eh-te.pdf',4104733,0,'2025-04-23 11:48:36',NULL),(16,'ats-pumpa---navod---1-2-3-eh-te.pdf','16-ats-pumpa-navod-1-2-3-eh-te.pdf',4104733,0,'2025-04-23 11:48:36',NULL),(17,'ats-pumpa---navod---1-2-3-eh-te.pdf','17-ats-pumpa-navod-1-2-3-eh-te.pdf',4104733,0,'2025-04-23 11:48:36',NULL),(18,'ats-pumpa---navod---1-2-3-eh-te.pdf','18-ats-pumpa-navod-1-2-3-eh-te.pdf',4104733,0,'2025-04-23 11:48:36',NULL),(19,'ats-pumpa---navod---1-2-3-eh-te.pdf','19-ats-pumpa-navod-1-2-3-eh-te.pdf',4104733,0,'2025-04-23 11:48:36',NULL),(20,'ats-pumpa---navod---1-2-3-eh-te.pdf','20-ats-pumpa-navod-1-2-3-eh-te.pdf',4104733,0,'2025-04-23 11:48:36',NULL),(21,'ats-pumpa---navod---1-2-3-eh-te.pdf','21-ats-pumpa-navod-1-2-3-eh-te.pdf',4104733,0,'2025-04-23 11:48:36',NULL),(22,'D2C MPX API Integration Specification (MASTER) -- 3.pdf','22-d2c-mpx-api-integration-specification-master-3.pdf',1702568,0,'2025-04-23 11:48:36',NULL),(23,'ats-pumpa---navod---1-2-3-eh-te.pdf','23-ats-pumpa-navod-1-2-3-eh-te.pdf',4104733,0,'2025-04-23 11:48:36',NULL),(24,'D2C MPX API Integration Specification (MASTER) -- 3.pdf','24-d2c-mpx-api-integration-specification-master-3.pdf',1702568,0,'2025-04-23 11:48:36',NULL),(25,'ats-pumpa---navod---1-2-3-eh-te.pdf','25-ats-pumpa-navod-1-2-3-eh-te.pdf',4104733,0,'2025-04-23 11:48:36',NULL),(26,'D2C MPX API Integration Specification (MASTER) -- 3.pdf','26-d2c-mpx-api-integration-specification-master-3.pdf',1702568,0,'2025-04-23 11:48:36',NULL),(27,'ats-pumpa---navod---1-2-3-eh-te.pdf','27-ats-pumpa-navod-1-2-3-eh-te.pdf',4104733,0,'2025-04-23 11:48:36',NULL),(28,'D2C MPX API Integration Specification (MASTER) -- 3.pdf','28-d2c-mpx-api-integration-specification-master-3.pdf',1702568,0,'2025-04-23 11:48:36',NULL),(29,'ats-pumpa---navod---1-2-3-eh-te.pdf','29-ats-pumpa-navod-1-2-3-eh-te.pdf',4104733,0,'2025-04-23 11:48:36',NULL),(30,'D2C MPX API Integration Specification (MASTER) -- 3.pdf','30-d2c-mpx-api-integration-specification-master-3.pdf',1702568,0,'2025-04-23 11:48:36',NULL),(31,'ats-pumpa---navod---1-2-3-eh-te.pdf','31-ats-pumpa-navod-1-2-3-eh-te.pdf',4104733,0,'2025-04-23 11:48:36',NULL),(32,'payment-detail-T568208798 (13).pdf','32-payment-detail-t568208798-13.pdf',3081,0,'2025-04-23 11:48:36',NULL),(33,'payment-detail-T568208798 (14).pdf','33-payment-detail-t568208798-14.pdf',3082,0,'2025-04-23 11:48:36',NULL),(34,'payment-detail-T568208798 (11).pdf','34-payment-detail-t568208798-11.pdf',2485,0,'2025-04-23 11:48:36',NULL),(35,'rotao60-8878719.pdf','35-rotao60-8878719.pdf',187781,0,'2025-04-23 11:48:36',NULL),(36,'toby-elliott-m3SrHEMrmbQ-unsplash.jpg','36-toby-elliott-m3srhemrmbq-unsplash.jpeg',2864450,0,'2025-04-23 11:48:36',NULL),(37,'girl-with-red-hat-sLcVe47YUJI-unsplash.jpg','37-girl-with-red-hat-slcve47yuji-unsplash.jpeg',5611172,0,'2025-04-23 11:48:36',NULL),(38,'_kamenivo_cenik_2021.pdf','38-kamenivo-cenik-2021.pdf',4785772,0,'2025-04-23 11:48:36',NULL),(39,'chyby,kterekaziwebvitals4.036.jpeg','39-chyby-kterekaziwebvitals4.036.jpeg',387417,0,'2025-04-23 11:48:36',NULL),(40,'payment-detail-T644971264.pdf','40-payment-detail-t644971264.pdf',3045,0,'2025-04-23 11:48:36',NULL),(41,'payment-detail-T738633803.pdf','41-payment-detail-t738633803.pdf',3034,0,'2025-04-23 11:48:36',NULL),(42,'41-payment-detail-t738633803.pdf','42-41-payment-detail-t738633803.pdf',3034,0,'2025-04-23 11:48:36',NULL),(43,'41-payment-detail-t738633803.pdf','43-41-payment-detail-t738633803.pdf',3034,0,'2025-04-23 11:48:36',NULL),(44,'payment-detail-T644971264.pdf','44-payment-detail-t644971264.pdf',3045,0,'2025-04-23 11:48:36',NULL),(45,'payment-detail-T738633803.pdf','45-payment-detail-t738633803.pdf',3034,0,'2025-04-23 11:48:36',NULL),(46,'payment-detail-T644971264.pdf','46-payment-detail-t644971264.pdf',3045,0,'2025-04-23 11:48:36',NULL),(47,'41-payment-detail-t738633803.pdf','47-41-payment-detail-t738633803.pdf',3034,0,'2025-04-23 11:48:36',NULL),(48,'41-payment-detail-t738633803.pdf','48-41-payment-detail-t738633803.pdf',3034,0,'2025-04-23 11:48:36',NULL),(49,'payment-detail-T644971264.pdf','49-payment-detail-t644971264.pdf',3045,0,'2025-04-23 11:48:36',NULL),(50,'41-payment-detail-t738633803.pdf','50-41-payment-detail-t738633803.pdf',3034,0,'2025-04-23 11:48:36',NULL),(51,'vzduchovka-hammerli-hunter-force-900-combo-4-5mm-55357.2519740793.1623055930.jpg',NULL,NULL,0,'2025-04-23 11:48:36',NULL),(52,'sdvsfgnehmhadsfds.2519740793.1623055896.jpg',NULL,NULL,0,'2025-04-23 11:48:36',NULL),(53,'vzduchovka-hammerli-hunter-force-900-combo-4-5mm-55361.2519740793.1623055930.jpg',NULL,NULL,0,'2025-04-23 11:48:36',NULL),(54,'vzduchovka-hammerli-hunter-force-900-combo-4-5mm-55363.2519740793.1623055930.jpg',NULL,NULL,0,'2025-04-23 11:48:36',NULL),(55,'vzduchovka-hammerli-hunter-force-900-combo-4-5mm-55360.2519740793.1623055930.jpg',NULL,NULL,0,'2025-04-23 11:48:36',NULL),(56,'vzduchovka-hammerli-hunter-force-900-combo-4-5mm-55362.2519740793.1623055930.jpg',NULL,NULL,0,'2025-04-23 11:48:36',NULL),(57,'sdvsfgnehmhadsfds.2519740793.1623055896.jpg','57-sdvsfgnehmhadsfds.2519740793.1623055896.jpeg',379502,0,'2025-04-23 11:48:36',NULL),(58,'sdvsfgnehmhadsfds.2519740793.1623055896.jpg','58-sdvsfgnehmhadsfds.2519740793.1623055896.jpeg',379502,0,'2025-04-23 11:48:36',NULL),(59,'vzduchovka-hammerli-hunter-force-900-combo-4-5mm-55357.2519740793.1623055930.jpg','59-vzduchovka-hammerli-hunter-force-900-combo-4-5mm-55357.2519740793.1623055930.jpeg',380305,0,'2025-04-23 11:48:36',NULL),(60,'vzduchovka-hammerli-hunter-force-900-combo-4-5mm-55362.2519740793.1623055930.jpg','60-vzduchovka-hammerli-hunter-force-900-combo-4-5mm-55362.2519740793.1623055930.jpeg',410929,0,'2025-04-23 11:48:36',NULL),(61,'vzduchovka-hammerli-hunter-force-900-combo-4-5mm-55361.2519740793.1623055930.jpg',NULL,NULL,0,'2025-04-23 11:48:36',NULL),(62,'vzduchovka-hammerli-hunter-force-900-combo-4-5mm-55363.2519740793.1623055930.jpg',NULL,NULL,0,'2025-04-23 11:48:36',NULL),(63,'vzduchovka-hammerli-hunter-force-900-combo-4-5mm-55358.2519740793.1623055930.jpg',NULL,NULL,0,'2025-04-23 11:48:36',NULL),(64,'vzduchovka-hammerli-hunter-force-900-combo-4-5mm-55359.2519740793.1623055930.jpg',NULL,NULL,0,'2025-04-23 11:48:36',NULL),(65,'vzduchovka-hammerli-hunter-force-900-combo-4-5mm-55360.2519740793.1623055930.jpg',NULL,NULL,0,'2025-04-23 11:48:36',NULL),(66,'2.4936_Hammerli Hunter Force 900 Combo-bullet.pdf',NULL,NULL,0,'2025-04-23 11:48:36',NULL),(67,'2.4936_Hammerli Hunter Force 900 Combo-bullet.pdf','67-2.4936-hammerli-hunter-force-900-combo-bullet.pdf',382049,0,'2025-04-23 11:48:36',NULL),(68,'payment-detail-T438632305 (2).pdf','68-payment-detail-t438632305-2.pdf',3030,0,'2025-04-23 11:48:36',NULL),(69,'203821655_10226477269460316_3028584662214159215_n.jpg','69-203821655-10226477269460316-3028584662214159215-n.jpeg',80903,0,'2025-04-23 11:48:36',NULL),(70,'203956436_10226477268660296_1686485296554203402_n.jpg','70-203956436-10226477268660296-1686485296554203402-n.jpeg',100205,0,'2025-04-23 11:48:36',NULL),(71,'203821655_10226477269460316_3028584662214159215_n.jpg','71-203821655-10226477269460316-3028584662214159215-n.jpeg',80903,0,'2025-04-23 11:48:36',NULL),(72,'203821655_10226477269460316_3028584662214159215_n.jpg','72-203821655-10226477269460316-3028584662214159215-n.jpeg',80903,0,'2025-04-23 11:48:36',NULL),(73,'203821655_10226477269460316_3028584662214159215_n.jpg','73-203821655-10226477269460316-3028584662214159215-n.jpeg',80903,0,'2025-04-23 11:48:36',NULL),(74,'203821655_10226477269460316_3028584662214159215_n.jpg','74-203821655-10226477269460316-3028584662214159215-n.jpeg',80903,0,'2025-04-23 11:48:36',NULL),(75,'203821655_10226477269460316_3028584662214159215_n.jpg','75-203821655-10226477269460316-3028584662214159215-n.jpeg',80903,0,'2025-04-23 11:48:36',NULL),(76,'4287454258.jpg','76-4287454258.jpeg',491793,0,'2025-04-23 11:48:36',NULL),(77,'registration_side_img (3).jpg','77-registration-side-img-3.jpeg',4632,0,'2025-04-23 11:48:36',NULL),(78,'payment-detail-T438632305 (2).pdf','78-payment-detail-t438632305-2.pdf',3030,0,'2025-04-23 11:48:36',NULL),(79,'payment-detail-T438632305 (2).pdf','79-payment-detail-t438632305-2.pdf',3030,0,'2025-04-23 11:48:36',NULL),(80,'payment-detail-T438632305 (2).pdf','80-payment-detail-t438632305-2.pdf',3030,0,'2025-04-23 11:48:36',NULL),(81,'payment-detail-T438632305 (2).pdf','81-payment-detail-t438632305-2.pdf',3030,0,'2025-04-23 11:48:36',NULL),(82,'lake-4841884_1920.jpg','82-lake-4841884-1920.jpeg',312203,0,'2025-04-23 11:48:36',NULL),(83,'payment-detail-T438632305 (2).pdf','83-payment-detail-t438632305-2.pdf',3030,0,'2025-04-23 11:48:36',NULL),(84,'lake-4841884_1920.jpg','84-lake-4841884-1920.jpeg',312203,0,'2025-04-23 11:48:36',NULL),(85,'payment-detail-T438632305 (2).pdf','85-payment-detail-t438632305-2.pdf',3030,0,'2025-04-23 11:48:36',NULL),(86,'payment-detail-T438632305 (2).pdf','86-payment-detail-t438632305-2.pdf',3030,0,'2025-04-23 11:48:36',NULL),(87,'payment-detail-T438632305 (2).pdf','87-payment-detail-t438632305-2.pdf',3030,0,'2025-04-23 11:48:36',NULL),(88,'payment-detail-T438632305 (2).pdf','88-payment-detail-t438632305-2.pdf',3030,0,'2025-04-23 11:48:36',NULL),(89,'payment-detail-T438632305 (2).pdf','89-payment-detail-t438632305-2.pdf',3030,0,'2025-04-23 11:48:36',NULL),(90,'payment-detail-T438632305 (2).pdf','90-payment-detail-t438632305-2.pdf',3030,0,'2025-04-23 11:48:36',NULL),(91,'berlin-4679964_1920.jpg','91-berlin-4679964-1920.jpeg',615867,0,'2025-04-23 11:48:36',NULL),(92,'m4-177.jpg','92-m4-177.jpeg',333275,0,'2025-04-23 11:48:36',NULL),(93,'m4-177.jpg','93-m4-177.jpeg',333275,0,'2025-04-23 11:48:36',NULL),(94,'m4-177.jpg','94-m4-177.jpeg',333275,0,'2025-04-23 11:48:36',NULL),(95,'123388959_10221854227656351_5532550917549873870_o.jpg','95-123388959-10221854227656351-5532550917549873870-o.jpeg',384737,0,'2025-04-23 11:48:36',NULL),(96,'123388959_10221854227656351_5532550917549873870_o.jpg','96-123388959-10221854227656351-5532550917549873870-o.jpeg',384737,0,'2025-04-23 11:48:36',NULL),(97,'03-w1920.jpg','97-03-w1920.jpeg',3033314,0,'2025-04-23 11:48:36',NULL),(98,'A4.pdf','98-a4.pdf',15730,0,'2025-04-23 11:48:36',NULL),(99,'bydleni-mobil.jpg','99-bydleni-mobil.jpeg',97257,0,'2025-04-23 11:48:36',NULL),(100,'A4.pdf','100-a4.pdf',15730,0,'2025-04-23 11:48:36',NULL),(101,'A4.pdf','101-a4.pdf',15730,0,'2025-04-23 11:48:36',NULL),(102,'clinic-material-4.pdf','102-clinic-material-4.pdf',1061611,0,'2025-04-23 11:48:36',NULL),(103,'laboratorni-zprava.pdf','103-laboratorni-zprava.pdf',2079698,0,'2025-04-23 11:48:36',NULL),(104,'laboratorni-zprava.pdf','104-laboratorni-zprava.pdf',2079698,1,'2025-04-23 11:48:36','2025-05-13 14:22:10'),(105,'laboratorni-zprava.pdf','105-laboratorni-zprava.pdf',2079698,1,'2025-04-23 11:48:36','2025-05-13 14:22:08'),(106,'524000555.jpg','_deleted_106-524000555.jpeg',277252,1,NULL,'2025-05-13 14:22:04');
/*!40000 ALTER TABLE `file` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `form_failed_submission`
--

DROP TABLE IF EXISTS `form_failed_submission`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `form_failed_submission` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `form_type` varchar(64) NOT NULL,
  `discriminator` text NOT NULL,
  `ip_address` varbinary(16) NOT NULL,
  `attempted_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `form_type` (`form_type`,`attempted_at`),
  KEY `attempted_at` (`attempted_at`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `form_failed_submission`
--

LOCK TABLES `form_failed_submission` WRITE;
/*!40000 ALTER TABLE `form_failed_submission` DISABLE KEYS */;
/*!40000 ALTER TABLE `form_failed_submission` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `holiday`
--

DROP TABLE IF EXISTS `holiday`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `holiday` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(250) COLLATE utf8_czech_ci NOT NULL,
  `publicFrom` date NOT NULL,
  `publicTo` date NOT NULL,
  `created` datetime NOT NULL,
  `public` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `public_publicTo_publicFrom` (`public`,`publicTo`,`publicFrom`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_czech_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `holiday`
--

LOCK TABLES `holiday` WRITE;
/*!40000 ALTER TABLE `holiday` DISABLE KEYS */;
/*!40000 ALTER TABLE `holiday` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `image`
--

DROP TABLE IF EXISTS `image`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `image` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `filename` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `libraryId` int(11) DEFAULT NULL,
  `sort` int(11) DEFAULT NULL,
  `sourceImage` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `md5` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `alts` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `timeOfChange` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `cat_idx` (`libraryId`),
  CONSTRAINT `cat` FOREIGN KEY (`libraryId`) REFERENCES `library_tree` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=171 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `image`
--

LOCK TABLES `image` WRITE;
/*!40000 ALTER TABLE `image` DISABLE KEYS */;
INSERT INTO `image` VALUES (11,'superkoderi@oldrichhrb_1200px-10','11-superkoderi-oldrichhrb-1200px-10.jpg',1,-11,NULL,NULL,NULL,NULL),(13,'superkoderi@oldrichhrb_1200px-09','13-superkoderi-oldrichhrb-1200px-09.jpg',1,-13,NULL,NULL,NULL,NULL),(14,'superkoderi@oldrichhrb_1200px-07','14-superkoderi-oldrichhrb-1200px-07.jpg',1,-14,NULL,NULL,NULL,NULL),(15,'superkoderi@oldrichhrb_1200px-05','15-superkoderi-oldrichhrb-1200px-05.jpg',1,-15,NULL,NULL,NULL,NULL),(16,'superkoderi@oldrichhrb_1200px-08','16-superkoderi-oldrichhrb-1200px-08.jpg',1,-16,NULL,NULL,NULL,NULL),(18,'superkoderi@oldrichhrb_1200px-03','18-superkoderi-oldrichhrb-1200px-03.jpg',1,-18,NULL,NULL,NULL,NULL),(19,'superkoderi@oldrichhrb_1200px-02','19-superkoderi-oldrichhrb-1200px-02.jpg',1,-19,NULL,NULL,NULL,NULL),(20,'superkoderi@oldrichhrb_1200px-01','20-superkoderi-oldrichhrb-1200px-01.jpg',1,-20,NULL,NULL,NULL,NULL),(22,'1-1_a_logo','22-1-1-a-logo.svg',1,-22,NULL,NULL,NULL,NULL),(23,'lake-4841884_1920','23-lake-4841884-1920.jpeg',1,-23,NULL,NULL,NULL,NULL),(24,'lake-4841884_1920','24-lake-4841884-1920.jpeg',1,-24,NULL,NULL,NULL,NULL),(25,'toby-elliott-m3SrHEMrmbQ-unsplash','25-toby-elliott-m3srhemrmbq-unsplash.jpeg',4,-25,NULL,NULL,NULL,NULL),(26,'lesly-juarez-uR7DBrAa4HE-unsplash','26-lesly-juarez-ur7dbraa4he-unsplash.jpeg',4,-26,NULL,NULL,NULL,NULL),(27,'toan-chu-pKFCH2t00wA-unsplash','27-toan-chu-pkfch2t00wa-unsplash.jpeg',4,-27,NULL,NULL,NULL,NULL),(28,'cameron-venti-pqyvyqqa87s-unsplash','28-cameron-venti-pqyvyqqa87s-unsplash.jpeg',4,-28,NULL,NULL,NULL,NULL),(29,'girl-with-red-hat-sLcVe47YUJI-unsplash','29-girl-with-red-hat-slcve47yuji-unsplash.jpeg',4,-29,NULL,NULL,NULL,NULL),(30,'caseen-kyle-registos-1ht1wnmfDiA-unsplash','30-caseen-kyle-registos-1ht1wnmfdia-unsplash.jpeg',4,-30,NULL,NULL,NULL,NULL),(31,'sergey-pesterev-XaidrBZfEwU-unsplash','31-sergey-pesterev-xaidrbzfewu-unsplash.jpeg',4,-31,NULL,NULL,NULL,NULL),(32,'vzduchovka-hammerli-hunter-force-900-combo-4-5mm-55359.2519740793.1623055930','32-vzduchovka-hammerli-hunter-force-900-combo-4-5mm-55359.2519740793.1623055930.jpeg',5,-32,NULL,NULL,NULL,NULL),(33,'sdvsfgnehmhadsfds.2519740793.1623055896','33-sdvsfgnehmhadsfds.2519740793.1623055896.jpeg',5,-33,NULL,NULL,NULL,NULL),(34,'vzduchovka-hammerli-hunter-force-900-combo-4-5mm-55357.2519740793.1623055930','34-vzduchovka-hammerli-hunter-force-900-combo-4-5mm-55357.2519740793.1623055930.jpeg',5,-34,NULL,NULL,NULL,NULL),(35,'vzduchovka-hammerli-hunter-force-900-combo-4-5mm-55362.2519740793.1623055930','35-vzduchovka-hammerli-hunter-force-900-combo-4-5mm-55362.2519740793.1623055930.jpeg',5,-35,NULL,NULL,NULL,NULL),(36,'vzduchovka-hammerli-hunter-force-900-combo-4-5mm-55358.2519740793.1623055930','36-vzduchovka-hammerli-hunter-force-900-combo-4-5mm-55358.2519740793.1623055930.jpeg',5,-36,NULL,NULL,NULL,NULL),(37,'vzduchovka-hammerli-hunter-force-900-combo-4-5mm-55363.2519740793.1623055930','37-vzduchovka-hammerli-hunter-force-900-combo-4-5mm-55363.2519740793.1623055930.jpeg',5,-37,NULL,NULL,NULL,NULL),(38,'vzduchovka-hammerli-hunter-force-900-combo-4-5mm-55361.2519740793.1623055930','38-vzduchovka-hammerli-hunter-force-900-combo-4-5mm-55361.2519740793.1623055930.jpeg',5,-38,NULL,NULL,NULL,NULL),(39,'vzduchovka-hammerli-hunter-force-900-combo-4-5mm-55360.2519740793.1623055930','39-vzduchovka-hammerli-hunter-force-900-combo-4-5mm-55360.2519740793.1623055930.jpeg',5,-39,NULL,NULL,NULL,NULL),(40,'vzduchovka-kral-arms-n-08-camo-5-5mm.2519740793.1610146923','40-vzduchovka-kral-arms-n-08-camo-5-5mm.2519740793.1610146923.jpeg',5,-40,NULL,NULL,NULL,NULL),(41,'vzduchovka-kral-arms-n-08-camo-5-5mm-46295.2519740793.1610146923','41-vzduchovka-kral-arms-n-08-camo-5-5mm-46295.2519740793.1610146923.jpeg',5,-41,NULL,NULL,NULL,NULL),(42,'vzduchovka-kral-arms-n-08-camo-5-5mm-46296.2519740793.1610146923','42-vzduchovka-kral-arms-n-08-camo-5-5mm-46296.2519740793.1610146923.jpeg',5,-42,NULL,NULL,NULL,NULL),(43,'vzduchovka-kral-arms-n-08-camo-5-5mm-46299.2519740793.1610146923','43-vzduchovka-kral-arms-n-08-camo-5-5mm-46299.2519740793.1610146923.jpeg',5,-43,NULL,NULL,NULL,NULL),(44,'vzduchovka-kral-arms-n-08-camo-5-5mm-46293.2519740793.1610146923','44-vzduchovka-kral-arms-n-08-camo-5-5mm-46293.2519740793.1610146923.jpeg',5,-44,NULL,NULL,NULL,NULL),(45,'vzduchovka-kral-arms-n-08-camo-5-5mm-46297.2519740793.1610146923','45-vzduchovka-kral-arms-n-08-camo-5-5mm-46297.2519740793.1610146923.jpeg',5,-45,NULL,NULL,NULL,NULL),(46,'vzduchovka-kral-arms-n-08-camo-5-5mm-46294.2519740793.1610146923','46-vzduchovka-kral-arms-n-08-camo-5-5mm-46294.2519740793.1610146923.jpeg',5,-46,NULL,NULL,NULL,NULL),(47,'vzduchovka-kral-arms-n-08-camo-5-5mm-46298.2519740793.1610146923','47-vzduchovka-kral-arms-n-08-camo-5-5mm-46298.2519740793.1610146923.jpeg',5,-47,NULL,NULL,NULL,NULL),(48,'lake-4841884_1920','48-lake-4841884-1920.jpeg',4,-48,NULL,NULL,NULL,NULL),(49,'intro','49-intro.jpeg',1,-49,NULL,NULL,NULL,NULL),(50,'m4-177','50-m4-177.jpeg',1,-50,NULL,NULL,NULL,NULL),(51,'m4-177','51-m4-177.jpeg',1,-51,NULL,NULL,NULL,NULL),(52,'m4-177','52-m4-177.jpeg',5,-52,NULL,NULL,NULL,NULL),(53,'m4-177','53-m4-177.jpeg',5,-53,NULL,NULL,NULL,NULL),(54,'berlin-4679964_1920','54-berlin-4679964-1920.jpeg',1,-54,NULL,NULL,NULL,NULL),(55,'nuclear-weapons-test-67557_1920','55-nuclear-weapons-test-67557-1920.jpeg',1,-55,NULL,NULL,NULL,NULL),(56,'nuclear-weapons-test-67557_1920','56-nuclear-weapons-test-67557-1920.jpeg',1,-56,NULL,NULL,NULL,NULL),(57,'berlin-4679964_1920','57-berlin-4679964-1920.jpeg',1,-57,NULL,NULL,NULL,NULL),(58,'bangkok-4864747_1920','58-bangkok-4864747-1920.jpeg',1,-58,NULL,NULL,NULL,NULL),(59,'lake-4841884_1920','59-lake-4841884-1920.jpeg',1,-59,NULL,NULL,NULL,NULL),(63,'feature-1','63-feature-1.jpeg',4,-63,NULL,NULL,NULL,NULL),(64,'symbol-gradient-gold-mint-black','64-symbol-gradient-gold-mint-black.png',1,-64,NULL,NULL,NULL,NULL),(65,'07_03-pracovna','65-07-03-pracovna.jpeg',5,-65,NULL,NULL,NULL,NULL),(66,'07_03-pracovna','66-07-03-pracovna.jpeg',5,-66,NULL,NULL,NULL,NULL),(67,'07_03-pracovna','67-07-03-pracovna.jpeg',5,-67,NULL,NULL,NULL,NULL),(68,'07_03-pracovna','68-07-03-pracovna.jpeg',5,-68,NULL,NULL,NULL,NULL),(69,'07_03-pracovna','69-07-03-pracovna.jpeg',5,-69,NULL,NULL,NULL,NULL),(70,'07_03-pracovna','70-07-03-pracovna.jpeg',5,-70,NULL,NULL,NULL,NULL),(71,'07_03-pracovna','71-07-03-pracovna.jpeg',5,-71,NULL,NULL,NULL,NULL),(72,'07_03-pracovna','72-07-03-pracovna.jpeg',5,-72,NULL,NULL,NULL,NULL),(73,'07_03-pracovna','73-07-03-pracovna.jpeg',5,-73,NULL,NULL,NULL,NULL),(74,'07_03-pracovna','74-07-03-pracovna.jpeg',5,-74,NULL,NULL,NULL,NULL),(75,'07_03-pracovna','75-07-03-pracovna.jpeg',5,-75,NULL,NULL,NULL,NULL),(76,'07_03-pracovna','76-07-03-pracovna.jpeg',5,-76,NULL,NULL,NULL,NULL),(77,'07_03-pracovna','77-07-03-pracovna.jpeg',5,-77,NULL,NULL,NULL,NULL),(79,'tomas-zeman','79-tomas-zeman.png',1,-79,NULL,NULL,NULL,NULL),(80,'gallery-large-03','80-gallery-large-03.jpeg',5,-80,NULL,NULL,NULL,NULL),(81,'conibase','81-conibase.svg',1,-81,NULL,NULL,'{}',NULL),(82,'spotify','82-spotify.svg',1,-82,NULL,NULL,'{}',NULL),(83,'800-1200x130','83-800-1200x130.jpg',1,-83,NULL,NULL,'{}',NULL),(84,'524000555','84-524000555.jpg',1,-84,NULL,NULL,'{}',NULL),(85,'1F3E2 1','85-1f3e2-1.svg',1,-85,NULL,NULL,'{}',NULL),(87,'cover-rozcestnik','87-cover-rozcestnik.jpg',1,-87,NULL,NULL,'{}',NULL),(88,'map','88-map.svg',1,-88,NULL,NULL,'{}',NULL),(89,'8ec59e4a32d2d7b30f8cc4c23786b4ff0cae2b10','89-8ec59e4a32d2d7b30f8cc4c23786b4ff0cae2b10.png',1,-89,NULL,NULL,'{}',NULL),(90,'team','90-team.png',1,-90,NULL,NULL,'{}',NULL),(91,'f42f8e1aac161bf82d36d6fd7545a2b67caa999c','91-f42f8e1aac161bf82d36d6fd7545a2b67caa999c.png',6,-91,NULL,NULL,'{}',NULL),(92,'f70261227d92656d7eb9e05774f43988ca35e91f','92-f70261227d92656d7eb9e05774f43988ca35e91f.png',6,-92,NULL,NULL,'{}',NULL),(93,'b787cee10bbd608b1818219f8fb2524b7ce463f0','93-b787cee10bbd608b1818219f8fb2524b7ce463f0.png',6,-93,NULL,NULL,'{}',NULL),(94,'966e82da2ce1dd97787de4540901736f21ce62dc','94-966e82da2ce1dd97787de4540901736f21ce62dc.png',1,-94,NULL,NULL,'{}',NULL),(95,'39489382c33cb9209ffa1ce980bffca10707daa5','95-39489382c33cb9209ffa1ce980bffca10707daa5.png',1,-95,NULL,NULL,'{}',NULL),(96,'8f98944bc5f16d33c6b1ae27260d8e1fea9d3b47','96-8f98944bc5f16d33c6b1ae27260d8e1fea9d3b47.png',1,-96,NULL,NULL,'{}',NULL),(97,'7dbf044ea34937f7c7b5b8c9a76521a1bd42340e','97-7dbf044ea34937f7c7b5b8c9a76521a1bd42340e.png',1,-97,NULL,NULL,'{}',NULL),(98,'d1f1444faee254889c9b1c88c150ff38093f9143','98-d1f1444faee254889c9b1c88c150ff38093f9143.png',1,-98,NULL,NULL,'{}',NULL),(99,'2fb94affd17c229f389734cca519ee96dc91b251','99-2fb94affd17c229f389734cca519ee96dc91b251.png',1,-99,NULL,NULL,'{}',NULL),(100,'63a9fbdb2ff55986d4b64fd671fc8df25e6735c0','100-63a9fbdb2ff55986d4b64fd671fc8df25e6735c0.png',1,-100,NULL,NULL,'{}',NULL),(101,'eac002093adece75acdd8411d78b4b334d7be4af','101-eac002093adece75acdd8411d78b4b334d7be4af.png',1,-101,NULL,NULL,'{}',NULL),(102,'de3b9be0840327a0b6395d3af470c95474bded8d','102-de3b9be0840327a0b6395d3af470c95474bded8d.png',1,-102,NULL,NULL,'{}',NULL),(103,'eJ26Qgqf5d8oheQgcqJw7bShSilwhpEw-136','103-ej26qgqf5d8oheqgcqjw7bshsilwhpew-136.png',1,-103,NULL,NULL,'{}',NULL),(104,'qhbrEBGwpEfFXvOj1LtQoKkuDR8aaE6J-136','104-qhbrebgwpeffxvoj1ltqokkudr8aae6j-136.png',1,-104,NULL,NULL,'{}',NULL),(105,'wB97qDeBVRvXWdiiGuUqsBA2kL0Xhtxz-136','105-wb97qdebvrvxwdiiguuqsba2kl0xhtxz-136.png',1,-105,NULL,NULL,'{}',NULL),(106,'Vl6VbX7FOxGMNpbcaG9MEDTan7wT34rh-136','106-vl6vbx7foxgmnpbcag9medtan7wt34rh-136.png',1,-106,NULL,NULL,'{}',NULL),(107,'qnqYoxwbrovkAPrJg2Jz9KhcZEsD0NwG-136','107-qnqyoxwbrovkaprjg2jz9khczesd0nwg-136.png',1,-107,NULL,NULL,'{}',NULL),(108,'YYT9Z9CPDYlZ5FNBWWexxyGbNcCB2KbO-136','108-yyt9z9cpdylz5fnbwwexxygbnccb2kbo-136.png',1,-108,NULL,NULL,'{}',NULL),(109,'HWSyL4BmZbN4nrqt1NLNtKjgfjiIYllb-136','109-hwsyl4bmzbn4nrqt1nlntkjgfjiiyllb-136.png',1,-109,NULL,NULL,'{}',NULL),(110,'IXmCDQVaKwLeuexQ9EF1BTO7294TNBtg-136','110-ixmcdqvakwleuexq9ef1bto7294tnbtg-136.png',1,-110,NULL,NULL,'{}',NULL),(111,'ABZ5DE8twqRS46AGytdxxKTehiWJEYTF-136','111-abz5de8twqrs46agytdxxktehiwjeytf-136.png',1,-111,NULL,NULL,'{}',NULL),(112,'9bHlFr3Cm7G15MtRsNN7ZsBGHDMUpdll-136','112-9bhlfr3cm7g15mtrsnn7zsbghdmupdll-136.png',1,-112,NULL,NULL,'{}',NULL),(113,'ZhWCpAq8a5djauEcK6YXyp7LwkLr6QBm-136','113-zhwcpaq8a5djaueck6yxyp7lwklr6qbm-136.png',1,-113,NULL,NULL,'{}',NULL),(114,'9X4gC8wvSiQGiGjxSW7GGbd6i49ImzWb-136','114-9x4gc8wvsiqgigjxsw7ggbd6i49imzwb-136.png',1,-114,NULL,NULL,'{}',NULL),(115,'f2dbytX1b6njCUNUpbMtj3mk9z9uLBTf-136','115-f2dbytx1b6njcunupbmtj3mk9z9ulbtf-136.png',1,-115,NULL,NULL,'{}',NULL),(116,'8VqWMbmNannNqgxfRFnab0OalodhXkWE-136','116-8vqwmbmnannnqgxfrfnab0oalodhxkwe-136.png',1,-116,NULL,NULL,'{}',NULL),(117,'ZAEBfFChkLcyHM9uUp6YlgaDzsEQ5ZQw-136','117-zaebffchklcyhm9uup6ylgadzseq5zqw-136.png',1,-117,NULL,NULL,'{}',NULL),(118,'LMHvasPJ54KVJPXF7mjq4lQ8B1Up0XCg-136','118-lmhvaspj54kvjpxf7mjq4lq8b1up0xcg-136.png',1,-118,NULL,NULL,'{}',NULL),(119,'4YRgnzhTdBUDc2CX85le1zQGc17LJEj3-136','119-4yrgnzhtdbudc2cx85le1zqgc17ljej3-136.png',1,-119,NULL,NULL,'{}',NULL),(120,'EF1whbYE8mXNcs4wPBniS3TuGBIwybbJ-136','120-ef1whbye8mxncs4wpbnis3tugbiwybbj-136.png',1,-120,NULL,NULL,'{}',NULL),(121,'oC3bI2RXxi6rC0anB5Poc3AEwLs5B873-136','121-oc3bi2rxxi6rc0anb5poc3aewls5b873-136.png',1,-121,NULL,NULL,'{}',NULL),(122,'MOjwPTjBdjSBEyW6EFyzDv7O7STUHENL-136','122-mojwptjbdjsbeyw6efyzdv7o7stuhenl-136.png',1,-122,NULL,NULL,'{}',NULL),(123,'1Ko1VcBBvlKMkbJO1yYvFjfgmaXnpkS8-136','123-1ko1vcbbvlkmkbjo1yyvfjfgmaxnpks8-136.png',1,-123,NULL,NULL,'{}',NULL),(124,'2ykHMaTPjpM2zET4gmiMTzPKlpKBGc9z-136','124-2ykhmatpjpm2zet4gmimtzpklpkbgc9z-136.png',1,-124,NULL,NULL,'{}',NULL),(125,'image2','125-image2.png',1,-125,NULL,NULL,'{}',NULL),(126,'image','126-image.png',1,-126,NULL,NULL,'{}',NULL),(127,'Frame 159','127-frame-159.png',1,-127,NULL,NULL,'{}',NULL),(128,'image 53','128-image-53.png',1,-128,NULL,NULL,'{}',NULL),(129,'Snímek obrazovky 2024-12-17 v 22.40.02 1','129-snimek-obrazovky-2024-12-17-v-22.40.02-1.png',1,-129,NULL,NULL,'{}',NULL),(130,'image 54','130-image-54.png',1,-130,NULL,NULL,'{}',NULL),(131,'image 52','131-image-52.png',1,-131,NULL,NULL,'{}',NULL),(132,'Frame 54','132-frame-54.png',1,-132,NULL,NULL,'{}',NULL),(133,'1F3D7 2','133-1f3d7-2.svg',1,-133,NULL,NULL,'{}',NULL),(134,'10-2','134-10-2.svg',1,-134,NULL,NULL,'{}',NULL),(135,'1F3E2 1','135-1f3e2-1.svg',1,-135,NULL,NULL,'{}',NULL),(136,'roman-tomek','136-roman-tomek.png',1,-136,NULL,NULL,'{}',NULL),(137,'jiri-hradsky','137-jiri-hradsky.png',1,-137,NULL,NULL,'{}',NULL),(138,'michaela-puskar-garajova','138-michaela-puskar-garajova.png',1,-138,NULL,NULL,'{}',NULL),(139,'david-supej','139-david-supej.png',1,-139,NULL,NULL,'{}',NULL),(140,'pavel-cech','140-pavel-cech.png',1,-140,NULL,NULL,'{}',NULL),(141,'image','141-image.svg',1,-141,NULL,NULL,'{}',NULL),(142,'Foto','142-foto.png',1,-142,NULL,NULL,'{}',NULL),(143,'91df54209755199f423a834e275b90c88ff4390d','143-91df54209755199f423a834e275b90c88ff4390d.png',1,-143,NULL,NULL,'{}',NULL),(144,'Foto','144-foto.png',1,-144,NULL,NULL,'{}',NULL),(145,'zhGaBB5yh9pKZCYbm2XES2BjaH3XObal','145-zhgabb5yh9pkzcybm2xes2bjah3xobal.webp',1,-145,NULL,NULL,'{}',NULL),(147,'jana-sedlakova','147-jana-sedlakova.jpeg',1,-147,NULL,NULL,'{}',NULL),(148,'jana-sedlakova','148-jana-sedlakova.jpeg',1,-148,NULL,NULL,'{}',NULL),(149,'pavel-cech','149-pavel-cech.png',1,-149,NULL,NULL,'{}',NULL),(150,'roman-tomek','150-roman-tomek.png',1,-150,NULL,NULL,'{}',NULL),(151,'PgJmcj068TSJWx6GnpJRTMvInDDhlWoG','151-pgjmcj068tsjwx6gnpjrtmvinddhlwog.svg',1,-151,NULL,NULL,'{}',NULL),(152,'m2Cyrubpg8VOFD7oD8tsC4GkUWvEbOFQ','152-m2cyrubpg8vofd7od8tsc4gkuwvebofq.svg',1,-152,NULL,NULL,'{}',NULL),(153,'L1yw8YDD96rJGTWaGAHLqpPUuzFv0dBq','153-l1yw8ydd96rjgtwagahlqppuuzfv0dbq.svg',1,-153,NULL,NULL,'{}',NULL),(154,'npRHIv3J4hguyMPo7j8W6gGWcqtKL87c','154-nprhiv3j4hguympo7j8w6ggwcqtkl87c.jpeg',1,-154,NULL,NULL,'{}',NULL),(155,'84e5004cc30fc0634785fa262d8b41b8379c2e89','155-84e5004cc30fc0634785fa262d8b41b8379c2e89.png',1,-155,NULL,NULL,'{}',NULL),(156,'fe6e8936ecefcb881bf79f60dfcca9ef4bc7eb6d','156-fe6e8936ecefcb881bf79f60dfcca9ef4bc7eb6d.png',1,-156,NULL,NULL,'{}',NULL),(157,'cut4','157-cut4.png',1,-157,NULL,NULL,'{}',NULL),(158,'cut2','158-cut2.png',1,-158,NULL,NULL,'{}',NULL),(159,'cut3','159-cut3.png',1,-159,NULL,NULL,'{}',NULL),(160,'cut','160-cut.png',1,-160,NULL,NULL,'{}',NULL),(161,'foto-kariera','161-foto-kariera.png',1,-161,NULL,NULL,'{}',NULL),(162,'Frame 128','162-frame-128.png',1,-162,NULL,NULL,'{}',NULL),(163,'68eb517071a501d59063ff46d398a8a42d75bd34','163-68eb517071a501d59063ff46d398a8a42d75bd34.png',1,-163,NULL,NULL,'{}',NULL),(164,'jana','164-jana.png',1,-164,NULL,NULL,'{}',NULL),(165,'david','165-david.png',1,-165,NULL,NULL,'{}',NULL),(166,'45OSyB4vJJuRZSAkrv15nfa3o0j6Cqji-1200-optimized','166-45osyb4vjjurzsakrv15nfa3o0j6cqji-1200-optimized.webp',1,-166,NULL,NULL,'{}',NULL),(167,'ZBVJ564Yq7us9S2gZ2Oy7wPNcJzaXFRz-1200-optimized','167-zbvj564yq7us9s2gz2oy7wpncjzaxfrz-1200-optimized.webp',1,-167,NULL,NULL,'{}',NULL),(168,'SN8NLHbQS7J7gnbtxmWeEQqHHXYCBVgi-1200-optimized','168-sn8nlhbqs7j7gnbtxmweeqqhhxycbvgi-1200-optimized.webp',1,-168,NULL,NULL,'{}',NULL),(169,'sbmsfJPccsot5EwBLMwFbGGHeN6wV2eV-1200-optimized','169-sbmsfjpccsot5ewblmwfbgghen6wv2ev-1200-optimized.webp',1,-169,NULL,NULL,'{}',NULL),(170,'bJEcbYapZaKPJgzOBSuCwsNJ8OFj1whT-1200-optimized','170-bjecbyapzakpjgzobsucwsnj8ofj1wht-1200-optimized.webp',1,-170,NULL,NULL,'{}',NULL);
/*!40000 ALTER TABLE `image` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `import_cache`
--

DROP TABLE IF EXISTS `import_cache`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `import_cache` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` varchar(256) COLLATE utf8mb4_czech_ci NOT NULL,
  `data` mediumtext COLLATE utf8mb4_czech_ci NOT NULL,
  `status` enum('new','ready','imported','error','processing','skipped') COLLATE utf8mb4_czech_ci NOT NULL DEFAULT 'new',
  `extId` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `createdTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `importedTime` datetime DEFAULT NULL,
  `message` text COLLATE utf8mb4_czech_ci,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `type_status_createdTime` (`type`,`status`,`createdTime`) USING BTREE,
  KEY `createdTime` (`createdTime`) USING BTREE,
  KEY `status` (`status`) USING BTREE,
  KEY `type_status_extId` (`type`,`status`,`extId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_czech_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `import_cache`
--

LOCK TABLES `import_cache` WRITE;
/*!40000 ALTER TABLE `import_cache` DISABLE KEYS */;
/*!40000 ALTER TABLE `import_cache` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `library_tree`
--

DROP TABLE IF EXISTS `library_tree`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `library_tree` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `parentId` int(11) DEFAULT NULL,
  `level` tinyint(4) DEFAULT NULL,
  `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `sort` mediumint(9) DEFAULT NULL,
  `last` tinyint(1) DEFAULT NULL,
  `created` int(11) NOT NULL,
  `createdTime` datetime NOT NULL,
  `edited` int(11) NOT NULL,
  `editedTime` datetime NOT NULL,
  `publicFrom` datetime DEFAULT NULL,
  `publicTo` datetime DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `nameTitle` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `nameAnchor` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `uid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idParent` (`parentId`),
  CONSTRAINT `library_tree_ibfk_2` FOREIGN KEY (`parentId`) REFERENCES `library_tree` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `library_tree`
--

LOCK TABLES `library_tree` WRITE;
/*!40000 ALTER TABLE `library_tree` DISABLE KEYS */;
INSERT INTO `library_tree` VALUES (1,NULL,NULL,NULL,NULL,0,0,'2021-06-28 09:26:24',1,'2013-10-03 18:19:28','2021-06-28 09:26:24','2021-06-28 09:26:24','Knihovna obrázků','Knihovna obrázků','Knihovna obrázků',NULL),(2,1,1,'1|',NULL,1,0,'2021-06-28 09:26:24',1,'2013-10-03 18:19:28','2021-06-28 09:26:24','2021-06-28 09:26:24','Výchozí složka','Výchozí složka','Výchozí složka','default'),(4,1,1,'1|',1,1,1,'2021-06-28 09:26:24',1,'2021-06-28 09:26:24','2021-06-28 09:26:24','2100-01-01 00:00:00','Sample images','Enter a new name','Enter a new name',NULL),(5,1,1,'1|',1,1,9,'2021-07-08 13:44:29',9,'2021-07-08 13:44:29','2021-07-08 13:44:29','2100-01-01 00:00:00','Produkty','Enter a new name','Enter a new name',NULL),(6,1,1,'1|',1,1,34,'2025-05-07 15:02:09',34,'2025-05-07 15:02:09','2025-05-07 15:02:09','2100-01-01 00:00:00','Pobočky','Nová stránka','Nová stránka',NULL);
/*!40000 ALTER TABLE `library_tree` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `log`
--

DROP TABLE IF EXISTS `log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `message` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `context` longtext COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `createdAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `log`
--

LOCK TABLES `log` WRITE;
/*!40000 ALTER TABLE `log` DISABLE KEYS */;
/*!40000 ALTER TABLE `log` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `material`
--

DROP TABLE IF EXISTS `material`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `material` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `internalName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `material`
--

LOCK TABLES `material` WRITE;
/*!40000 ALTER TABLE `material` DISABLE KEYS */;
INSERT INTO `material` VALUES (1,'Co musíte řešit při prodeji firmy','{\"base\":[{\"annot\":[{\"bg\":\"u-bgc-green\",\"btn\":[{\"toggle\":\"customHref\",\"customHref\":[{\"href\":\"#\",\"hrefName\":\"Stáhnout e-book\"}]}],\"right\":[{\"image\":\"163\"}]}],\"highlights\":[{\"title\":\"Co se dozvíte\",\"items\":[{\"image\":\"135\",\"name\":\"Highlight 1\",\"text\":\"Solve a problem or close a sale in real-time with chat. If no one is available, customers are seamlessly routed to email without confusion.\"},{\"image\":\"134\",\"name\":\"Highlight 2\",\"text\":\"Explore 100+ integrations that make your day-to-day workflow more efficient and familiar. Plus, our extensive developer tools.\"},{\"image\":\"133\",\"name\":\"Highlight 3\",\"text\":\"We’re an extension of your customer service team, and all of our resources are free. Chat to our friendly team 24/7 when you need help.\"}]}],\"content\":[{\"content\":\"<h4>První pokusy: Když se nedaří trefit správné jméno</h4>\\n<p>Zakladatelé Denisa Hrubešová a David Slačálek měli od začátku jasno – vytvořit úspěšnou českou platformu pro tvůrce a jejich fanoušky. Co jim ale dělalo problém? Najít název. \\u2028<br />„Náš první právník nám dobře radil, že je potřeba co nejdřív zaregistrovat ochrannou známku. Ale kreativní nápady chyběly. Tak jsme použili generátor náhodných slov a vybrali si Indity,“ vzpomíná Denisa Hrubešová.\\u2028<br />O měsíc později přišla ze Španělska výzva: název Indity se až příliš podobal už existující značce. Denisa s Davidem se tak ocitli zpátky na začátku – a týden před spuštěním platformy neměli jméno. Nakonec se vymyslel název Pickey, který rovnou zaregistrovali. </p>\",\"showForm\":true}]}]}'),(2,'Jak jsme pomohli Forendors s rebrandingem','{\"base\":[{\"annot\":[{\"bg\":\"u-bgc-green\",\"btn\":[{\"toggle\":\"customHref\",\"customHref\":[{\"href\":\"#\",\"hrefName\":\"Stáhnout e-book\"}]}],\"right\":[{\"image\":\"145\"}]}],\"highlights\":[{\"title\":\"Řešení pro váš byznys\",\"items\":[{\"image\":\"135\",\"name\":\"Sales & finance\",\"text\":\"Lorem ipsum dolor sit amet, consectetuer adipiscing elit. In enim a arcu imperdiet malesuada. Donec vitae arcu. Nunc dapibus tortor vel mi dapibus sollicitudin. \",\"link\":[{\"toggle\":\"customHref\"}]},{\"image\":\"134\",\"name\":\"Týmová práce\",\"text\":\"Lorem ipsum dolor sit amet, consectetuer adipiscing elit. In enim a arcu imperdiet malesuada. Donec vitae arcu. Nunc dapibus tortor vel mi dapibus sollicitudin. \",\"link\":[{\"toggle\":\"customHref\"}]},{\"image\":\"133\",\"name\":\"Vzdělávání\",\"text\":\"Lorem ipsum dolor sit amet, consectetuer adipiscing elit. In enim a arcu imperdiet malesuada. Donec vitae arcu. Nunc dapibus tortor vel mi dapibus sollicitudin. \"}]}],\"content\":[{\"content\":\"<h3>První pokusy: Když se nedaří trefit správné jméno</h3>\\n<p>Zakladatelé Denisa Hrubešová a David Slačálek měli od začátku jasno – vytvořit úspěšnou českou platformu pro tvůrce a jejich fanoušky. Co jim ale dělalo problém? Najít název. \\u2028<br />„Náš první právník nám dobře radil, že je potřeba co nejdřív zaregistrovat ochrannou známku. Ale kreativní nápady chyběly. Tak jsme použili generátor náhodných slov a vybrali si Indity,“ vzpomíná Denisa Hrubešová.\\u2028<br />O měsíc později přišla ze Španělska výzva: název Indity se až příliš podobal už existující značce. Denisa s Davidem se tak ocitli zpátky na začátku – a týden před spuštěním platformy neměli jméno. Nakonec se vymyslel název Pickey, který rovnou zaregistrovali. </p>\",\"showForm\":true}]}]}');
/*!40000 ALTER TABLE `material` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `material_localization`
--

DROP TABLE IF EXISTS `material_localization`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `material_localization` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mutationId` int(11) NOT NULL,
  `materialId` int(11) NOT NULL,
  `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `nameAnchor` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `nameTitle` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `keywords` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `isTop` int(11) NOT NULL DEFAULT '0',
  `public` int(11) NOT NULL DEFAULT '0',
  `forceNoIndex` int(11) NOT NULL DEFAULT '0',
  `hideInSearch` int(11) NOT NULL DEFAULT '0',
  `hideInSitemap` int(11) NOT NULL DEFAULT '0',
  `publicFrom` datetime DEFAULT NULL,
  `publicTo` datetime DEFAULT NULL,
  `edited` int(11) DEFAULT NULL,
  `editedTime` datetime DEFAULT NULL,
  `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `customContentSchemeJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `customContentJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `viewsNumber` int(10) unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `FK_blog_mutation` (`mutationId`) USING BTREE,
  KEY `FK_blog_localization_blog` (`materialId`) USING BTREE,
  CONSTRAINT `material_localization_ibfk_1` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE,
  CONSTRAINT `material_localization_ibfk_2` FOREIGN KEY (`materialId`) REFERENCES `material` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `material_localization`
--

LOCK TABLES `material_localization` WRITE;
/*!40000 ALTER TABLE `material_localization` DISABLE KEYS */;
INSERT INTO `material_localization` VALUES (1,1,1,'Co musíte řešit při prodeji firmy','Co musíte řešit při prodeji firmy','Co musíte řešit při prodeji firmy','','',0,1,0,0,0,'2025-05-22 11:13:00','2125-05-22 11:13:00',34,'2025-06-06 08:24:37','{\"base\":[{\"annotation\":\"Lorem ipsum dolor sit amet, consectetuer adipiscing elit. In enim a arcu imperdiet malesuada. Donec vitae arcu. Nunc dapibus tortor vel mi dapibus sollicitudin. \\n\"}]}',NULL,'{\"testimonial____xeOVg9WudrzHt3QeKC5Q0\":[{\"testimonial\":4}]}',11),(2,1,2,'Jak jsme pomohli Forendors s rebrandingem','Jak jsme pomohli Forendors s rebrandingem','Jak jsme pomohli Forendors s rebrandingem','Lorem ipsum dolor sit amet, consectetuer adipiscing elit.Lorem ipsum dolor sit amet, consectetuer adipiscing elit.','',0,1,0,0,0,'2025-06-06 08:17:00','2125-06-06 08:17:00',34,'2025-06-06 08:21:22','{\"base\":[{\"annotation\":\"Lorem ipsum dolor sit amet, consectetuer adipiscing elit.Lorem ipsum dolor sit amet, consectetuer adipiscing elit.\"}]}',NULL,'{\"testimonial____uYC_aRgYbDiDVoG_vfqGg\":[{\"testimonial\":\"5\"}]}',3);
/*!40000 ALTER TABLE `material_localization` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `material_x_blog_tag`
--

DROP TABLE IF EXISTS `material_x_blog_tag`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `material_x_blog_tag` (
  `materialId` int(11) NOT NULL,
  `blogTagId` int(11) NOT NULL,
  PRIMARY KEY (`materialId`,`blogTagId`) USING BTREE,
  KEY `FK__blog` (`materialId`) USING BTREE,
  KEY `FK__blog_tag` (`blogTagId`) USING BTREE,
  CONSTRAINT `material_x_blog_tag_ibfk_1` FOREIGN KEY (`materialId`) REFERENCES `material` (`id`) ON DELETE CASCADE,
  CONSTRAINT `material_x_blog_tag_ibfk_2` FOREIGN KEY (`blogTagId`) REFERENCES `blog_tag` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `material_x_blog_tag`
--

LOCK TABLES `material_x_blog_tag` WRITE;
/*!40000 ALTER TABLE `material_x_blog_tag` DISABLE KEYS */;
INSERT INTO `material_x_blog_tag` VALUES (1,7),(2,7);
/*!40000 ALTER TABLE `material_x_blog_tag` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `material_x_material`
--

DROP TABLE IF EXISTS `material_x_material`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `material_x_material` (
  `materialId` int(11) NOT NULL,
  `attachedMaterialId` int(11) NOT NULL,
  PRIMARY KEY (`materialId`,`attachedMaterialId`) USING BTREE,
  KEY `FK_blog_x_blog_blog` (`materialId`) USING BTREE,
  KEY `FK_blog_x_blog_blog_2` (`attachedMaterialId`) USING BTREE,
  CONSTRAINT `material_x_material_ibfk_1` FOREIGN KEY (`materialId`) REFERENCES `material` (`id`) ON DELETE CASCADE,
  CONSTRAINT `material_x_material_ibfk_2` FOREIGN KEY (`attachedMaterialId`) REFERENCES `material` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `material_x_material`
--

LOCK TABLES `material_x_material` WRITE;
/*!40000 ALTER TABLE `material_x_material` DISABLE KEYS */;
/*!40000 ALTER TABLE `material_x_material` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `menu_main`
--

DROP TABLE IF EXISTS `menu_main`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `menu_main` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `internalName` varchar(50) CHARACTER SET utf8 COLLATE utf8_czech_ci NOT NULL DEFAULT '',
  `order` int(11) DEFAULT NULL,
  `customFieldsJson` longtext COLLATE utf8_bin,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `menu_main`
--

LOCK TABLES `menu_main` WRITE;
/*!40000 ALTER TABLE `menu_main` DISABLE KEYS */;
INSERT INTO `menu_main` VALUES (1,'FAQ',7,'{\"link\":[{\"toggle\":\"systemHref\",\"systemHref\":[{\"page\":\"8\"}]}]}'),(2,'Pro koho',1,'{\"link\":[{\"toggle\":\"systemHref\",\"systemHref\":[{\"page\":458}]}],\"settings\":[{\"nonclickable\":true}],\"submenu\":[{\"categories\":[{\"link\":[{\"toggle\":\"systemHref\",\"systemHref\":[{\"page\":471}]}],\"items\":[{\"link\":[{\"toggle\":\"systemHref\",\"systemHref\":[{\"page\":472}]}],\"icon\":\"135\"},{\"link\":[{\"toggle\":\"systemHref\",\"systemHref\":[{\"page\":473}]}],\"icon\":\"134\"},{\"link\":[{\"toggle\":\"systemHref\",\"systemHref\":[{\"page\":474}]}],\"icon\":\"133\"}],\"desc\":\"Podle firmy\"},{\"items\":[{\"link\":[{\"toggle\":\"systemHref\",\"systemHref\":[{\"page\":475}]}]},{\"link\":[{\"toggle\":\"systemHref\",\"systemHref\":[{\"page\":476}]}]},{\"link\":[{\"toggle\":\"systemHref\",\"systemHref\":[{\"page\":478}]}]},{\"link\":[{\"toggle\":\"systemHref\",\"systemHref\":[{\"page\":477}]}]},{\"link\":[{\"toggle\":\"systemHref\",\"systemHref\":[{\"page\":479}]}]}],\"desc\":\"Podle týmu\"}]}]}'),(3,'Služby',0,'{\"link\":[{\"toggle\":\"systemHref\",\"systemHref\":[{\"page\":449}]}]}'),(4,'Kontakt',NULL,'{\"link\":[{\"toggle\":\"systemHref\",\"systemHref\":[{\"page\":\"462\"}]}]}'),(5,'O nás',2,'{\"link\":[{\"toggle\":\"systemHref\",\"systemHref\":[{\"page\":\"102\"}]}]}'),(6,'Kariéra',3,'{\"link\":[{\"toggle\":\"systemHref\",\"systemHref\":[{\"page\":\"459\"}]}]}'),(7,'Reference',4,'{\"link\":[{\"toggle\":\"systemHref\",\"systemHref\":[{\"page\":\"460\"}]}]}'),(8,'Blog',5,'{\"link\":[{\"toggle\":\"systemHref\",\"systemHref\":[{\"page\":\"446\"}]}]}'),(9,'Akce',6,'{\"link\":[{\"toggle\":\"systemHref\",\"systemHref\":[{\"page\":\"461\"}]}]}');
/*!40000 ALTER TABLE `menu_main` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `menu_main_localization`
--

DROP TABLE IF EXISTS `menu_main_localization`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `menu_main_localization` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mutationId` int(11) NOT NULL,
  `menuMainId` int(11) NOT NULL,
  `name` varchar(250) CHARACTER SET utf8 COLLATE utf8_czech_ci DEFAULT '',
  `nameAnchor` varchar(250) COLLATE utf8_bin DEFAULT '',
  `nameTitle` varchar(250) COLLATE utf8_bin DEFAULT '',
  `description` text COLLATE utf8_bin,
  `keywords` text COLLATE utf8_bin,
  `title` varchar(250) COLLATE utf8_bin DEFAULT '',
  `public` tinyint(1) DEFAULT '0',
  `forceNoIndex` int(11) DEFAULT '0',
  `hideInSearch` int(11) DEFAULT '0',
  `hideInSitemap` int(11) DEFAULT '0',
  `edited` int(11) DEFAULT NULL,
  `editedTime` datetime DEFAULT NULL,
  `customFieldsJson` longtext COLLATE utf8_bin,
  `customContentJson` longtext COLLATE utf8_bin,
  `isBold` tinyint(1) NOT NULL DEFAULT '0',
  `isBig` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `mutationId_menuMainId` (`mutationId`,`menuMainId`),
  KEY `FK_menuMain_mutation` (`mutationId`) USING BTREE,
  KEY `FK_menuMain_localization_menuMain` (`menuMainId`) USING BTREE,
  CONSTRAINT `FK_menuMain_localization_menuMain` FOREIGN KEY (`menuMainId`) REFERENCES `menu_main` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `FK_menuMain_localization_mutation` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `menu_main_localization`
--

LOCK TABLES `menu_main_localization` WRITE;
/*!40000 ALTER TABLE `menu_main_localization` DISABLE KEYS */;
INSERT INTO `menu_main_localization` VALUES (1,1,1,'FAQ','','',NULL,NULL,'',1,0,0,0,34,'2025-05-19 14:29:25','{}','{}',0,0),(2,1,2,'Pro koho','','',NULL,NULL,'',1,0,0,0,34,'2025-05-21 10:18:07','{}','{}',0,0),(3,1,3,'Služby','','',NULL,NULL,'',0,0,0,0,34,'2025-05-19 14:25:55','{}','{}',0,0),(4,1,4,'Kontakt','','',NULL,NULL,'',1,0,0,0,34,'2025-05-19 03:48:48','{}','{}',0,0),(5,1,5,'O nás','','',NULL,NULL,'',0,0,0,0,34,'2025-05-19 14:26:38','{}','{}',0,0),(6,1,6,'Kariéra','','',NULL,NULL,'',0,0,0,0,34,'2025-05-19 14:27:15','{}','{}',0,0),(7,1,7,'Reference','','',NULL,NULL,'',0,0,0,0,34,'2025-05-19 14:27:45','{}','{}',0,0),(8,1,8,'Blog','','',NULL,NULL,'',0,0,0,0,34,'2025-05-19 14:28:05','{}','{}',0,0),(9,1,9,'Akce','','',NULL,NULL,'',0,0,0,0,34,'2025-05-19 14:28:41','{}','{}',0,0);
/*!40000 ALTER TABLE `menu_main_localization` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `migrations`
--

DROP TABLE IF EXISTS `migrations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `migrations` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `group` varchar(100) NOT NULL,
  `file` varchar(100) NOT NULL,
  `checksum` char(32) NOT NULL,
  `executed` datetime NOT NULL,
  `ready` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `type_file` (`group`,`file`)
) ENGINE=InnoDB AUTO_INCREMENT=198 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `migrations`
--

LOCK TABLES `migrations` WRITE;
/*!40000 ALTER TABLE `migrations` DISABLE KEYS */;
INSERT INTO `migrations` VALUES (1,'core_structure','2022-04-13-111300-mutation.sql','7b67653da8bc3fc5e6ab09310943865f','2025-04-23 13:48:30',1),(2,'core_basic_data','2022-04-13-111300-mutation.sql','4c178b044468ec5a32375bc795b3e360','2025-04-23 13:48:30',1),(3,'core_structure','2022-04-13-111400-user.sql','6877957d0c47f038e7dddaf2a78dba20','2025-04-23 13:48:30',1),(4,'core_basic_data','2022-04-13-111400-user.sql','962ec54b83aca602ffe2d102350ab2be','2025-04-23 13:48:30',1),(5,'core_structure','2022-04-13-111500-user_mutation.sql','6512d7f0f234dd94600e8f63de36c05e','2025-04-23 13:48:30',1),(6,'core_basic_data','2022-04-13-111500-user_mutation.sql','aac45ea93475af1819ae2b40b4fb3a3a','2025-04-23 13:48:30',1),(7,'core_structure','2022-04-14-111329-library.sql','c5c4d69820a89cb6fc3e05a116edaed1','2025-04-23 13:48:30',1),(8,'core_basic_data','2022-04-14-111329-library.sql','18576d912ff2ced0474680e5f82b7773','2025-04-23 13:48:30',1),(9,'core_structure','2022-04-14-111330-alias.sql','0f9c0b4201dc8567e38a5175477906b9','2025-04-23 13:48:30',1),(10,'core_structure','2022-04-14-111330-alias_history.sql','562f0e6ad8b547882d27f38be5b80538','2025-04-23 13:48:30',1),(11,'core_structure','2022-04-14-111330-file.sql','0a791272621f3ee2f9102cc7bb24cd5e','2025-04-23 13:48:30',1),(12,'core_basic_data','2022-04-14-111330-file.sql','3e375549347d070ae5d1e006636f8eab','2025-04-23 13:48:30',1),(13,'core_structure','2022-04-14-111330-image.sql','23389e8b61ac929fb48d0afd59b9fc4a','2025-04-23 13:48:30',1),(14,'core_basic_data','2022-04-14-111330-image.sql','3de01f41ba0b04c484479bc416f27cf0','2025-04-23 13:48:30',1),(15,'core_structure','2022-04-14-111330-string.sql','0babb04369b94e5bf4cf71dbde7e0917','2025-04-23 13:48:30',1),(16,'core_basic_data','2022-04-14-111330-string.sql','0be1fccd1a70578edf5c36182e6b4eaf','2025-04-23 13:48:30',1),(17,'core_structure','2022-04-14-111331-elastic_search_index.sql','5fac76795bcb37303cdc3a886cdb7152','2025-04-23 13:48:30',1),(18,'core_structure','2022-04-14-111331-redirect.sql','64223d71831fbcdbeba686b7e2fb9513','2025-04-23 13:48:30',1),(19,'emailTemplate_structure','2022-04-14-121320-email_template.sql','74daf6480f26bff5557d2d7211606662','2025-04-23 13:48:30',1),(20,'emailTemplate_basic_data','2022-04-14-121320-email_template.sql','201e8bcc72c066ba1c6a58cf66d340d6','2025-04-23 13:48:30',1),(21,'emailTemplate_structure','2022-04-14-121320-email_template_file.sql','1fe68936d5cc5ee97010174f57ace499','2025-04-23 13:48:30',1),(22,'state_structure','2022-04-14-121320-state.sql','ae0457327cdb647cc8bfaaa0f9f012fa','2025-04-23 13:48:30',1),(23,'author_structure','2022-04-14-121329-author.sql','fad58859d80a70734f1ca6cba27c2d49','2025-04-23 13:48:30',1),(24,'state_structure','2022-04-14-121329-state_migration.sql','df6cf2998933a735c531592940fcb1b7','2025-04-23 13:48:30',1),(25,'author_dummy_data','2022-04-14-121330-author.sql','7fe99f1aca374ca8f2dca37d7c930100','2025-04-23 13:48:30',1),(26,'author_structure','2022-04-14-121330-author_localization.sql','12dfdcde615b1d46ba7fbd5ac66bed64','2025-04-23 13:48:30',1),(27,'author_dummy_data','2022-04-14-121330-author_localization.sql','7cdad74defaeaeeeb4456be91f8e6c94','2025-04-23 13:48:30',1),(28,'author_dummy_data','2022-04-14-121330-author_localization_alias_fix.sql','0b71ac82b2464baad0226ec1d4f78368','2025-04-23 13:48:30',1),(29,'blog_structure','2022-04-14-121330-blog.sql','589cf00974704cbc2ac1c887b2d681bc','2025-04-23 13:48:30',1),(30,'other_structure','2022-04-14-121330-newsletter_email.sql','170d07a0d84090d602af6753d350c4ff','2025-04-23 13:48:30',1),(31,'other_basic_data','2022-04-14-121330-newsletter_email.sql','b1bc7c202fa70a125745fcec1843d098','2025-04-23 13:48:30',1),(32,'page_structure','2022-04-14-121330-page.sql','6cde697d5171295617ef64d74fddbb75','2025-04-23 13:48:30',1),(33,'state_basic_data','2022-04-14-121330-state.sql','79ea384f5fd5a3a594b4e983fee05211','2025-04-23 13:48:30',1),(34,'user_structure','2022-04-14-121330-user_hash.sql','0520f8ce1677f78560151adebf491f60','2025-04-23 13:48:30',1),(35,'user_dummy_data','2022-04-14-121330-user_hash.sql','476a9ad440b051d103c0f5e45a8c3d32','2025-04-23 13:48:30',1),(36,'user_structure','2022-04-14-121330-user_image.sql','8fa6a41ee9a5fad50498db80cebaa8f8','2025-04-23 13:48:30',1),(37,'user_dummy_data','2022-04-14-121330-user_image.sql','36733a001d919038c3d434dc42394c56','2025-04-23 13:48:30',1),(38,'author_structure','2022-04-14-121331-blog_author.sql','6ffead7c62f3c605d965beb0bd9bbd80','2025-04-23 13:48:31',1),(39,'blog_structure','2022-04-14-121331-blog_tag.sql','d3f1c9fe0901d8360735cf343c8a3669','2025-04-23 13:48:31',1),(40,'blog_dummy_data','2022-04-14-121331-blog_tag.sql','4697170f5ac21fa2bf1b2ba6c41330f4','2025-04-23 13:48:31',1),(41,'blog_structure','2022-04-14-121331-blog_tree.sql','7d138e945a4b7839e41aa51cd7fd9fba','2025-04-23 13:48:31',1),(42,'blog_dummy_data','2022-04-14-121331-blog_tree.sql','e127553fb5651fb2b6896f1755e6a6a3','2025-04-23 13:48:31',1),(43,'page_basic_data','2022-04-14-121331-page.sql','2a91e9b27ab527aa909162d963dd5225','2025-04-23 13:48:31',1),(44,'page_structure','2022-04-14-121331-page_file.sql','d6d564073ec552c9787b2170a7dc3591','2025-04-23 13:48:31',1),(45,'page_structure','2022-04-14-121331-page_image.sql','5711a6997244bda926576eccb5e99f9e','2025-04-23 13:48:31',1),(46,'state_basic_data','2022-04-14-121331-state_migration.sql','347a00e12b76188009e67560f964ac88','2025-04-23 13:48:31',1),(47,'blog_structure','2022-04-14-121332-blog_blog.sql','87e5377b3263864df54ce24945e6b5b4','2025-04-23 13:48:31',1),(48,'blog_structure','2022-04-14-121332-blog_blog_tag.sql','99486d28afc7480a8d25e168fcc446ea','2025-04-23 13:48:31',1),(49,'blog_dummy_data','2022-04-14-121332-blog_blog_tag.sql','e133473eaf8ce2c4467430bdde902123','2025-04-23 13:48:31',1),(50,'page_basic_data','2022-04-14-121332-page_image.sql','f149564bab254d435d5d87706b92ff3d','2025-04-23 13:48:31',1),(51,'page_structure','2022-04-14-121332-page_parameter.sql','f5224a18cc2fcbc7a285c236ce442af8','2025-04-23 13:48:31',1),(52,'blog_basic_data','2022-04-14-121334-page.sql','0fab8137a8b9c593e8684cca0ec8dc82','2025-04-23 13:48:31',1),(53,'user_basic_data','2022-04-14-131330-page.sql','4354de150efa3ac6187a7ebdad72bcb7','2025-04-23 13:48:31',1),(54,'page_basic_data','2022-04-14-131331-page_file.sql','f13deba8fe7050605f703267baba9068','2025-04-23 13:48:31',1),(55,'page_structure','2022-04-14-131331-page_page.sql','f474763643017fb472f12181edfedede','2025-04-23 13:48:31',1),(56,'page_basic_data','2022-04-14-131331-page_page.sql','5a173950911c5272c7f8e6cdc99bb7df','2025-04-23 13:48:31',1),(57,'author_basic_data','2022-04-15-121329-page.sql','eaff490b0ca99cd9ae8f8d486d67239e','2025-04-23 13:48:31',1),(58,'blog_dummy_data','2022-04-15-121330-blog.sql','8bf5b98c20c24f3353ce4215b1660c43','2025-04-23 13:48:31',1),(59,'blog_dummy_data','2022-04-15-133330-pages.sql','1a1c312fda5d045cb4dfa4f6e45e8fe9','2025-04-23 13:48:31',1),(60,'blog_structure','2022-05-02-211330-blog_and_bloglocalization.sql','a136cd0878bdeb59ecffadf31c79390d','2025-04-23 13:48:31',1),(61,'blog_dummy_data','2022-05-02-211332-blog_and_bloglocalization.sql','1845a5e1f50a763b302f7203af83f24b','2025-04-23 13:48:31',1),(62,'core_structure','2022-08-09-134430-string.sql','93bc2f7e8eecd4cca45f62a7ed49c208','2025-04-23 13:48:31',1),(63,'page_structure','2022-08-12-1315-page_parameter.sql','fbb486d0e180e6130740f20d6d3c4084','2025-04-23 13:48:31',1),(64,'blog_structure','2022-08-12-144300-blog_tag_localization.sql','336ebfd4fcc18d8bce54af52752c95e6','2025-04-23 13:48:31',1),(65,'blog_dummy_data','2022-08-12-144500-blog_tag_bloglocalization.sql','0a72f5e5dce2082a75beeadca305cac4','2025-04-23 13:48:31',1),(66,'blog_dummy_data','2022-08-12-145000-blog_tag_bloglocalization.sql','a22fe91080c1db5303315f8775da0ee8','2025-04-23 13:48:31',1),(67,'blog_structure','2022-08-12-145000-blog_tag_localization.sql','f18db20739ab153b3d49b6f7f0969de8','2025-04-23 13:48:31',1),(68,'core_structure','2022-08-18-174430-fix_tree_cf.sql','e3d515c24409a68c2100de32e6df8845','2025-04-23 13:48:31',1),(69,'page_basic_data','2022-08-18-175000-page_fix_cf.sql','1dc11b37b854f9d0eb5aa4c616d6c454','2025-04-23 13:48:31',1),(70,'core_structure','2022-08-22-004400-es.sql','4b97c1b9d77eecab3e89e284570cf977','2025-04-23 13:48:31',1),(71,'core_structure','2022-08-22-134500-es_add_name.sql','91e287fbd44ff4ad7006c227a9d5c9bb','2025-04-23 13:48:31',1),(72,'blog_structure','2022-08-22-220000-blog_localization_tree.sql','3a435a5c7dd9bfc10abe88c057af1b83','2025-04-23 13:48:31',1),(73,'blog_structure','2022-08-25-115000-default_values.sql','211756492606cb5ac7b32cfe1cc59b43','2025-04-23 13:48:55',1),(74,'core_basic_data','2022-08-26-143134-template-migration.sql','f98388d2a04fbd684ff687fc3c66427c','2025-04-23 13:48:32',1),(75,'core_structure','2022-09-05-091945-mutation-cf.sql','59ee7be880ca46ba8af6c29a378152cd','2025-04-23 13:48:32',1),(76,'page_structure','2022-09-05-110819-page-mutation.sql','110df1af8a03b3b6194d8c53982ae604','2025-04-23 13:48:32',1),(77,'core_structure','2022-09-06-135421-en-mutation.sql','e5a93c80548de4a0b7d7c5df748b0324','2025-04-23 13:48:32',1),(78,'user_dummy_data','2022-09-07-130757-test-user.sql','28c61d4fe806e5c1b9de28f51bbe4ed6','2025-04-23 13:48:32',1),(79,'parameter_structure','2022-09-20-100000-parameter.sql','10596bbafcc365057c59cf49a266351f','2025-04-23 13:48:32',1),(80,'parameter_structure','2022-09-20-100001-parameter-value.sql','367d22a8bdeea79de6744eb78807c25f','2025-04-23 13:48:32',1),(81,'parameter_structure','2022-09-20-100002-parameter-value-image.sql','7031f6b671fbdd2296827b5f7592955c','2025-04-23 13:48:32',1),(82,'parameter_basic_data','2022-09-20-101001-parameter.sql','e822fe28a8cdb7620f69416e8ec6c2c6','2025-04-23 13:48:32',1),(83,'parameter_basic_data','2022-09-20-101002-parameter-value.sql','f0eb69ec3c65bf7e00e4e453793df8f7','2025-04-23 13:48:32',1),(84,'product_structure','2022-09-20-110000-price-level.sql','5cb7454edfd567d9978dc33e17fde73f','2025-04-23 13:48:32',1),(85,'product_structure','2022-09-20-110001-product.sql','af6e654c52a72e67f581b19298ccf033','2025-04-23 13:48:32',1),(86,'product_structure','2022-09-20-110002-product-localization.sql','348df57f1c254b30ed7ba0634137d2a4','2025-04-23 13:48:32',1),(87,'product_structure','2022-09-20-110003-product-variant.sql','b3ac531f8ac1c4721abdda702e778136','2025-04-23 13:48:32',1),(88,'product_structure','2022-09-20-110004-product-variant-localization.sql','720cc4c6f6efd03c94a5164a84b157af','2025-04-23 13:48:32',1),(89,'product_structure','2022-09-20-110005-page-product.sql','7924fb2de33e661a3066d46485692ef7','2025-04-23 13:48:32',1),(90,'product_structure','2022-09-20-110006-product-file.sql','bfeb840cc60ada461384bd616818cccb','2025-04-23 13:48:32',1),(91,'product_structure','2022-09-20-110007-product-image.sql','d81d8b69720f2a4dffe3f889c3fc4bbb','2025-04-23 13:48:32',1),(92,'product_structure','2022-09-20-110008-product-page.sql','eb63c8e297e2d10185fce0805f998a65','2025-04-23 13:48:32',1),(93,'product_structure','2022-09-20-110009-product-parameter.sql','d4e55a34105d84cdeb5a3f3a6a3506c4','2025-04-23 13:48:32',1),(94,'product_structure','2022-09-20-110010-product-product.sql','4044d316f57fb6022c2680abbf8559db','2025-04-23 13:48:32',1),(95,'product_structure','2022-09-20-110011-service.sql','1053ee0d4871e9ec19e7a811cbd9a11a','2025-04-23 13:48:32',1),(96,'product_structure','2022-09-20-110014-product-review.sql','9c06b6bf9f6d613e95efdf70caaf337d','2025-04-23 13:48:32',1),(97,'product_structure','2022-09-20-110015-product-price.sql','2792a87ab3426e3a8d3a761efea8d75a','2025-04-23 13:48:32',1),(98,'product_basic_data','2022-09-20-111001-price-level.sql','f1df23ad690ada9417660c47b706b720','2025-04-23 13:48:32',1),(99,'product_basic_data','2022-09-20-111002-product.sql','b8c212fcc4d43f36ab63201fe08d129d','2025-04-23 13:48:32',1),(100,'product_basic_data','2022-09-20-111003-product-localization.sql','50d47222b4d3aa8a01bcc990920141ba','2025-04-23 13:48:32',1),(101,'product_basic_data','2022-09-20-111004-product-variant.sql','1021814a39ff608160e4d97ef79f138f','2025-04-23 13:48:32',1),(102,'product_basic_data','2022-09-20-111005-product-variant-localization.sql','0128b5820e3e0dbd9b4bc0c89b1e16da','2025-04-23 13:48:32',1),(103,'product_basic_data','2022-09-20-111006-page-product.sql','7e30e15767af46b773f2a45d10585d87','2025-04-23 13:48:32',1),(104,'product_basic_data','2022-09-20-111007-product-file.sql','8237852c7369f93beb6c9be613050d7c','2025-04-23 13:48:32',1),(105,'product_basic_data','2022-09-20-111008-product-image.sql','5b8bc5656a7d6a62ff993ee2b75f323c','2025-04-23 13:48:32',1),(106,'product_basic_data','2022-09-20-111009-product-page.sql','d9a703d9ac302e1f02dfb7d1a2178aec','2025-04-23 13:48:32',1),(107,'product_basic_data','2022-09-20-111010-product-parameter.sql','51ddc0b1450a254b73b3e9ebc94c8a27','2025-04-23 13:48:32',1),(108,'product_basic_data','2022-09-20-111011-product-product.sql','264ed7199f2f317b63755074eb7f71ac','2025-04-23 13:48:32',1),(109,'product_basic_data','2022-09-20-111012-service.sql','b4a97c67c3241425330047e82d59304e','2025-04-23 13:48:32',1),(110,'product_basic_data','2022-09-20-111013-product-price.sql','277bebe852f662306f1c86ae3aea05df','2025-04-23 13:48:32',1),(111,'stock_structure','2022-09-20-120001-stock.sql','1e510007692115a79a1e946454a911bd','2025-04-23 13:48:32',1),(112,'stock_structure','2022-09-20-120002-stock-supplies.sql','87a40671f3a18fe62e5ce35df4f8ec9e','2025-04-23 13:48:32',1),(113,'stock_basic_data','2022-09-20-121001-stock.sql','58caee064b22a1d51f83cc154bd81838','2025-04-23 13:48:32',1),(114,'stock_basic_data','2022-09-20-121002-stock-supplies.sql','57a9a68aecd2e302685198e4544e22ea','2025-04-23 13:48:32',1),(115,'page_basic_data','2022-09-26-133733-catalog-pages.sql','7274b36edeed67c53d0604861ac80387','2025-04-23 13:48:32',1),(116,'product_structure','2022-09-26-141930-product-cc-remove-tabs.sql','a33f116036425cfcfcb29605726ece42','2025-04-23 13:48:32',1),(117,'page_structure','2022-09-26-153141-remove-cc-scheme.sql','c43fbdec5931b675f773b3b8d5bc7b91','2025-04-23 13:48:33',1),(118,'user_structure','2022-09-26-233002-price-level.sql','82ad89e1e728638cbccfd7916bc33106','2025-04-23 13:48:33',1),(119,'core_structure','2022-09-26-234114-fix-es-name-to-virtual.sql','19019f9aa9761b60fbdac585900002ee','2025-04-23 13:48:33',1),(120,'parameter_structure','2022-09-27-001925-fix-cf.sql','e900c4c902cf5d0123ab98060ece1d6f','2025-04-23 13:48:33',1),(121,'parameter_structure','2022-09-27-101343-cleaning.sql','928d0df4853ee176713d6cfa5b1ad7dd','2025-04-23 13:48:33',1),(122,'parameter_structure','2022-09-27-135501-add-value-cf.sql','e6334b3ac56c3824afeae123d1a81853','2025-04-23 13:48:33',1),(123,'product_structure','2022-09-27-153700-remove-services.sql','fb2be8728a218bb21d03a9912c3cc674','2025-04-23 13:48:33',1),(124,'core_structure','2022-09-27-153900-mutation-currency.sql','802ca38d46f70747b801eadf83af64bb','2025-04-23 13:48:33',1),(125,'state_structure','2022-09-27-153901-state-vat-rates.sql','709ec6ac71e3d671a7a52dda0be406af','2025-04-23 13:48:33',1),(126,'product_structure','2022-09-27-153902-price-as-money.sql','5af41d385c902a584d30f6bcff9d9675','2025-04-23 13:48:33',1),(127,'product_structure','2022-09-27-172617-product-vat-rates.sql','9a5e6ba5bb30798cf935750779b04d44','2025-04-23 13:48:33',1),(128,'page_structure','2022-10-03-140737-page-fix.sql','7ca37aa3be6638ce0423f00520da77ee','2025-04-23 13:48:33',1),(129,'core_basic_data','2022-10-10-101003-user_serction.sql','2d6b4b829ef976c4191ee8668a3546d5','2025-04-23 13:48:33',1),(130,'user_structure','2022-10-10-141454-uniq-email.sql','421934ef87c6955b3f10ddeff7d25e41','2025-04-23 13:48:33',1),(131,'core_structure','2022-10-11-125337-mutation-iso-code.sql','79cc64129847527f8e8db7ccf299128a','2025-04-23 13:48:33',1),(132,'core_structure','2022-10-11-130000-elastic-has-name.sql','85186d3e4c419d0ff708be0f736ae41b','2025-04-23 13:48:33',1),(133,'state_structure','2022-10-11-130412-utf8mb.sql','45d19ab08913013bbc5609bae7105b4d','2025-04-23 13:48:33',1),(134,'stock_structure','2022-10-11-130449-utf8mb.sql','860a5143127d925c22addfa0c587f5fb','2025-04-23 13:48:33',1),(135,'page_structure','2022-10-11-130704-utf8mb.sql','81665a8ff69e6486ccab389653c9c7db','2025-04-23 13:48:33',1),(136,'user_structure','2022-10-11-130926-utf8mb.sql','47b97c94d8777c3658735f8a80fca9e4','2025-04-23 13:48:33',1),(137,'author_structure','2022-10-11-134039-utf8mb.sql','467e5a01ea9c5a98ae5fd60e4dd57e91','2025-04-23 13:48:33',1),(138,'blog_structure','2022-10-11-134558-utf8mb.sql','74821bd6092ca1bb15e5bdae20652585','2025-04-23 13:48:33',1),(139,'core_structure','2022-10-11-134947-utf8mb.sql','1d900c0b3bc711d736902b36b284d7b4','2025-04-23 13:48:33',1),(140,'emailTemplate_structure','2022-10-11-135104-utf8mb.sql','df970c988bf66d3bf95dab5de5777a9d','2025-04-23 13:48:34',1),(141,'other_structure','2022-10-11-135535-utf8mb.sql','9aa0bf89f4b2812101c9c0f375297326','2025-04-23 13:48:34',1),(142,'parameter_structure','2022-10-11-135623-utf8mb.sql','92c3fe8b7da6705f6da57834f5d3c1aa','2025-04-23 13:48:34',1),(143,'product_structure','2022-10-11-135749-utf8mb.sql','b5eee6e2fbdc1bab4699d5eee9be7fff','2025-04-23 13:48:34',1),(144,'core_structure','2022-10-11-141148-setup-db.sql','64347bd6229d18ca66404e0760eae026','2025-04-23 13:48:34',1),(145,'seolink_structure','2022-10-11-144444-seolink.sql','ace588be77ea0a7f0f5ede2853a19577','2025-04-23 13:48:34',1),(146,'user_structure','2022-10-18-165656-google-login.sql','c420783adfe031966f4f6c5ea5657a38','2025-04-23 13:48:34',1),(147,'user_dummy_data','2022-10-31-114554-test-user-role.sql','a7450cfdec9b4d0ca98be757576c7437','2025-04-23 13:48:34',1),(148,'core_basic_data','2022-11-15-112317-remove-404.sql','657e6f422e9d1f28befac7b6b48931db','2025-04-23 13:48:34',1),(149,'product_basic_data','2022-11-16-132636-product-aliases.sql','c6274949588d8b759f4e79ce13ba65a3','2025-04-23 13:48:34',1),(150,'user_structure','2022-12-07-102609-remove-image.sql','5a1bec842cd7d6ad332d70d73a7f7529','2025-04-23 13:48:34',1),(151,'page_structure','2022-12-22-093531-remove-old-attrs.sql','8b0a2dde60efe1af704a18772971306e','2025-04-23 13:48:34',1),(152,'user_structure','2023-01-13-163113-api-token.sql','22929912a54870c2ed7767379a59a1d3','2025-04-23 13:48:34',1),(153,'page_structure','2023-01-25-135518-tree-parent.sql','eeecdb2ac73dda1e0cf1d48552de2139','2025-04-23 13:48:34',1),(154,'seolink_structure','2023-02-06-142110-fix-create.sql','ff6c995f325198954a6ff51d8f2d592f','2025-04-23 13:48:34',1),(155,'blog_structure','2023-02-09-154713-fix-varchars.sql','933ad0e57d589c162cc5be0c7735f786','2025-04-23 13:48:34',1),(156,'author_structure','2023-02-09-154828-fix-varchars.sql','b75ffe78dcc54b042717a964f4091c5c','2025-04-23 13:48:34',1),(157,'seolink_structure','2023-02-09-155057-fix-varchars.sql','60cd912552fc052b7bbb61c3d68766fe','2025-04-23 13:48:34',1),(158,'core_dummy_data','2023-03-01-115500-other_mutation.sql','659b496ddbcb1b2d63485d0907c62ea8','2025-04-23 13:48:34',1),(159,'user_basic_data','2023-03-01-135522-add-martina.sql','474dde299ce25a16e128da96ed405052','2025-04-23 13:48:34',1),(160,'other_structure','2023-03-02-114600-fix-newsletter-email.sql','e075aa0c0dcf95eb9cac0c3785c6e7df','2025-04-23 13:48:34',1),(161,'page_basic_data','2023-03-03-105725-pages-parent.sql','3d682d4c0665b264583355843653471a','2025-04-23 13:48:34',1),(162,'product_basic_data','2023-03-03-145217-fix-catalog.sql','8acca07d175fe93800adff61b3744d55','2025-04-23 13:48:34',1),(163,'product_structure','2023-03-09-152255-remove-old-parameter.sql','4c42302ceac26b7bb2198a7e64533d73','2025-04-23 13:48:34',1),(164,'core_basic_data','2023-03-13-084731-remove-testing-mutation.sql','e4627f227a92d084eaa494619944e8aa','2025-04-23 13:48:34',1),(165,'other_structure','2023-03-14-145241-fix-library.sql','c5fa785dd98f5b52a58306fe46c35f89','2025-04-23 13:48:34',1),(166,'blog_structure','2023-05-16-124751-is-top.sql','86c2a98b0e1a6f975d1efd9ec7e767d7','2025-04-23 13:48:35',1),(167,'core_structure','2023-07-11-135739-log.sql','130c7a64b285f3f6d459e7083d23b5b3','2025-04-23 13:48:35',1),(168,'core_structure','2023-08-10-141221-image-alts.sql','c8cc7c5f2184299d83c241f575097b0a','2025-04-23 13:48:35',1),(169,'core_structure','2023-08-21-150938-remove-tree-image.sql','7c9aac9d90eb0e04750ce20e6c817ed8','2025-04-23 13:48:35',1),(170,'product_structure','2023-08-22-150303-images.sql','0a28778abff058dc80aecd68ac19beee','2025-04-23 13:48:35',1),(171,'other_structure','2023-08-31-121654-states.sql','508b0370daecfed173a4637f8710b5d3','2025-04-23 13:48:35',1),(172,'other_structure','2023-09-01-101015-state-vats.sql','11df9b7bd43637082f288cc0391aac28','2025-04-23 13:48:35',1),(173,'order_structure','2024-02-26-091639-order.sql','7edf0ac528297fea23061a1a68295fb3','2025-04-23 13:48:35',1),(174,'core_basic_data','2024-02-28-161117-set-mutation-state.sql','a312b75fe5f83ed27bc7764c5f4f99d4','2025-04-23 13:48:35',1),(175,'order_structure','2024-02-29-132959-add-currency-to-order.sql','e6837f8f8e42fbf0941446b7262531e9','2025-04-23 13:48:35',1),(176,'page_basic_data','2024-03-01-135350-added-prebasket-and-cart-pages.sql','abf6a740011ef703256934aeba7ef791','2025-04-23 13:48:35',1),(177,'core_structure','2024-03-04-184310-added-table-holiday.sql','63d3d159801bc10f3193ce42a195315f','2025-04-23 13:48:35',1),(178,'product_structure','2024-03-05-103405-added-supply-column-deliverydelay.sql','8f981eacf7ebdbcf26046eba952e0a25','2025-04-23 13:48:35',1),(179,'order_structure','2024-03-06-141204-updated-voucher.sql','489fa1d616e5ffe9aadb1a11f2c76ed5','2025-04-23 13:48:35',1),(180,'user_basic_data','2024-03-07-111513-developer-kubomikita.sql','8f4ebcb1ab09423a016abe1499d7bba2','2025-04-23 13:48:35',1),(181,'core_basic_data','2024-03-07-145038-added-translations-to-cart-voucher.sql','8bddd33a1078aad0b327962bf199c686','2025-04-23 13:48:35',1),(182,'order_structure','2024-03-08-081809-added-properties-to-delivery-method.sql','c9a927840116026890b8be9faf5c659c','2025-04-23 13:48:35',1),(183,'order_structure','2024-03-11-121650-added-properties-to-payment-method.sql','6ddd3f173657b77420eac3ccce208c3c','2025-04-23 13:48:35',1),(184,'order_structure','2024-03-14-083938-drop-order-delivery-payment.sql','********************************','2025-04-23 13:48:35',1),(185,'order_structure','2024-03-14-110731-order-number-sequence.sql','efccd43abded906f28f705a0460b85a9','2025-04-23 13:48:36',1),(186,'core_basic_data','2024-03-20-090716-add-profile-pages.sql','bca8f16e2d4fe13d3e2bb238d33421f2','2025-04-23 13:48:36',1),(187,'order_structure','2024-03-22-134236-order-state-history.sql','f87ff76be5f821b5eeb9ac7a2051ac5b','2025-04-23 13:48:36',1),(188,'order_structure','2024-03-24-141910-card-payment.sql','0719f290c536b8f406befd0834e3ce55','2025-04-23 13:48:36',1),(189,'core_structure','2024-04-24-174059-form-failed-submission.sql','f626cb8b2aaeed24476ff71ba102723e','2025-04-23 13:48:36',1),(190,'core_structure','2025-04-04-111331-file.sql','10615b1df9378a0251b53efc3bc27c14','2025-04-23 13:48:36',1),(191,'menu_main_structure','2025-05-16-084620-init.sql','b03ce1d47583a35cfcccfe51f0d2d268','2025-05-16 14:31:41',1),(192,'menu_main_structure','2025-05-16-085012-init-localization.sql','620cb6def6fc264e31045d16f16698bf','2025-05-16 14:31:41',1),(193,'reference_structure','2025-05-19-084633-init.sql','4f2bf174b776b464ee70edeb14442de5','2025-05-21 17:47:59',1),(194,'testimonial_structure','2025-05-19-087644-init.sql','58b9c34b606350fe4f6bac7372c3b463','2025-05-21 17:47:59',1),(195,'blog_structure','2025-05-21-124752.sql','2b712362ab5f3f9d7bffbea77baf2865','2025-05-21 17:47:59',1),(196,'material_structure','2025-05-22-087642-init.sql','d6d074e27dc3a0d2764a26d15eb9c830','2025-05-22 11:12:03',1),(197,'blog_structure','2025-05-22-087654.sql','0fa17028f9d396ae4a1d285eebed9390','2025-05-22 14:03:54',1);
/*!40000 ALTER TABLE `migrations` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mutation`
--

DROP TABLE IF EXISTS `mutation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `mutation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `langCode` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `isoCodePrefix` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '',
  `public` int(11) DEFAULT '1',
  `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `langMenu` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `adminEmail` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `contactEmail` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `orderEmail` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `fromEmail` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `fromEmailName` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `currency` char(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `synonyms` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `heurekaOverenoKey` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  PRIMARY KEY (`id`),
  UNIQUE KEY `langCode` (`langCode`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mutation`
--

LOCK TABLES `mutation` WRITE;
/*!40000 ALTER TABLE `mutation` DISABLE KEYS */;
INSERT INTO `mutation` VALUES (1,'cs','cs',1,'Čeština',NULL,'<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','SuperAdmin','CZK','{\"xbox\":[\"produkt 4\",\"xboxíček\"]}','','{\"mutationData\":[{\"icon\":\"cz\"}],\"footerMenu\":[{\"footer_menu_1\":[{\"title\":\"Služby\",\"list\":[{\"toggle\":\"systemHref\",\"systemHref\":[{\"page\":465,\"hrefName\":\"Softwarové právo\"}]},{\"toggle\":\"systemHref\",\"systemHref\":[{\"page\":255,\"hrefName\":\"Ochrana osobních údajů\"}]},{\"toggle\":\"systemHref\",\"systemHref\":[{\"page\":466,\"hrefName\":\"Kybernetická bezpečnost\"}]},{\"toggle\":\"systemHref\",\"systemHref\":[{\"page\":467,\"hrefName\":\"Právo duševního vlastnictví\"}]},{\"toggle\":\"systemHref\",\"systemHref\":[{\"page\":468,\"hrefName\":\"Nemovitosti\"}]},{\"toggle\":\"systemHref\",\"systemHref\":[{\"page\":469,\"hrefName\":\"Korporátní právo\"}]},{\"toggle\":\"systemHref\",\"systemHref\":[{\"page\":470,\"hrefName\":\"Pracovní právo\"}]}]}],\"footer_menu_2\":[{\"title\":\"Často hledáte\",\"list\":[{\"toggle\":\"systemHref\",\"systemHref\":[{\"page\":8,\"hrefName\":\"FAQ\"}]},{\"toggle\":\"systemHref\",\"systemHref\":[{\"page\":102,\"hrefName\":\"Náš tým\"}]},{\"toggle\":\"systemHref\",\"systemHref\":[{\"page\":463,\"hrefName\":\"E-book\"}]},{\"toggle\":\"systemHref\",\"systemHref\":[{\"page\":446,\"hrefName\":\"Aktuality\"}]},{\"toggle\":\"systemHref\",\"systemHref\":[{\"page\":464,\"hrefName\":\"Podcast\"}]},{\"toggle\":\"customHref\",\"systemHref\":[{\"hrefName\":\"Newsletter\"}],\"customHref\":[{\"href\":\"https://sedlakovalegal.ecomailapp.cz/public/form/3-b6fcc542fb021c84fdaff536dd0a74a1\",\"hrefName\":\"Newsletter\"}]},{\"toggle\":\"customHref\",\"customHref\":[{\"href\":\"https://www.sedlakovalegal.cz/cdn/file/V6MotHb2BMBSmAsn169FgDFuMwUgshMU.pdf\",\"hrefName\":\"Smlouva o spolupráci\"}]}]}],\"footer_menu_copyright\":[{\"toggle\":\"systemHref\",\"systemHref\":[{\"page\":\"255\",\"hrefName\":\"Zásady ochrany osobních údajů\"}]},{\"toggle\":\"systemHref\",\"systemHref\":[{\"page\":\"482\"}]}]}]}'),(2,'en','en',1,'English',NULL,'<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','SuperAdmin','EUR',NULL,NULL,'{}');
/*!40000 ALTER TABLE `mutation` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mutation_state`
--

DROP TABLE IF EXISTS `mutation_state`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `mutation_state` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mutationId` int(11) NOT NULL,
  `stateId` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `mutationId_stateId` (`mutationId`,`stateId`),
  KEY `stateId` (`stateId`),
  CONSTRAINT `mutation_state_ibfk_3` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `mutation_state_ibfk_4` FOREIGN KEY (`stateId`) REFERENCES `state` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8 COLLATE=utf8_czech_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mutation_state`
--

LOCK TABLES `mutation_state` WRITE;
/*!40000 ALTER TABLE `mutation_state` DISABLE KEYS */;
INSERT INTO `mutation_state` VALUES (1,1,61),(2,2,2);
/*!40000 ALTER TABLE `mutation_state` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `newsletter_email`
--

DROP TABLE IF EXISTS `newsletter_email`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `newsletter_email` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `createdTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `mutationId` int(11) NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  UNIQUE KEY `email_mutationId` (`email`,`mutationId`),
  KEY `newsletter_email_mutation` (`mutationId`),
  CONSTRAINT `newsletter_email_mutation` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf32;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `newsletter_email`
--

LOCK TABLES `newsletter_email` WRITE;
/*!40000 ALTER TABLE `newsletter_email` DISABLE KEYS */;
INSERT INTO `newsletter_email` VALUES (1,'<EMAIL>','2017-12-05 08:00:04',1);
/*!40000 ALTER TABLE `newsletter_email` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `order`
--

DROP TABLE IF EXISTS `order`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `order` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `hash` varchar(128) DEFAULT NULL,
  `state` varchar(255) NOT NULL,
  `placedAt` datetime DEFAULT NULL,
  `orderNumber` varchar(255) DEFAULT NULL,
  `userId` int(11) DEFAULT NULL,
  `mutationId` int(11) NOT NULL,
  `priceLevelId` int(11) NOT NULL,
  `email` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `street` varchar(255) NOT NULL,
  `city` varchar(255) NOT NULL,
  `zip` varchar(255) NOT NULL,
  `phone` varchar(255) NOT NULL,
  `note` text NOT NULL,
  `countryId` int(11) NOT NULL,
  `companyName` varchar(255) DEFAULT NULL,
  `companyIdentifier` varchar(255) DEFAULT NULL,
  `vatNumber` varchar(255) DEFAULT NULL,
  `deliveryId` int(11) DEFAULT NULL,
  `paymentId` int(11) DEFAULT NULL,
  `currency` char(3) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `orderNumber` (`orderNumber`,`mutationId`),
  UNIQUE KEY `hash` (`hash`),
  KEY `userId` (`userId`),
  KEY `mutationId` (`mutationId`),
  KEY `priceLevelId` (`priceLevelId`),
  KEY `countryId` (`countryId`),
  KEY `deliveryId` (`deliveryId`),
  KEY `paymentId` (`paymentId`),
  CONSTRAINT `order_ibfk_1` FOREIGN KEY (`userId`) REFERENCES `user` (`id`),
  CONSTRAINT `order_ibfk_2` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`),
  CONSTRAINT `order_ibfk_3` FOREIGN KEY (`priceLevelId`) REFERENCES `price_level` (`id`),
  CONSTRAINT `order_ibfk_4` FOREIGN KEY (`countryId`) REFERENCES `state` (`id`),
  CONSTRAINT `order_ibfk_7` FOREIGN KEY (`deliveryId`) REFERENCES `order_delivery` (`id`),
  CONSTRAINT `order_ibfk_8` FOREIGN KEY (`paymentId`) REFERENCES `order_payment` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `order`
--

LOCK TABLES `order` WRITE;
/*!40000 ALTER TABLE `order` DISABLE KEYS */;
/*!40000 ALTER TABLE `order` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `order_delivery`
--

DROP TABLE IF EXISTS `order_delivery`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `order_delivery` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `deliveryMethodId` int(11) DEFAULT NULL,
  `informationId` int(11) DEFAULT NULL,
  `amount` int(11) NOT NULL DEFAULT '1',
  `vatRate` varchar(255) NOT NULL,
  `unitPrice_amount` decimal(18,4) NOT NULL,
  `unitPrice_currency` char(3) NOT NULL,
  `dateDeliveryFrom` date DEFAULT NULL,
  `dateDeliveryTo` date DEFAULT NULL,
  `dateExpedition` date DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `deliveryMethodId` (`deliveryMethodId`),
  KEY `informationId` (`informationId`),
  CONSTRAINT `order_delivery_ibfk_1` FOREIGN KEY (`deliveryMethodId`) REFERENCES `delivery_method` (`id`),
  CONSTRAINT `order_delivery_ibfk_3` FOREIGN KEY (`informationId`) REFERENCES `order_delivery_information` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `order_delivery`
--

LOCK TABLES `order_delivery` WRITE;
/*!40000 ALTER TABLE `order_delivery` DISABLE KEYS */;
/*!40000 ALTER TABLE `order_delivery` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `order_delivery_information`
--

DROP TABLE IF EXISTS `order_delivery_information`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `order_delivery_information` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` varchar(255) NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  `company` varchar(255) DEFAULT NULL,
  `street` varchar(255) DEFAULT NULL,
  `city` varchar(255) DEFAULT NULL,
  `zip` varchar(255) DEFAULT NULL,
  `countryId` int(11) DEFAULT NULL,
  `phoneNumber` varchar(255) DEFAULT NULL,
  `trackingCode` varchar(255) DEFAULT NULL,
  `pickupPointId` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `countryId` (`countryId`),
  CONSTRAINT `order_delivery_information_ibfk_1` FOREIGN KEY (`countryId`) REFERENCES `state` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `order_delivery_information`
--

LOCK TABLES `order_delivery_information` WRITE;
/*!40000 ALTER TABLE `order_delivery_information` DISABLE KEYS */;
/*!40000 ALTER TABLE `order_delivery_information` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `order_discount`
--

DROP TABLE IF EXISTS `order_discount`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `order_discount` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `orderId` int(11) NOT NULL,
  `amount` int(11) NOT NULL,
  `unitPrice_amount` decimal(18,4) NOT NULL,
  `unitPrice_currency` char(3) NOT NULL,
  `vatRate` varchar(255) NOT NULL,
  `discountTypeUniqueIdentifier` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `orderId` (`orderId`),
  CONSTRAINT `order_discount_ibfk_1` FOREIGN KEY (`orderId`) REFERENCES `order` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `order_discount`
--

LOCK TABLES `order_discount` WRITE;
/*!40000 ALTER TABLE `order_discount` DISABLE KEYS */;
/*!40000 ALTER TABLE `order_discount` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `order_number_sequence`
--

DROP TABLE IF EXISTS `order_number_sequence`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `order_number_sequence` (
  `mutationId` int(11) NOT NULL,
  `year` year(4) NOT NULL,
  `number` bigint(20) unsigned NOT NULL,
  PRIMARY KEY (`mutationId`,`year`),
  CONSTRAINT `order_number_sequence_ibfk_1` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `order_number_sequence`
--

LOCK TABLES `order_number_sequence` WRITE;
/*!40000 ALTER TABLE `order_number_sequence` DISABLE KEYS */;
/*!40000 ALTER TABLE `order_number_sequence` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `order_payment`
--

DROP TABLE IF EXISTS `order_payment`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `order_payment` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `paymentMethodId` int(11) DEFAULT NULL,
  `informationId` int(11) DEFAULT NULL,
  `amount` int(11) NOT NULL DEFAULT '1',
  `vatRate` varchar(255) NOT NULL,
  `unitPrice_amount` decimal(18,4) NOT NULL,
  `unitPrice_currency` char(3) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `paymentMethodId` (`paymentMethodId`),
  KEY `informationId` (`informationId`),
  CONSTRAINT `order_payment_ibfk_1` FOREIGN KEY (`paymentMethodId`) REFERENCES `payment_method` (`id`),
  CONSTRAINT `order_payment_ibfk_2` FOREIGN KEY (`informationId`) REFERENCES `order_payment_information` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `order_payment`
--

LOCK TABLES `order_payment` WRITE;
/*!40000 ALTER TABLE `order_payment` DISABLE KEYS */;
/*!40000 ALTER TABLE `order_payment` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `order_payment_information`
--

DROP TABLE IF EXISTS `order_payment_information`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `order_payment_information` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` varchar(255) NOT NULL,
  `state` varchar(255) NOT NULL,
  `variableSymbol` varchar(255) DEFAULT NULL,
  `dueDate` date DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `order_payment_information`
--

LOCK TABLES `order_payment_information` WRITE;
/*!40000 ALTER TABLE `order_payment_information` DISABLE KEYS */;
/*!40000 ALTER TABLE `order_payment_information` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `order_product`
--

DROP TABLE IF EXISTS `order_product`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `order_product` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `orderId` int(11) NOT NULL,
  `amount` int(11) NOT NULL,
  `unitPrice_amount` decimal(18,4) NOT NULL,
  `unitPrice_currency` char(3) NOT NULL,
  `vatRate` varchar(255) NOT NULL,
  `variantId` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `orderId` (`orderId`),
  KEY `variantId` (`variantId`),
  CONSTRAINT `order_product_ibfk_1` FOREIGN KEY (`orderId`) REFERENCES `order` (`id`),
  CONSTRAINT `order_product_ibfk_2` FOREIGN KEY (`variantId`) REFERENCES `product_variant` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `order_product`
--

LOCK TABLES `order_product` WRITE;
/*!40000 ALTER TABLE `order_product` DISABLE KEYS */;
/*!40000 ALTER TABLE `order_product` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `order_state_change`
--

DROP TABLE IF EXISTS `order_state_change`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `order_state_change` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `orderId` int(11) NOT NULL,
  `changedAt` datetime NOT NULL,
  `from` varchar(255) DEFAULT NULL,
  `to` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `orderId` (`orderId`),
  CONSTRAINT `order_state_change_ibfk_1` FOREIGN KEY (`orderId`) REFERENCES `order` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `order_state_change`
--

LOCK TABLES `order_state_change` WRITE;
/*!40000 ALTER TABLE `order_state_change` DISABLE KEYS */;
/*!40000 ALTER TABLE `order_state_change` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `order_voucher`
--

DROP TABLE IF EXISTS `order_voucher`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `order_voucher` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `orderId` int(11) NOT NULL,
  `amount` int(11) NOT NULL,
  `unitPrice_amount` decimal(18,4) NOT NULL,
  `unitPrice_currency` char(3) NOT NULL,
  `vatRate` varchar(255) NOT NULL,
  `voucherCodeId` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `orderId` (`orderId`),
  KEY `voucherCodeId` (`voucherCodeId`),
  CONSTRAINT `order_voucher_ibfk_1` FOREIGN KEY (`orderId`) REFERENCES `order` (`id`),
  CONSTRAINT `order_voucher_ibfk_3` FOREIGN KEY (`voucherCodeId`) REFERENCES `voucher_code` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `order_voucher`
--

LOCK TABLES `order_voucher` WRITE;
/*!40000 ALTER TABLE `order_voucher` DISABLE KEYS */;
/*!40000 ALTER TABLE `order_voucher` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `parameter`
--

DROP TABLE IF EXISTS `parameter`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `parameter` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `uid` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `type` enum('text','textarea','wysiwyg','number','bool','select','multiselect') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `sort` int(11) NOT NULL,
  `variantParameter` tinyint(4) NOT NULL,
  `isInFilter` int(11) NOT NULL DEFAULT '0',
  `customFieldsJson` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `isProtected` tinyint(4) DEFAULT '0',
  `extId` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uid` (`uid`),
  UNIQUE KEY `extId` (`extId`)
) ENGINE=InnoDB AUTO_INCREMENT=132 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `parameter`
--

LOCK TABLES `parameter` WRITE;
/*!40000 ALTER TABLE `parameter` DISABLE KEYS */;
INSERT INTO `parameter` VALUES (113,'Select','select','select',2,1,1,'{}',0,NULL),(114,'Multiselect','multiselect','multiselect',1,0,1,'{}',0,NULL),(115,'Tag','tag','multiselect',1,0,0,'{}',0,NULL),(116,'Bool','bool','bool',3,0,1,'{}',0,NULL),(117,'Text','text','text',4,0,0,'{}',0,NULL),(118,'Textarea','textarea','textarea',5,0,0,'{}',0,NULL),(119,'Číslo','cislo','number',6,0,1,'{}',0,NULL),(120,'Wysiwyg','wysiwyg','wysiwyg',7,0,0,'{}',0,NULL),(121,'Kategorie zbraně','kategorieZbrane','select',8,0,1,'{}',0,NULL),(127,'Ráže','raze','select',9,0,1,'{}',0,NULL),(128,'Délka hlavně','delkaHlavne','number',10,0,1,'{}',0,NULL),(129,'Enter a new name',NULL,'multiselect',1,0,0,'{}',0,NULL),(130,'Enter a new name',NULL,'multiselect',1,0,0,'{}',0,NULL),(131,'Enter a new name',NULL,'multiselect',1,0,0,'{}',0,NULL);
/*!40000 ALTER TABLE `parameter` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `parameter_value`
--

DROP TABLE IF EXISTS `parameter_value`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `parameter_value` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `parameterId` int(11) NOT NULL,
  `internalValue` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `internalAlias` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `sort` int(11) NOT NULL,
  `parameterSort` int(11) NOT NULL,
  `extId` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  PRIMARY KEY (`id`),
  UNIQUE KEY `extId` (`extId`),
  KEY `parameterId` (`parameterId`),
  CONSTRAINT `parameter_value_ibfk_1` FOREIGN KEY (`parameterId`) REFERENCES `parameter` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=6594 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `parameter_value`
--

LOCK TABLES `parameter_value` WRITE;
/*!40000 ALTER TABLE `parameter_value` DISABLE KEYS */;
INSERT INTO `parameter_value` VALUES (6564,113,'Hodnota 1','hodnota-1',1,1,NULL,NULL),(6565,113,'Hodnota 2','hodnota-2',2,1,NULL,NULL),(6566,113,'Hodnota 3','hodnota-3',3,1,NULL,NULL),(6567,114,'Hodnota 1','hodnota-1',1,1,NULL,NULL),(6568,115,'Tag 1','tag-1',1,1,NULL,NULL),(6569,115,'Tag 2','tag-2',2,1,NULL,NULL),(6570,114,'Hodnota 2','hodnota-2',2,1,NULL,NULL),(6571,114,'Hodnota 3','hodnota-3',3,1,NULL,NULL),(6572,119,'10','6572',1,1,NULL,NULL),(6573,119,'20','6573',2,1,NULL,NULL),(6574,119,'88','6574',3,1,NULL,NULL),(6575,119,'120','6575',4,1,NULL,NULL),(6576,119,'2','6576',5,1,NULL,NULL),(6577,117,'text','6577',0,0,NULL,NULL),(6578,118,'textarea','6578',0,0,NULL,NULL),(6579,120,'<p>Wysiwyg</p>','6579',0,0,NULL,NULL),(6580,116,'1','6580',0,0,NULL,NULL),(6581,118,'textarea.   gafsdfa','6581',0,0,NULL,NULL),(6582,121,'C','c',1,8,NULL,NULL),(6583,121,'D','d',2,8,NULL,NULL),(6585,119,'200','6585',0,0,NULL,NULL),(6586,127,'4,5 mm','4-5-mm',1,9,NULL,NULL),(6587,127,'5 mm','5-mm',2,9,NULL,NULL),(6588,127,'5,5 mm','5-5-mm',3,9,NULL,NULL),(6591,128,'455','6592',1,10,NULL,NULL),(6592,128,'483','6591',2,10,NULL,NULL),(6593,114,'h4','h4',4,1,NULL,NULL);
/*!40000 ALTER TABLE `parameter_value` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `payment_method`
--

DROP TABLE IF EXISTS `payment_method`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `payment_method` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `externalId` int(11) DEFAULT NULL,
  `paymentMethodUniqueIdentifier` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `desc` text NOT NULL,
  `tooltip` text,
  `sort` int(11) NOT NULL,
  `public` tinyint(1) NOT NULL,
  `isRecommended` tinyint(1) NOT NULL,
  `mutationId` int(11) NOT NULL,
  `vats` text NOT NULL,
  `customFieldsJson` longtext,
  PRIMARY KEY (`id`),
  KEY `mutationId` (`mutationId`),
  CONSTRAINT `payment_method_ibfk_1` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `payment_method`
--

LOCK TABLES `payment_method` WRITE;
/*!40000 ALTER TABLE `payment_method` DISABLE KEYS */;
/*!40000 ALTER TABLE `payment_method` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `payment_method_price`
--

DROP TABLE IF EXISTS `payment_method_price`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `payment_method_price` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `paymentMethodId` int(11) NOT NULL,
  `priceLevelId` int(11) NOT NULL,
  `stateId` int(11) NOT NULL,
  `price_amount` decimal(18,4) NOT NULL,
  `price_currency` char(3) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `paymentMethodId` (`paymentMethodId`),
  KEY `priceLevelId` (`priceLevelId`),
  KEY `stateId` (`stateId`),
  CONSTRAINT `payment_method_price_ibfk_1` FOREIGN KEY (`paymentMethodId`) REFERENCES `payment_method` (`id`),
  CONSTRAINT `payment_method_price_ibfk_2` FOREIGN KEY (`priceLevelId`) REFERENCES `price_level` (`id`),
  CONSTRAINT `payment_method_price_ibfk_3` FOREIGN KEY (`stateId`) REFERENCES `state` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `payment_method_price`
--

LOCK TABLES `payment_method_price` WRITE;
/*!40000 ALTER TABLE `payment_method_price` DISABLE KEYS */;
/*!40000 ALTER TABLE `payment_method_price` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `payment_method_x_state`
--

DROP TABLE IF EXISTS `payment_method_x_state`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `payment_method_x_state` (
  `paymentMethodId` int(11) NOT NULL,
  `stateId` int(11) NOT NULL,
  PRIMARY KEY (`paymentMethodId`,`stateId`),
  KEY `stateId` (`stateId`),
  CONSTRAINT `payment_method_x_state_ibfk_1` FOREIGN KEY (`paymentMethodId`) REFERENCES `payment_method` (`id`),
  CONSTRAINT `payment_method_x_state_ibfk_2` FOREIGN KEY (`stateId`) REFERENCES `state` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `payment_method_x_state`
--

LOCK TABLES `payment_method_x_state` WRITE;
/*!40000 ALTER TABLE `payment_method_x_state` DISABLE KEYS */;
/*!40000 ALTER TABLE `payment_method_x_state` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `price_level`
--

DROP TABLE IF EXISTS `price_level`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `price_level` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `price_level`
--

LOCK TABLES `price_level` WRITE;
/*!40000 ALTER TABLE `price_level` DISABLE KEYS */;
INSERT INTO `price_level` VALUES (1,'default','Základní (MO)'),(2,'wholesale','Velkoobchodní (VO)');
/*!40000 ALTER TABLE `price_level` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `product`
--

DROP TABLE IF EXISTS `product`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `product` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `template` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT 'Product:detail',
  `publicFrom` datetime DEFAULT NULL,
  `publicTo` datetime DEFAULT NULL,
  `internalName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `hideFirstImage` tinyint(1) NOT NULL,
  `availability` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `isSet` tinyint(4) DEFAULT NULL,
  `isOld` tinyint(4) DEFAULT NULL,
  `isInPrepare` tinyint(4) DEFAULT NULL,
  `isNew` tinyint(4) NOT NULL,
  `notSoldSeparately` tinyint(4) DEFAULT NULL,
  `discount` float(10,2) DEFAULT NULL,
  `discountType` tinyint(1) DEFAULT NULL,
  `reviewAverage` float(10,2) NOT NULL DEFAULT '0.00',
  `isFreeTransport` tinyint(1) DEFAULT '0',
  `soldCount` int(11) DEFAULT '0',
  `customFieldsJson` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `editedTime` datetime DEFAULT NULL,
  `edited` int(11) DEFAULT NULL,
  `vats` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `extId` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `extId` (`extId`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `product`
--

LOCK TABLES `product` WRITE;
/*!40000 ALTER TABLE `product` DISABLE KEYS */;
INSERT INTO `product` VALUES (2,NULL,':Front:Product:detail','2018-04-11 15:28:38','2120-04-11 15:28:38','produkt 2',0,'',NULL,0,0,0,0,NULL,NULL,0.00,0,0,'{\"feeds\":{\"zbozi\":\"\",\"heureka\":\"\",\"google\":\"\"},\"article\":{\"tree\":\"\"}}','2021-06-28 08:37:05',3,'{\"1\":\"standard\",\"2\":\"standard\",\"3\":\"standard\",\"4\":\"standard\",\"5\":\"standard\",\"6\":\"standard\",\"7\":\"standard\",\"8\":\"standard\"}',NULL),(4,NULL,':Front:Product:detail','2018-04-11 15:28:38','2120-04-11 15:28:38','Produkt 4',0,'',NULL,0,0,0,0,NULL,NULL,0.00,0,0,'{\"feeds\":{\"zbozi\":\"\",\"heureka\":\"\",\"google\":\"\"}}','2021-07-07 17:05:23',1,'{\"1\":\"standard\",\"2\":\"standard\",\"3\":\"standard\",\"4\":\"standard\",\"5\":\"standard\",\"6\":\"standard\",\"7\":\"standard\",\"8\":\"standard\"}',NULL),(5,NULL,':Front:Product:detail','2018-04-11 15:28:38','2120-04-11 15:28:38','produkt 5',0,'',NULL,0,0,0,0,NULL,NULL,0.00,0,0,'{\"feeds\":{\"zbozi\":\"\",\"heureka\":\"\",\"google\":\"\"}}','2021-06-28 08:34:53',3,'{\"1\":\"standard\",\"2\":\"standard\",\"3\":\"standard\",\"4\":\"standard\",\"5\":\"standard\",\"6\":\"standard\",\"7\":\"standard\",\"8\":\"standard\"}',NULL),(6,NULL,':Front:Product:detail','2018-04-11 15:28:38','2120-04-11 15:28:38','produkt 6',0,'',NULL,0,0,0,0,NULL,NULL,0.00,0,0,'[]','2021-06-28 08:33:48',3,'{\"1\":\"standard\",\"2\":\"standard\",\"3\":\"standard\",\"4\":\"standard\",\"5\":\"standard\",\"6\":\"standard\",\"7\":\"standard\",\"8\":\"standard\"}',NULL),(7,NULL,':Front:Product:detail','2018-04-11 15:28:38','2120-04-11 15:28:38','produkt 7',0,'',NULL,0,0,0,0,NULL,NULL,0.00,0,0,'[]','2021-06-28 08:53:36',3,'{\"1\":\"standard\",\"2\":\"standard\",\"3\":\"standard\",\"4\":\"standard\",\"5\":\"standard\",\"6\":\"standard\",\"7\":\"standard\",\"8\":\"standard\"}',NULL),(8,NULL,':Front:Product:detail','2018-04-11 15:28:38','2120-04-11 15:28:38','produkt 8',0,'',NULL,0,0,0,0,NULL,NULL,0.00,0,0,'[]','2021-06-29 16:17:25',14,'{\"1\":\"standard\",\"2\":\"standard\",\"3\":\"standard\",\"4\":\"standard\",\"5\":\"standard\",\"6\":\"standard\",\"7\":\"standard\",\"8\":\"standard\"}',NULL),(9,NULL,':Front:Product:detail','2018-04-11 15:28:00','2120-04-11 15:28:00','produkt 9',0,'',NULL,0,0,0,0,NULL,NULL,0.00,0,0,'','2021-11-30 16:33:58',3,'{\"1\":\"standard\",\"2\":\"standard\",\"3\":\"standard\",\"4\":\"standard\",\"5\":\"standard\",\"6\":\"standard\",\"7\":\"standard\",\"8\":\"standard\"}',NULL),(10,NULL,':Front:Product:detail','2018-04-11 15:28:00','2120-04-11 15:28:00','VZDUCHOVKA HAMMERLI HUNTER FORCE 900 COMBO 4,5MM1',0,'',NULL,0,0,1,0,NULL,NULL,0.00,0,0,'{}','2022-01-13 13:37:38',4,'{\"1\":\"standard\",\"2\":\"standard\",\"3\":\"standard\",\"4\":\"standard\",\"5\":\"standard\",\"6\":\"standard\",\"7\":\"standard\",\"8\":\"standard\"}',NULL),(11,NULL,':Front:Product:detail','2021-07-04 18:16:00','2121-07-04 18:16:00','Vzduchovka Kral Arms N-08 Camo 5,5mm',0,'',NULL,0,0,0,0,NULL,NULL,0.00,0,0,'{}','2022-04-04 10:42:30',13,'{\"1\":\"standard\",\"2\":\"standard\",\"3\":\"standard\",\"4\":\"standard\",\"5\":\"standard\",\"6\":\"standard\",\"7\":\"standard\",\"8\":\"standard\"}',NULL),(13,NULL,':Front:Product:detail','2021-12-13 11:40:00','2121-07-13 11:38:00','',0,'',NULL,0,0,0,0,NULL,NULL,0.00,0,0,'{}','2021-09-03 09:59:25',5,'{\"1\":\"standard\",\"2\":\"standard\",\"3\":\"standard\",\"4\":\"standard\",\"5\":\"standard\",\"6\":\"standard\",\"7\":\"standard\",\"8\":\"standard\"}',NULL),(14,NULL,':Front:Product:detail','2021-08-23 14:44:00','2121-08-23 14:44:00','Název',0,'',NULL,0,0,0,0,NULL,NULL,0.00,0,0,'{}','2022-02-08 14:09:22',4,'{\"1\":\"standard\",\"2\":\"standard\",\"3\":\"standard\",\"4\":\"standard\",\"5\":\"standard\",\"6\":\"standard\",\"7\":\"standard\",\"8\":\"standard\"}',NULL);
/*!40000 ALTER TABLE `product` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `product_file`
--

DROP TABLE IF EXISTS `product_file`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `product_file` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `productLocalizationId` int(11) NOT NULL,
  `fileId` int(11) NOT NULL,
  `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `url` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `sort` int(11) DEFAULT NULL,
  `size` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `productId_fileId` (`productLocalizationId`,`fileId`),
  KEY `fileId` (`fileId`),
  CONSTRAINT `product_file_ibfk_4` FOREIGN KEY (`fileId`) REFERENCES `file` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `product_file_ibfk_5` FOREIGN KEY (`productLocalizationId`) REFERENCES `product_localization` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `product_file`
--

LOCK TABLES `product_file` WRITE;
/*!40000 ALTER TABLE `product_file` DISABLE KEYS */;
INSERT INTO `product_file` VALUES (1,6,9,'hezké jméno','/data/files/9-image-10.png',0,9141),(2,6,11,'Eu0gZ5_XMAABywS.jfif','/data/files/11-eu0gz5-xmaabyws.jpeg',1,63479),(3,5,9,'hezké jméno','/data/files/9-image-10.png',0,9141),(4,5,11,'Eu0gZ5_XMAABywS.jfif','/data/files/11-eu0gz5-xmaabyws.jpeg',1,63479),(5,6,12,'asdsad','/data/files/12-image-11.png',2,20042),(6,5,12,'en name','/data/files/12-image-11.png',2,20042),(7,1,15,'Test - uprava nazvu souboru','/data/files/15-ats-pumpa-navod-1-2-3-eh-te.pdf',0,4104733),(8,16,38,'Ceník','/data/files/38-kamenivo-cenik-2021.pdf',0,4785772),(20,20,67,'2.4936_Hammerli Hunter Force 900 Combo-bullet.pdf','/data/files/67-2.4936-hammerli-hunter-force-900-combo-bullet.pdf',0,382049);
/*!40000 ALTER TABLE `product_file` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `product_image`
--

DROP TABLE IF EXISTS `product_image`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `product_image` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `productId` int(11) NOT NULL,
  `libraryImageId` int(11) NOT NULL COMMENT 'idfile',
  `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `sort` tinyint(4) DEFAULT NULL,
  `variants` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `extId` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `productId_imageId` (`productId`,`libraryImageId`) USING BTREE,
  UNIQUE KEY `productId_extId` (`productId`,`extId`),
  KEY `imageId` (`libraryImageId`) USING BTREE,
  CONSTRAINT `product_image_ibfk_3` FOREIGN KEY (`productId`) REFERENCES `product` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `product_image_ibfk_4` FOREIGN KEY (`libraryImageId`) REFERENCES `image` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=33 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `product_image`
--

LOCK TABLES `product_image` WRITE;
/*!40000 ALTER TABLE `product_image` DISABLE KEYS */;
INSERT INTO `product_image` VALUES (1,6,13,'name',0,NULL,'{\"cs\":{\"name\":\"\"},\"en\":{\"name\":\"\"}}',NULL),(2,6,14,'name',1,NULL,'{\"cs\":{\"name\":\"\"},\"en\":{\"name\":\"\"}}',NULL),(3,8,22,NULL,0,NULL,'{\"en\":{\"name\":\"\"},\"cs\":{\"name\":\"\"}}',NULL),(4,4,22,NULL,1,NULL,'{\"en\":{\"name\":\"\"},\"cs\":{\"name\":\"\"}}',NULL),(5,4,23,NULL,2,NULL,'{\"en\":{\"name\":\"\"},\"cs\":{\"name\":\"\"}}',NULL),(6,4,28,NULL,0,NULL,'{\"en\":{\"name\":\"\"},\"cs\":{\"name\":\"\"}}',NULL),(7,10,33,NULL,0,'','{\"en\":{\"name\":\"\"},\"cs\":{\"name\":\"\"}}',NULL),(8,10,34,NULL,1,'','{\"en\":{\"name\":\"\"},\"cs\":{\"name\":\"\"}}',NULL),(9,10,36,NULL,2,'','{\"en\":{\"name\":\"\"},\"cs\":{\"name\":\"\"}}',NULL),(10,10,32,NULL,3,'','{\"en\":{\"name\":\"\"},\"cs\":{\"name\":\"\"}}',NULL),(11,10,39,NULL,4,'','{\"en\":{\"name\":\"\"},\"cs\":{\"name\":\"\"}}',NULL),(12,10,38,NULL,5,'','{\"en\":{\"name\":\"\"},\"cs\":{\"name\":\"\"}}',NULL),(13,10,35,NULL,6,'','{\"en\":{\"name\":\"\"},\"cs\":{\"name\":\"\"}}',NULL),(14,10,37,NULL,7,'','{\"en\":{\"name\":\"\"},\"cs\":{\"name\":\"\"}}',NULL),(15,11,40,NULL,0,'','{\"en\":{\"name\":\"\"},\"cs\":{\"name\":\"\"}}',NULL),(16,11,43,NULL,1,'','{\"en\":{\"name\":\"\"},\"cs\":{\"name\":\"\"}}',NULL),(17,11,47,NULL,2,'','{\"en\":{\"name\":\"\"},\"cs\":{\"name\":\"\"}}',NULL),(18,11,45,NULL,3,'','{\"en\":{\"name\":\"\"},\"cs\":{\"name\":\"\"}}',NULL),(19,11,44,NULL,4,'','{\"en\":{\"name\":\"\"},\"cs\":{\"name\":\"\"}}',NULL),(20,11,46,NULL,5,'','{\"en\":{\"name\":\"\"},\"cs\":{\"name\":\"\"}}',NULL),(21,11,41,NULL,6,'','{\"en\":{\"name\":\"\"},\"cs\":{\"name\":\"\"}}',NULL),(22,11,42,NULL,7,'','{\"en\":{\"name\":\"\"},\"cs\":{\"name\":\"\"}}',NULL),(24,13,22,NULL,0,NULL,'{\"en\":{\"name\":\"1-1_a_logo\"},\"cs\":{\"name\":\"1-1_a_log\"}}',NULL),(25,13,57,NULL,1,NULL,'{\"en\":{\"name\":\"berlin-4679964_1920.jpg\"},\"cs\":{\"name\":\"berlin-4679964_1920.jpg\"}}',NULL),(29,14,28,NULL,0,'','{\"en\":{\"name\":\"cameron-venti-pqyvyqqa87s-unsplash\"},\"cs\":{\"name\":\"cameron-venti-pqyvyqqa87s-unsplash\"}}',NULL),(30,14,30,NULL,1,'','{\"en\":{\"name\":\"caseen-kyle-registos-1ht1wnmfDiA-unsplash\"},\"cs\":{\"name\":\"caseen-kyle-registos-1ht1wnmfDiA-unsplash\"}}',NULL),(31,14,63,NULL,2,'','{\"en\":{\"name\":\"feature-1\"},\"cs\":{\"name\":\"feature-1\"}}',NULL),(32,14,22,NULL,3,'','{\"en\":{\"name\":\"1-1_a_logo\"},\"cs\":{\"name\":\"1-1_a_logo\"}}',NULL);
/*!40000 ALTER TABLE `product_image` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `product_localization`
--

DROP TABLE IF EXISTS `product_localization`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `product_localization` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `productId` int(11) NOT NULL,
  `mutationId` int(11) NOT NULL,
  `public` int(11) DEFAULT '0',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `nameTitle` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `nameAnchor` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `keywords` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `annotation` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `setup` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `customContentJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  PRIMARY KEY (`id`),
  UNIQUE KEY `mutationId_productId` (`mutationId`,`productId`),
  KEY `productId` (`productId`),
  CONSTRAINT `product_localization_ibfk_1` FOREIGN KEY (`productId`) REFERENCES `product` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `product_localization_ibfk_2` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=29 DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `product_localization`
--

LOCK TABLES `product_localization` WRITE;
/*!40000 ALTER TABLE `product_localization` DISABLE KEYS */;
INSERT INTO `product_localization` VALUES (1,6,1,1,'produkt 6','produkt 6','produkt 6','',NULL,'Bla bla bla','','{\"inheritCategories\":false,\"inheritVideos\":false,\"inheritFiles\":false,\"inheritPages\":false,\"inheritLinks\":false}','{\"image_multiple\":[22,23],\"file\":{\"id\":\"32\",\"name\":\"payment-detail-T568208798 (13).pdf\"},\"image\":\"22\"}','{}'),(2,6,2,1,'Alias test','Alias test','Alias test','',NULL,'','','{\"inheritCategories\":false,\"inheritVideos\":false,\"inheritFiles\":false,\"inheritPages\":false,\"inheritLinks\":false}','{}','{}'),(5,7,2,1,'test product','test product','test product','',NULL,'annotation','<p>content</p>','{\"inheritCategories\":false,\"inheritVideos\":false,\"inheritFiles\":false,\"inheritPages\":true,\"inheritLinks\":false}','{}','{}'),(6,7,1,1,'produkt 7','produkt 7','produkt 7','',NULL,'anotace','<p>obsah</p>','{\"inheritCategories\":false,\"inheritVideos\":false,\"inheritFiles\":false,\"inheritPages\":false,\"inheritLinks\":false}','{}','{}'),(7,8,2,0,'','','','',NULL,'','','{\"inheritCategories\":false,\"inheritVideos\":false,\"inheritFiles\":false,\"inheritPages\":false,\"inheritLinks\":false}','{\"parameterForFilter\":null,\"image_multiple\":\"\",\"file\":null,\"image\":\"\",\"file_multiple\":null}','{}'),(8,8,1,0,'produkt 8','produkt 8','produkt 8','',NULL,'ttt','','{\"inheritCategories\":false,\"inheritVideos\":false,\"inheritFiles\":false,\"inheritPages\":false,\"inheritLinks\":false}','{\"parameterForFilter\":null,\"image_multiple\":\"\",\"file\":null,\"image\":\"\",\"file_multiple\":null}','{}'),(9,9,2,1,'Test 11 EN','Test 11 EN','product 9  EN','',NULL,'','','{\"inheritCategories\":false,\"inheritVideos\":false,\"inheritFiles\":false,\"inheritPages\":false,\"inheritLinks\":false}','{\"parameterForFilter\":[{\"visibleParameters\":[{\"visibleCount\":\"1\"}]}],\"name\":\"John Doe\",\"checkbox\":true,\"select\":\"first\",\"select_multiple\":\"first,second\"}','{}'),(10,9,1,1,'produkt 9','product','product 9','',NULL,'','','{\"inheritCategories\":false,\"inheritVideos\":false,\"inheritFiles\":false,\"inheritPages\":false,\"inheritLinks\":false}','{\"parameterForFilter\":[{\"visibleParameters\":[{\"indexable\":true,\"numberAsRange\":true}]}],\"name\":\"John Doe\",\"image_multiple\":[22,23,24]}','{}'),(13,5,2,1,'','','','',NULL,'','','{\"inheritCategories\":false,\"inheritVideos\":false,\"inheritFiles\":false,\"inheritPages\":false,\"inheritLinks\":false}','{}','{}'),(14,5,1,1,'produkt 5','produkt 5','produkt 5','',NULL,'','','{\"inheritCategories\":false,\"inheritVideos\":false,\"inheritFiles\":false,\"inheritPages\":false,\"inheritLinks\":false}','{}','{}'),(15,4,2,1,'','','','',NULL,'','','{\"inheritCategories\":true,\"inheritVideos\":false,\"inheritFiles\":false,\"inheritPages\":false,\"inheritLinks\":false}','{\"name\":\"John Doe\"}','{}'),(16,4,1,1,'Produkt 4','Produkt 4 title','Produkt 4 link','Meta',NULL,'Anotace','<p>Obsah</p>','{\"inheritCategories\":false,\"inheritVideos\":false,\"inheritFiles\":false,\"inheritPages\":false,\"inheritLinks\":false}','{\"name\":\"John Doe\",\"image_multiple\":[\"23\"]}','{}'),(17,2,2,1,'','','','',NULL,'','','{\"inheritCategories\":false,\"inheritVideos\":false,\"inheritFiles\":false,\"inheritPages\":false,\"inheritLinks\":false}','{}','{}'),(18,2,1,1,'produkt 2','produkt 2','produkt 2','',NULL,'','','{\"inheritCategories\":false,\"inheritVideos\":false,\"inheritFiles\":false,\"inheritPages\":false,\"inheritLinks\":false}','{}','{}'),(19,10,2,0,'','','','',NULL,'','','{\"inheritCategories\":false,\"inheritVideos\":false,\"inheritFiles\":false,\"inheritPages\":false,\"inheritLinks\":false}','{\"name\":\"John Doe\"}','{}'),(20,10,1,1,'VZDUCHOVKA HAMMERLI HUNTER FORCE 900 COMBO 4,5MM1','VZDUCHOVKA HAMMERLI HUNTER FORCE 900 COMBO 4,5MM1','VZDUCHOVKA HAMMERLI HUNTER FORCE 900 COMBO 4,5MM1','',NULL,'Nejslavnější model pružinovky se spodním nátahem od německého výrobce Umarex. Mohutná pažba s ozdobnou rytinou často připomíná reálnou pušku. Hledí je vybaveno TRU-GLO systémem, což jsou světlovodné vlákna, které pomáhají se zamířením při zhoršených podmínkách.a','<p><span style=\"caret-color: #3e3e3e; color: #3e3e3e; font-family: \'Open Sans\'; font-size: 16px; font-style: normal; font-variant-caps: normal; font-weight: normal; letter-spacing: normal; orphans: auto; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; widows: auto; word-spacing: 0px; -webkit-text-size-adjust: auto; -webkit-text-stroke-width: 0px; background-color: #ffffff; text-decoration: none; display: inline !important; float: none;\">Nejslavnější model pružinovky se spodním nátahem od německého výrobce </span><strong style=\"box-sizing: border-box; margin: 0px; padding: 0px; border: 0px; font-family: \'Open Sans\'; font-size: 16px; font-style: normal; font-variant-caps: normal; font-weight: bold; font-stretch: inherit; line-height: inherit; vertical-align: baseline; caret-color: #3e3e3e; color: #3e3e3e; letter-spacing: normal; orphans: auto; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; widows: auto; word-spacing: 0px; -webkit-text-size-adjust: auto; -webkit-text-stroke-width: 0px; text-decoration: none;\">Umarex</strong><span style=\"caret-color: #3e3e3e; color: #3e3e3e; font-family: \'Open Sans\'; font-size: 16px; font-style: normal; font-variant-caps: normal; font-weight: normal; letter-spacing: normal; orphans: auto; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; widows: auto; word-spacing: 0px; -webkit-text-size-adjust: auto; -webkit-text-stroke-width: 0px; background-color: #ffffff; text-decoration: none; display: inline !important; float: none;\">. Mohutná pažba s ozdobnou rytinou často připomíná reálnou pušku. Hledí je vybaveno </span><strong style=\"box-sizing: border-box; margin: 0px; padding: 0px; border: 0px; font-family: \'Open Sans\'; font-size: 16px; font-style: normal; font-variant-caps: normal; font-weight: bold; font-stretch: inherit; line-height: inherit; vertical-align: baseline; caret-color: #3e3e3e; color: #3e3e3e; letter-spacing: normal; orphans: auto; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; widows: auto; word-spacing: 0px; -webkit-text-size-adjust: auto; -webkit-text-stroke-width: 0px; text-decoration: none;\">TRU-GLO</strong><span style=\"caret-color: #3e3e3e; color: #3e3e3e; font-family: \'Open Sans\'; font-size: 16px; font-style: normal; font-variant-caps: normal; font-weight: normal; letter-spacing: normal; orphans: auto; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; widows: auto; word-spacing: 0px; -webkit-text-size-adjust: auto; -webkit-text-stroke-width: 0px; background-color: #ffffff; text-decoration: none; display: inline !important; float: none;\"> systémem, což jsou světlovodné vlákna, které </span><strong style=\"box-sizing: border-box; margin: 0px; padding: 0px; border: 0px; font-family: \'Open Sans\'; font-size: 16px; font-style: normal; font-variant-caps: normal; font-weight: bold; font-stretch: inherit; line-height: inherit; vertical-align: baseline; caret-color: #3e3e3e; color: #3e3e3e; letter-spacing: normal; orphans: auto; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; widows: auto; word-spacing: 0px; -webkit-text-size-adjust: auto; -webkit-text-stroke-width: 0px; text-decoration: none;\">pomáhají se zamířením</strong><span style=\"caret-color: #3e3e3e; color: #3e3e3e; font-family: \'Open Sans\'; font-size: 16px; font-style: normal; font-variant-caps: normal; font-weight: normal; letter-spacing: normal; orphans: auto; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; widows: auto; word-spacing: 0px; -webkit-text-size-adjust: auto; -webkit-text-stroke-width: 0px; background-color: #ffffff; text-decoration: none; display: inline !important; float: none;\"> při zhoršených podmínkách.</span><br style=\"box-sizing: border-box; caret-color: #3e3e3e; color: #3e3e3e; font-family: \'Open Sans\'; font-size: 16px; font-style: normal; font-variant-caps: normal; font-weight: normal; letter-spacing: normal; orphans: auto; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; widows: auto; word-spacing: 0px; -webkit-text-size-adjust: auto; -webkit-text-stroke-width: 0px; text-decoration: none;\" /><br style=\"box-sizing: border-box; caret-color: #3e3e3e; color: #3e3e3e; font-family: \'Open Sans\'; font-size: 16px; font-style: normal; font-variant-caps: normal; font-weight: normal; letter-spacing: normal; orphans: auto; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; widows: auto; word-spacing: 0px; -webkit-text-size-adjust: auto; -webkit-text-stroke-width: 0px; text-decoration: none;\" /><span style=\"caret-color: #3e3e3e; color: #3e3e3e; font-family: \'Open Sans\'; font-size: 16px; font-style: normal; font-variant-caps: normal; font-weight: normal; letter-spacing: normal; orphans: auto; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; widows: auto; word-spacing: 0px; -webkit-text-size-adjust: auto; -webkit-text-stroke-width: 0px; background-color: #ffffff; text-decoration: none; display: inline !important; float: none;\">Vzduchovka je vybavena a </span><strong style=\"box-sizing: border-box; margin: 0px; padding: 0px; border: 0px; font-family: \'Open Sans\'; font-size: 16px; font-style: normal; font-variant-caps: normal; font-weight: bold; font-stretch: inherit; line-height: inherit; vertical-align: baseline; caret-color: #3e3e3e; color: #3e3e3e; letter-spacing: normal; orphans: auto; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; widows: auto; word-spacing: 0px; -webkit-text-size-adjust: auto; -webkit-text-stroke-width: 0px; text-decoration: none;\">automatickou pojistkou</strong><span style=\"caret-color: #3e3e3e; color: #3e3e3e; font-family: \'Open Sans\'; font-size: 16px; font-style: normal; font-variant-caps: normal; font-weight: normal; letter-spacing: normal; orphans: auto; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; widows: auto; word-spacing: 0px; -webkit-text-size-adjust: auto; -webkit-text-stroke-width: 0px; background-color: #ffffff; text-decoration: none; display: inline !important; float: none;\"> proti náhodnému výstřelu. Tělo vzduchovky je vybaveno </span><strong style=\"box-sizing: border-box; margin: 0px; padding: 0px; border: 0px; font-family: \'Open Sans\'; font-size: 16px; font-style: normal; font-variant-caps: normal; font-weight: bold; font-stretch: inherit; line-height: inherit; vertical-align: baseline; caret-color: #3e3e3e; color: #3e3e3e; letter-spacing: normal; orphans: auto; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; widows: auto; word-spacing: 0px; -webkit-text-size-adjust: auto; -webkit-text-stroke-width: 0px; text-decoration: none;\">11mm rybinou</strong><span style=\"caret-color: #3e3e3e; color: #3e3e3e; font-family: \'Open Sans\'; font-size: 16px; font-style: normal; font-variant-caps: normal; font-weight: normal; letter-spacing: normal; orphans: auto; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; widows: auto; word-spacing: 0px; -webkit-text-size-adjust: auto; -webkit-text-stroke-width: 0px; background-color: #ffffff; text-decoration: none; display: inline !important; float: none;\"> pro usazení puškohledu. Výrobce v setu dodává svůj </span><strong style=\"box-sizing: border-box; margin: 0px; padding: 0px; border: 0px; font-family: \'Open Sans\'; font-size: 16px; font-style: normal; font-variant-caps: normal; font-weight: bold; font-stretch: inherit; line-height: inherit; vertical-align: baseline; caret-color: #3e3e3e; color: #3e3e3e; letter-spacing: normal; orphans: auto; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; widows: auto; word-spacing: 0px; -webkit-text-size-adjust: auto; -webkit-text-stroke-width: 0px; text-decoration: none;\">puškohled 6x42</strong><span style=\"caret-color: #3e3e3e; color: #3e3e3e; font-family: \'Open Sans\'; font-size: 16px; font-style: normal; font-variant-caps: normal; font-weight: normal; letter-spacing: normal; orphans: auto; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; widows: auto; word-spacing: 0px; -webkit-text-size-adjust: auto; -webkit-text-stroke-width: 0px; background-color: #ffffff; text-decoration: none; display: inline !important; float: none;\">.</span><br style=\"box-sizing: border-box; caret-color: #3e3e3e; color: #3e3e3e; font-family: \'Open Sans\'; font-size: 16px; font-style: normal; font-variant-caps: normal; font-weight: normal; letter-spacing: normal; orphans: auto; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; widows: auto; word-spacing: 0px; -webkit-text-size-adjust: auto; -webkit-text-stroke-width: 0px; text-decoration: none;\" /><br style=\"box-sizing: border-box; caret-color: #3e3e3e; color: #3e3e3e; font-family: \'Open Sans\'; font-size: 16px; font-style: normal; font-variant-caps: normal; font-weight: normal; letter-spacing: normal; orphans: auto; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; widows: auto; word-spacing: 0px; -webkit-text-size-adjust: auto; -webkit-text-stroke-width: 0px; text-decoration: none;\" /><strong style=\"box-sizing: border-box; margin: 0px; padding: 0px; border: 0px; font-family: \'Open Sans\'; font-size: 16px; font-style: normal; font-variant-caps: normal; font-weight: bold; font-stretch: inherit; line-height: inherit; vertical-align: baseline; caret-color: #3e3e3e; color: #3e3e3e; letter-spacing: normal; orphans: auto; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; widows: auto; word-spacing: 0px; -webkit-text-size-adjust: auto; -webkit-text-stroke-width: 0px; text-decoration: none;\">Spodní nátah</strong><span style=\"caret-color: #3e3e3e; color: #3e3e3e; font-family: \'Open Sans\'; font-size: 16px; font-style: normal; font-variant-caps: normal; font-weight: normal; letter-spacing: normal; orphans: auto; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; widows: auto; word-spacing: 0px; -webkit-text-size-adjust: auto; -webkit-text-stroke-width: 0px; background-color: #ffffff; text-decoration: none; display: inline !important; float: none;\"> zaručí přesnost i po delší době používání.</span><br style=\"box-sizing: border-box; caret-color: #3e3e3e; color: #3e3e3e; font-family: \'Open Sans\'; font-size: 16px; font-style: normal; font-variant-caps: normal; font-weight: normal; letter-spacing: normal; orphans: auto; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; widows: auto; word-spacing: 0px; -webkit-text-size-adjust: auto; -webkit-text-stroke-width: 0px; text-decoration: none;\" /><br style=\"box-sizing: border-box; caret-color: #3e3e3e; color: #3e3e3e; font-family: \'Open Sans\'; font-size: 16px; font-style: normal; font-variant-caps: normal; font-weight: normal; letter-spacing: normal; orphans: auto; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; widows: auto; word-spacing: 0px; -webkit-text-size-adjust: auto; -webkit-text-stroke-width: 0px; text-decoration: none;\" /><strong style=\"box-sizing: border-box; margin: 0px; padding: 0px; border: 0px; font-family: \'Open Sans\'; font-size: 16px; font-style: normal; font-variant-caps: normal; font-weight: bold; font-stretch: inherit; line-height: inherit; vertical-align: baseline; caret-color: #3e3e3e; color: #3e3e3e; letter-spacing: normal; orphans: auto; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; widows: auto; word-spacing: 0px; -webkit-text-size-adjust: auto; -webkit-text-stroke-width: 0px; text-decoration: none;\">Naše zkušenost: </strong><span style=\"caret-color: #3e3e3e; color: #3e3e3e; font-family: \'Open Sans\'; font-size: 16px; font-style: normal; font-variant-caps: normal; font-weight: normal; letter-spacing: normal; orphans: auto; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; widows: auto; word-spacing: 0px; -webkit-text-size-adjust: auto; -webkit-text-stroke-width: 0px; background-color: #ffffff; text-decoration: none; display: inline !important; float: none;\">Opravdu jedna z nejoblíbenějších vzduchovek u nás. Vzduchovka má sice větší hmotnost, ale pro pořádné chlapy to není žádný problém.</span></p>','{\"inheritCategories\":false,\"inheritVideos\":false,\"inheritFiles\":false,\"inheritPages\":false,\"inheritLinks\":false}','{\"name\":\"John Doe\"}','{}'),(21,11,2,0,'Vzduchovka EN','Vzduchovka EN','Vzduchovka EN','',NULL,'','','{\"inheritCategories\":false,\"inheritVideos\":false,\"inheritFiles\":false,\"inheritPages\":false,\"inheritLinks\":false}','{\"footerMenuMobile\":[{\"columnFirst\":[{\"title\":\"Test\"}],\"columnSecond\":[{\"title\":\"Test\"}]}],\"name\":\"John Doe\"}','{}'),(22,11,1,1,'Vzduchovka Kral Arms N-08 Camo 5,5mm','Vzduchovka Kral Arms N-08 Camo 5,5mm','Vzduchovka Kral Arms N-08 Camo 5,5mm','',NULL,'Pružinová vzduchovka se spodním nátahem od tureckého výrobce Kral Arms. Tento model má pevnou hlaveň, která příznivě ovlivňuje přesnost střelby. Spodní páka pod hlavní slouží k natažení zbraně. Syntetická pažba s imitací dřeva má zdrsněný povrch v místě úchopu a lícnici Monte Carlo.','<p style=\"box-sizing: border-box; margin: 0px 0px 1rem; padding: 0px; border: 0px; font-family: \'Open Sans\'; font-size: 16px; font-style: normal; font-variant-caps: normal; font-weight: normal; font-stretch: inherit; line-height: 1.8; vertical-align: baseline; caret-color: #3e3e3e; color: #3e3e3e; letter-spacing: normal; orphans: auto; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; widows: auto; word-spacing: 0px; -webkit-text-size-adjust: auto; -webkit-text-stroke-width: 0px; text-decoration: none;\">Pružinová vzduchovka<strong style=\"box-sizing: border-box; margin: 0px; padding: 0px; border: 0px; font-family: inherit; font-size: inherit; font-style: inherit; font-variant-caps: inherit; font-weight: bold; font-stretch: inherit; line-height: inherit; vertical-align: baseline;\"> se spodním nátahem</strong> od tureckého výrobce Kral Arms. Tento model má pevnou hlaveň, která příznivě ovlivňuje přesnost střelby<span style=\"box-sizing: border-box; margin: 0px; padding: 0px; border: 0px; font-family: Arial, Helvetica, sans-serif; font-size: 14px; font-style: inherit; font-variant-caps: inherit; font-weight: inherit; font-stretch: inherit; line-height: inherit; vertical-align: baseline; background-color: #ffffff;\">.</span> Spodní páka pod hlavní slouží k natažení zbraně.</p>\n<p style=\"box-sizing: border-box; margin: 0px 0px 1rem; padding: 0px; border: 0px; font-family: \'Open Sans\'; font-size: 16px; font-style: normal; font-variant-caps: normal; font-weight: normal; font-stretch: inherit; line-height: 1.8; vertical-align: baseline; caret-color: #3e3e3e; color: #3e3e3e; letter-spacing: normal; orphans: auto; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; widows: auto; word-spacing: 0px; -webkit-text-size-adjust: auto; -webkit-text-stroke-width: 0px; text-decoration: none;\"><strong style=\"box-sizing: border-box; margin: 0px; padding: 0px; border: 0px; font-family: inherit; font-size: inherit; font-style: inherit; font-variant-caps: inherit; font-weight: bold; font-stretch: inherit; line-height: inherit; vertical-align: baseline;\">Syntetická pažba s imitací dřeva</strong> má zdrsněný povrch v místě úchopu a lícnici Monte Carlo. Pažba je vhodná<strong style=\"box-sizing: border-box; margin: 0px; padding: 0px; border: 0px; font-family: inherit; font-size: inherit; font-style: inherit; font-variant-caps: inherit; font-weight: bold; font-stretch: inherit; line-height: inherit; vertical-align: baseline;\"> pro pravoruké i levoruké střelce</strong> a na jejím konci je umístěna gumová botka pro absorpci zpětného rázu.</p>\n<p style=\"box-sizing: border-box; margin: 0px 0px 1rem; padding: 0px; border: 0px; font-family: \'Open Sans\'; font-size: 16px; font-style: normal; font-variant-caps: normal; font-weight: normal; font-stretch: inherit; line-height: 1.8; vertical-align: baseline; caret-color: #3e3e3e; color: #3e3e3e; letter-spacing: normal; orphans: auto; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; widows: auto; word-spacing: 0px; -webkit-text-size-adjust: auto; -webkit-text-stroke-width: 0px; text-decoration: none;\">K přesnému zamíření vám dopomohou světlovodná <strong style=\"box-sizing: border-box; margin: 0px; padding: 0px; border: 0px; font-family: inherit; font-size: inherit; font-style: inherit; font-variant-caps: inherit; font-weight: bold; font-stretch: inherit; line-height: inherit; vertical-align: baseline;\">TruGlo mířidla</strong>. Zadní hledí je navíc stranově i výškově stavitelné. Montáž puškohledu vám umožní <strong style=\"box-sizing: border-box; margin: 0px; padding: 0px; border: 0px; font-family: inherit; font-size: inherit; font-style: inherit; font-variant-caps: inherit; font-weight: bold; font-stretch: inherit; line-height: inherit; vertical-align: baseline;\">11 mm drážky</strong>. Manuální pojistka proti náhodnému výstřelu je umístěna v lučíku před spouští.</p>\n<h2>KLÍČOVÉ VLASTNOSTI</h2>\n<ul>\n<li style=\"box-sizing: border-box; border: 0px none; font-family: \'Open Sans\'; font-size: 16px; font-style: normal; font-variant-caps: normal; font-weight: normal; font-stretch: inherit; line-height: 1.8; vertical-align: baseline; caret-color: #3e3e3e; color: #3e3e3e; letter-spacing: normal; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; word-spacing: 0px; -moz-text-size-adjust: auto; -webkit-text-stroke-width: 0px; text-decoration: none;\">Spodní nátah</li>\n<li style=\"box-sizing: border-box; border: 0px none; font-family: \'Open Sans\'; font-size: 16px; font-style: normal; font-variant-caps: normal; font-weight: normal; font-stretch: inherit; line-height: 1.8; vertical-align: baseline; caret-color: #3e3e3e; color: #3e3e3e; letter-spacing: normal; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; word-spacing: 0px; -moz-text-size-adjust: auto; -webkit-text-stroke-width: 0px; text-decoration: none;\">Syntetická pažba s imitací dřeva</li>\n<li style=\"box-sizing: border-box; border: 0px none; font-family: \'Open Sans\'; font-size: 16px; font-style: normal; font-variant-caps: normal; font-weight: normal; font-stretch: inherit; line-height: 1.8; vertical-align: baseline; caret-color: #3e3e3e; color: #3e3e3e; letter-spacing: normal; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; word-spacing: 0px; -moz-text-size-adjust: auto; -webkit-text-stroke-width: 0px; text-decoration: none;\">TruGlo mířidla</li>\n<li style=\"box-sizing: border-box; border: 0px none; font-family: \'Open Sans\'; font-size: 16px; font-style: normal; font-variant-caps: normal; font-weight: normal; font-stretch: inherit; line-height: 1.8; vertical-align: baseline; caret-color: #3e3e3e; color: #3e3e3e; letter-spacing: normal; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; word-spacing: 0px; -moz-text-size-adjust: auto; -webkit-text-stroke-width: 0px; text-decoration: none;\">Vhodná pro praváky i leváky</li>\n</ul>','{\"inheritCategories\":false,\"inheritVideos\":false,\"inheritFiles\":false,\"inheritPages\":false,\"inheritLinks\":false}','{\"footerMenuMobile\":[{\"columnFirst\":[{\"title\":\"Prvni sloupec\",\"items\":[{\"link\":\"193\"},{\"link\":\"78\"}]}],\"columnSecond\":[{\"title\":\"Druhy sloupec\"}]}],\"name\":\"John Doe\"}','{}'),(25,13,2,0,'','','','',NULL,'','','{\"inheritCategories\":false,\"inheritVideos\":false,\"inheritFiles\":false,\"inheritPages\":false,\"inheritLinks\":false}','{\"file\":{\"id\":\"78\",\"name\":\"payment-detail-T438632305 (2).pdf\"},\"name\":\"John Doe\"}','{}'),(26,13,1,0,'','Test mazani','Test mazani','',NULL,'','','{\"inheritCategories\":false,\"inheritVideos\":false,\"inheritFiles\":false,\"inheritPages\":false,\"inheritLinks\":false}','{\"name\":\"John Doe\"}','{}'),(27,14,2,0,'','','','',NULL,'','','{\"inheritCategories\":false,\"inheritVideos\":false,\"inheritFiles\":false,\"inheritPages\":false,\"inheritLinks\":false}','{}','{}'),(28,14,1,0,'Název','Název','Název','',NULL,'','','{\"inheritCategories\":false,\"inheritVideos\":false,\"inheritFiles\":false,\"inheritPages\":false,\"inheritLinks\":false}','{\"feeds\":[{\"showItems\":false,\"heureka\":\"heureka\"}],\"name\":\"test\"}','{}');
/*!40000 ALTER TABLE `product_localization` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `product_parameter`
--

DROP TABLE IF EXISTS `product_parameter`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `product_parameter` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `productId` int(11) NOT NULL,
  `parameterId` int(11) NOT NULL,
  `parameterValueId` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `productId_parameterId_parameterValueId` (`productId`,`parameterId`,`parameterValueId`),
  KEY `parameterId` (`parameterId`),
  KEY `parameterValueId` (`parameterValueId`),
  CONSTRAINT `product_parameter_ibfk_4` FOREIGN KEY (`productId`) REFERENCES `product` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `product_parameter_ibfk_5` FOREIGN KEY (`parameterId`) REFERENCES `parameter` (`id`) ON UPDATE CASCADE,
  CONSTRAINT `product_parameter_ibfk_6` FOREIGN KEY (`parameterValueId`) REFERENCES `parameter_value` (`id`) ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=14719 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `product_parameter`
--

LOCK TABLES `product_parameter` WRITE;
/*!40000 ALTER TABLE `product_parameter` DISABLE KEYS */;
INSERT INTO `product_parameter` VALUES (14222,2,113,6564),(14218,4,113,6565),(14219,4,114,6570),(14220,4,114,6571),(14265,4,116,6580),(14258,4,117,6577),(14259,4,118,6578),(14509,4,119,6585),(14261,4,120,6579),(14214,5,113,6566),(14215,5,114,6570),(14216,5,114,6571),(14217,5,119,6575),(14211,6,113,6565),(14212,6,114,6570),(14213,6,119,6574),(14141,7,113,6565),(14142,7,114,6570),(14225,7,119,6572),(14236,8,113,6566),(14208,8,119,6573),(14201,9,113,6564),(14202,9,114,6567),(14233,9,117,6577),(14367,9,118,6581),(14203,9,119,6572),(14512,10,121,6583),(14513,10,127,6586),(14550,10,128,6592),(14681,11,113,6564),(14685,11,114,6570),(14526,11,121,6583),(14527,11,127,6588),(14547,11,128,6591),(14717,13,114,6567),(14718,13,114,6570),(14587,13,121,6583),(14588,13,127,6588);
/*!40000 ALTER TABLE `product_parameter` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `product_product`
--

DROP TABLE IF EXISTS `product_product`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `product_product` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mainProductId` int(11) NOT NULL,
  `attachedProductId` int(11) NOT NULL,
  `type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `sort` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `productMainId_productId_type` (`mainProductId`,`attachedProductId`,`type`),
  KEY `productId` (`attachedProductId`),
  KEY `mainProductId` (`mainProductId`),
  CONSTRAINT `product_product_ibfk_1` FOREIGN KEY (`mainProductId`) REFERENCES `product` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `product_product_ibfk_4` FOREIGN KEY (`attachedProductId`) REFERENCES `product` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COLLATE=utf8_czech_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `product_product`
--

LOCK TABLES `product_product` WRITE;
/*!40000 ALTER TABLE `product_product` DISABLE KEYS */;
INSERT INTO `product_product` VALUES (1,9,7,'normal',0);
/*!40000 ALTER TABLE `product_product` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `product_review`
--

DROP TABLE IF EXISTS `product_review`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `product_review` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `productId` int(11) NOT NULL,
  `userId` int(11) DEFAULT NULL,
  `isWebMaster` tinyint(1) NOT NULL DEFAULT '0',
  `isMain` tinyint(1) NOT NULL COMMENT 'main review for user',
  `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `email` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `stars` int(11) DEFAULT NULL,
  `date` datetime DEFAULT NULL,
  `text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  PRIMARY KEY (`id`),
  KEY `idtree_idx` (`productId`),
  KEY `userId` (`userId`),
  CONSTRAINT `product_review_ibfk_3` FOREIGN KEY (`productId`) REFERENCES `product` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `product_review_ibfk_4` FOREIGN KEY (`userId`) REFERENCES `user` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `product_review`
--

LOCK TABLES `product_review` WRITE;
/*!40000 ALTER TABLE `product_review` DISABLE KEYS */;
/*!40000 ALTER TABLE `product_review` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `product_tree`
--

DROP TABLE IF EXISTS `product_tree`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `product_tree` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `productId` int(11) NOT NULL,
  `treeId` int(11) NOT NULL,
  `sort` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `productId_treeId` (`productId`,`treeId`),
  KEY `treeId` (`treeId`),
  CONSTRAINT `product_tree_ibfk_1` FOREIGN KEY (`productId`) REFERENCES `product` (`id`) ON DELETE CASCADE,
  CONSTRAINT `product_tree_ibfk_2` FOREIGN KEY (`treeId`) REFERENCES `tree` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=13709 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `product_tree`
--

LOCK TABLES `product_tree` WRITE;
/*!40000 ALTER TABLE `product_tree` DISABLE KEYS */;
INSERT INTO `product_tree` VALUES (13693,7,94,0),(13696,9,94,1),(13697,8,94,0),(13698,6,94,0),(13699,5,94,0),(13700,4,94,0),(13701,2,94,0),(13703,4,77,1),(13708,14,21,0);
/*!40000 ALTER TABLE `product_tree` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `product_variant`
--

DROP TABLE IF EXISTS `product_variant`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `product_variant` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `productId` int(11) NOT NULL,
  `param1ValueId` int(11) DEFAULT NULL,
  `param2ValueId` int(11) DEFAULT NULL,
  `ean` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `created` datetime DEFAULT NULL,
  `createdBy` int(11) DEFAULT NULL,
  `edited` datetime DEFAULT NULL,
  `editedBy` int(11) DEFAULT NULL,
  `sort` int(11) NOT NULL DEFAULT '0',
  `soldCount` int(11) NOT NULL,
  `isInDiscount` int(11) DEFAULT NULL,
  `extId` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `productId_param1ValueId_param2ValueId` (`productId`,`param1ValueId`,`param2ValueId`),
  UNIQUE KEY `extId` (`extId`),
  KEY `param2ValueId` (`param2ValueId`),
  KEY `param1ValueId` (`param1ValueId`),
  CONSTRAINT `product_variant_ibfk_13` FOREIGN KEY (`param2ValueId`) REFERENCES `parameter_value` (`id`) ON DELETE NO ACTION ON UPDATE CASCADE,
  CONSTRAINT `product_variant_ibfk_14` FOREIGN KEY (`param1ValueId`) REFERENCES `parameter_value` (`id`) ON DELETE NO ACTION ON UPDATE CASCADE,
  CONSTRAINT `product_variant_ibfk_2` FOREIGN KEY (`productId`) REFERENCES `product` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=64 DEFAULT CHARSET=utf8 COLLATE=utf8_czech_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `product_variant`
--

LOCK TABLES `product_variant` WRITE;
/*!40000 ALTER TABLE `product_variant` DISABLE KEYS */;
INSERT INTO `product_variant` VALUES (44,9,6564,NULL,'0','','2021-06-10 16:04:45',12,'2021-11-30 16:33:58',3,0,0,NULL,NULL),(46,8,NULL,NULL,'0','','2021-06-10 17:30:37',1,'2021-06-29 16:17:25',14,0,0,NULL,NULL),(47,7,NULL,NULL,'0','','2021-06-10 22:40:43',3,'2021-06-28 08:53:36',3,0,0,NULL,NULL),(48,6,NULL,NULL,'0','','2021-06-11 11:26:01',13,'2021-06-28 08:33:48',3,0,0,NULL,NULL),(50,9,6565,NULL,'','','2021-06-23 09:38:47',1,'2021-11-30 16:33:58',3,1,0,NULL,NULL),(51,5,NULL,NULL,'0','','2021-06-28 08:34:05',3,'2021-06-28 08:34:53',3,0,0,NULL,NULL),(52,4,NULL,NULL,'*********','Kód','2021-06-28 08:35:18',3,'2021-07-07 17:05:23',1,0,0,NULL,NULL),(53,2,NULL,NULL,'0','','2021-06-28 08:36:31',3,'2021-06-28 08:37:05',3,0,0,NULL,NULL),(56,4,6564,NULL,'','','2021-07-01 06:45:10',12,'2021-07-07 17:05:23',1,1,0,NULL,NULL),(57,10,NULL,NULL,'0','','2021-07-01 17:22:44',1,'2022-01-13 13:37:38',4,0,0,NULL,NULL),(58,11,NULL,NULL,'0','','2021-07-04 18:16:09',9,'2022-04-04 10:42:31',13,0,0,NULL,NULL),(59,9,6566,NULL,'','','2021-07-06 10:23:26',3,'2021-11-30 16:33:58',3,2,0,NULL,NULL),(61,13,NULL,NULL,'0','','2021-07-13 11:38:51',13,'2021-09-03 09:59:26',5,0,0,NULL,NULL),(62,14,6565,NULL,'0','','2021-08-23 14:44:01',3,'2022-02-08 14:09:23',4,0,0,NULL,NULL),(63,14,6566,NULL,'','','2021-11-23 10:46:47',13,'2022-02-08 14:09:23',4,1,0,NULL,NULL);
/*!40000 ALTER TABLE `product_variant` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `product_variant_localization`
--

DROP TABLE IF EXISTS `product_variant_localization`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `product_variant_localization` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `variantId` int(11) NOT NULL,
  `mutationId` int(11) NOT NULL,
  `active` int(11) NOT NULL DEFAULT '0',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `variantId_mutationId` (`variantId`,`mutationId`),
  KEY `mutationId` (`mutationId`),
  CONSTRAINT `product_variant_localization_ibfk_1` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `product_variant_localization_ibfk_2` FOREIGN KEY (`variantId`) REFERENCES `product_variant` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=112 DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `product_variant_localization`
--

LOCK TABLES `product_variant_localization` WRITE;
/*!40000 ALTER TABLE `product_variant_localization` DISABLE KEYS */;
INSERT INTO `product_variant_localization` VALUES (72,44,1,1,NULL),(73,44,2,1,NULL),(76,46,1,1,NULL),(77,46,2,1,NULL),(78,47,1,1,NULL),(79,47,2,1,NULL),(80,48,1,1,NULL),(81,48,2,1,NULL),(84,50,1,1,NULL),(85,50,2,0,NULL),(86,51,1,1,NULL),(87,51,2,1,NULL),(88,52,1,1,NULL),(89,52,2,1,NULL),(90,53,1,1,NULL),(91,53,2,1,NULL),(96,56,1,0,NULL),(97,56,2,0,NULL),(98,57,1,1,NULL),(99,57,2,0,NULL),(100,58,1,1,NULL),(101,58,2,1,NULL),(102,59,1,1,NULL),(103,59,2,1,NULL),(106,61,1,0,NULL),(107,61,2,0,NULL),(108,62,1,1,NULL),(109,62,2,1,NULL),(110,63,1,1,NULL),(111,63,2,1,NULL);
/*!40000 ALTER TABLE `product_variant_localization` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `product_variant_price`
--

DROP TABLE IF EXISTS `product_variant_price`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `product_variant_price` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `mutationId` int(11) NOT NULL,
  `priceLevelId` int(11) NOT NULL,
  `productId` int(11) NOT NULL,
  `productVariantId` int(11) NOT NULL,
  `price_amount` decimal(18,4) NOT NULL,
  `price_currency` char(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  PRIMARY KEY (`id`),
  KEY `mutationId` (`mutationId`),
  KEY `productVariantId` (`productVariantId`),
  KEY `priceLevelId` (`priceLevelId`),
  KEY `productId` (`productId`),
  CONSTRAINT `product_variant_price_ibfk_1` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `product_variant_price_ibfk_2` FOREIGN KEY (`productVariantId`) REFERENCES `product_variant` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `product_variant_price_ibfk_3` FOREIGN KEY (`priceLevelId`) REFERENCES `price_level` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `product_variant_price_ibfk_4` FOREIGN KEY (`productId`) REFERENCES `product` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=93 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_czech_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `product_variant_price`
--

LOCK TABLES `product_variant_price` WRITE;
/*!40000 ALTER TABLE `product_variant_price` DISABLE KEYS */;
INSERT INTO `product_variant_price` VALUES (13,1,1,9,44,180.0000,'CZK'),(14,1,2,9,44,130.0000,'CZK'),(15,2,1,9,44,20.0000,'EUR'),(16,2,2,9,44,19.0000,'EUR'),(21,1,1,6,48,300.0000,'CZK'),(22,1,2,6,48,290.0000,'CZK'),(23,2,1,6,48,20.0000,'EUR'),(24,2,2,6,48,23.0000,'EUR'),(25,1,1,9,50,150.0000,'CZK'),(26,1,2,9,50,1000.0000,'CZK'),(27,2,1,9,50,10.0000,'EUR'),(28,2,2,9,50,10.0000,'EUR'),(29,1,1,8,46,500.0000,'CZK'),(30,1,2,8,46,400.0000,'CZK'),(31,2,1,8,46,10.0000,'EUR'),(32,2,2,8,46,10.0000,'EUR'),(33,1,1,7,47,2000.0000,'CZK'),(34,1,2,7,47,1980.0000,'CZK'),(35,2,1,7,47,10.0000,'EUR'),(36,2,2,7,47,20.0000,'EUR'),(37,1,1,5,51,2500.0000,'CZK'),(38,1,2,5,51,2400.0000,'CZK'),(39,2,1,5,51,0.0000,'EUR'),(40,2,2,5,51,0.0000,'EUR'),(41,1,1,4,52,50.0000,'CZK'),(42,1,2,4,52,40.0000,'CZK'),(43,2,1,4,52,0.0000,'EUR'),(44,2,2,4,52,0.0000,'EUR'),(49,1,1,2,53,1000.0000,'CZK'),(50,1,2,2,53,900.0000,'CZK'),(51,2,1,2,53,0.0000,'EUR'),(52,2,2,2,53,0.0000,'EUR'),(61,1,1,4,56,10.0000,'CZK'),(62,1,2,4,56,10.0000,'CZK'),(63,2,1,4,56,0.0000,'EUR'),(64,2,2,4,56,0.0000,'EUR'),(65,1,1,9,59,100.0000,'CZK'),(66,1,2,9,59,100.0000,'CZK'),(67,2,1,9,59,10.0000,'EUR'),(68,2,2,9,59,10.0000,'EUR'),(73,1,1,10,57,5281.0000,'CZK'),(74,1,2,10,57,0.0000,'CZK'),(75,2,1,10,57,0.0000,'EUR'),(76,2,2,10,57,0.0000,'EUR'),(77,1,1,11,58,2636.0000,'CZK'),(78,1,2,11,58,0.0000,'CZK'),(79,2,1,11,58,0.0000,'EUR'),(80,2,2,11,58,0.0000,'EUR'),(81,1,1,13,61,0.0000,'CZK'),(82,1,2,13,61,0.0000,'CZK'),(83,2,1,13,61,0.0000,'EUR'),(84,2,2,13,61,0.0000,'EUR'),(85,1,1,14,62,0.0000,'CZK'),(86,1,2,14,62,0.0000,'CZK'),(87,2,1,14,62,0.0000,'EUR'),(88,2,2,14,62,0.0000,'EUR'),(89,1,1,14,63,0.0000,'CZK'),(90,1,2,14,63,0.0000,'CZK'),(91,2,1,14,63,0.0000,'EUR'),(92,2,2,14,63,0.0000,'EUR');
/*!40000 ALTER TABLE `product_variant_price` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `redirect`
--

DROP TABLE IF EXISTS `redirect`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `redirect` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `oldUrl` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `newUrl` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `code` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `oldUrl` (`oldUrl`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_czech_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `redirect`
--

LOCK TABLES `redirect` WRITE;
/*!40000 ALTER TABLE `redirect` DISABLE KEYS */;
/*!40000 ALTER TABLE `redirect` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `reference`
--

DROP TABLE IF EXISTS `reference`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `reference` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `internalName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `reference`
--

LOCK TABLES `reference` WRITE;
/*!40000 ALTER TABLE `reference` DISABLE KEYS */;
INSERT INTO `reference` VALUES (1,'Test reference','{}'),(2,'Testovaci reference','{\"base\":[{\"annot\":[{\"bg\":\"u-bgc-green\",\"btn\":[{\"toggle\":\"customHref\",\"customHref\":[{\"href\":\"#\",\"hrefName\":\"Chci vyřešit ochranu značky\"}]}],\"right\":[{\"link\":\"https://www.youtube.com/watch?v=FOIZ86CDW8I\",\"image\":\"143\"}]}],\"testimonial\":1,\"author\":3}]}'),(3,'Reference 3','{\"base\":[{\"annot\":[{\"bg\":\"u-bgc-yellow\",\"btn\":[{\"toggle\":\"customHref\",\"customHref\":[{\"href\":\"#\",\"hrefName\":\"Chci vyřešit ochranu značky\"}]}],\"right\":[{\"link\":\"https://www.youtube.com/watch?v=FOIZ86CDW8I\"}]}]}]}');
/*!40000 ALTER TABLE `reference` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `reference_localization`
--

DROP TABLE IF EXISTS `reference_localization`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `reference_localization` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mutationId` int(11) NOT NULL,
  `referenceId` int(11) NOT NULL,
  `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `nameAnchor` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `nameTitle` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `keywords` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `isTop` int(11) NOT NULL DEFAULT '0',
  `public` int(11) NOT NULL DEFAULT '0',
  `forceNoIndex` int(11) NOT NULL DEFAULT '0',
  `hideInSearch` int(11) NOT NULL DEFAULT '0',
  `hideInSitemap` int(11) NOT NULL DEFAULT '0',
  `publicFrom` datetime DEFAULT NULL,
  `publicTo` datetime DEFAULT NULL,
  `edited` int(11) DEFAULT NULL,
  `editedTime` datetime DEFAULT NULL,
  `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `customContentSchemeJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `customContentJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `viewsNumber` int(10) unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `FK_blog_mutation` (`mutationId`) USING BTREE,
  KEY `FK_blog_localization_blog` (`referenceId`) USING BTREE,
  CONSTRAINT `reference_localization_ibfk_2` FOREIGN KEY (`referenceId`) REFERENCES `reference` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `reference_localization_ibfk_3` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `reference_localization`
--

LOCK TABLES `reference_localization` WRITE;
/*!40000 ALTER TABLE `reference_localization` DISABLE KEYS */;
INSERT INTO `reference_localization` VALUES (1,1,1,'Test reference','Test reference','Test reference','','',0,1,0,0,0,'2025-05-22 09:37:00','2125-05-22 09:37:00',4,'2025-05-22 09:37:39','{}',NULL,'{}',8),(2,1,2,'Yarmill: Jak získat legal-safe data pro AI asistenta','Yarmill: Jak získat legal-safe data pro AI asistenta','Yarmill: Jak získat legal-safe data pro AI asistenta','SEO popis anotace','',0,1,0,0,0,'2025-05-28 01:29:00','2125-05-28 01:29:00',34,'2025-05-30 08:17:11','{\"base\":[{\"annotation\":\"Yarmill je v Česku vyvinutá aplikace – profesionální tréninkový deník a datová platforma pro sportovní svazy a týmy. S cílem zvýšit konkurenceschopnost českých sportovců se náš klient Yarmill pustil do společného projektu s Českým olympijským výborem (ČOV) a začal pracovat na vytvoření rozsáhlé odborné AI knihovny. Už od začátku se Yarmill potýkal se složitostí problematiky autorských práv.\"}]}',NULL,'{\"highlights____oohEVoVn8_OrkldZ5oIxx\":[{\"items\":[{\"image\":\"135\",\"name\":\"Lorem\",\"text\":\"Lorem ipsum dolor sit amet consectetur adipisicing elit. Quisquam, quos.\"},{\"image\":\"133\",\"name\":\"Quisquam, quos.\",\"text\":\"Lorem ipsum dolor sit amet consectetur adipisicing elit. Quisquam, quos.\"},{\"image\":\"134\",\"name\":\"Dolor sit amet\",\"text\":\"Lorem ipsum dolor sit amet consectetur adipisicing elit. Quisquam, quos.\"}]}],\"content____W5rLe5e76JzHJNAg9covc\":[{\"content\":\"<h3>První pokusy: Když se nedaří trefit správné jméno</h3>\\n<p>Zakladatelé Denisa Hrubešová a David Slačálek měli od začátku jasno – vytvořit úspěšnou českou platformu pro tvůrce a jejich fanoušky. Co jim ale dělalo problém? Najít název. \\u2028<br />„Náš první právník nám dobře radil, že je potřeba co nejdřív zaregistrovat ochrannou známku. Ale kreativní nápady chyběly. Tak jsme použili generátor náhodných slov a vybrali si Indity,“ vzpomíná Denisa Hrubešová.\\u2028<br />O měsíc později přišla ze Španělska výzva: název Indity se až příliš podobal už existující značce. Denisa s Davidem se tak ocitli zpátky na začátku – a týden před spuštěním platformy neměli jméno. Nakonec se vymyslel název Pickey, který rovnou zaregistrovali. Ale ani ten se neosvědčil: „Sice se to dobře vyslovovalo, ale v podcastech se na něj špatně odkazovalo. Zpětně hodnotím, že jsme do toho měli narvat na začátku mnohem víc peněz – které jsme v té době ale neměli.“ </p>\"}],\"testimonial____1E_6ZDuYCD5RmyO2g_Bch\":[{\"testimonial\":3}],\"content____YzkFPA5HgMsuOqR3O3uKh\":[{\"content\":\"<h3>První pokusy: Když se nedaří trefit správné jméno</h3>\\n<p>Zakladatelé Denisa Hrubešová a David Slačálek měli od začátku jasno – vytvořit úspěšnou českou platformu pro tvůrce a jejich fanoušky. Co jim ale dělalo problém? Najít název. \\u2028<br />„Náš první právník nám dobře radil, že je potřeba co nejdřív zaregistrovat ochrannou známku. Ale kreativní nápady chyběly. Tak jsme použili generátor náhodných slov a vybrali si Indity,“ vzpomíná Denisa Hrubešová.\\u2028<br />O měsíc později přišla ze Španělska výzva: název Indity se až příliš podobal už existující značce. Denisa s Davidem se tak ocitli zpátky na začátku – a týden před spuštěním platformy neměli jméno. Nakonec se vymyslel název Pickey, který rovnou zaregistrovali. Ale ani ten se neosvědčil: „Sice se to dobře vyslovovalo, ale v podcastech se na něj špatně odkazovalo. Zpětně hodnotím, že jsme do toho měli narvat na začátku mnohem víc peněz – které jsme v té době ale neměli.“ </p>\\n<blockquote>Potřebujete řešit ochranné známky? Využijte s námi dotace od EU. <a href=\\\"/kontakt\\\">Ozvěte se nám</a>.</blockquote>\\n<h3>První pokusy: Když se nedaří trefit správné jméno</h3>\\n<p>Zakladatelé Denisa Hrubešová a David Slačálek měli od začátku jasno – vytvořit úspěšnou českou platformu pro tvůrce a jejich fanoušky. Co jim ale dělalo problém? Najít název. \\u2028<br />„Náš první právník nám dobře radil, že je potřeba co nejdřív zaregistrovat ochrannou známku. Ale kreativní nápady chyběly. Tak jsme použili generátor náhodných slov a vybrali si Indity,“ vzpomíná Denisa Hrubešová.\\u2028<br />O měsíc později přišla ze Španělska výzva: název Indity se až příliš podobal už existující značce. Denisa s Davidem se tak ocitli zpátky na začátku – a týden před spuštěním platformy neměli jméno. Nakonec se vymyslel název Pickey, který rovnou zaregistrovali. Ale ani ten se neosvědčil: „Sice se to dobře vyslovovalo, ale v podcastech se na něj špatně odkazovalo. Zpětně hodnotím, že jsme do toho měli narvat na začátku mnohem víc peněz – které jsme v té době ale neměli.“ </p>\"}],\"testimonial____FDKe_JYa6xuwKdYjReRlm\":[{\"testimonial\":1}],\"content____oWOdDkZynGPhl4tsQY2eq\":[{\"content\":\"<h3>První pokusy: Když se nedaří trefit správné jméno</h3>\\n<p>Zakladatelé Denisa Hrubešová a David Slačálek měli od začátku jasno – vytvořit úspěšnou českou platformu pro tvůrce a jejich fanoušky. Co jim ale dělalo problém? Najít název. \\u2028<br />„Náš první právník nám dobře radil, že je potřeba co nejdřív zaregistrovat ochrannou známku. Ale kreativní nápady chyběly. Tak jsme použili generátor náhodných slov a vybrali si Indity,“ vzpomíná Denisa Hrubešová.\\u2028<br />O měsíc později přišla ze Španělska výzva: název Indity se až příliš podobal už existující značce. Denisa s Davidem se tak ocitli zpátky na začátku – a týden před spuštěním platformy neměli jméno. Nakonec se vymyslel název Pickey, který rovnou zaregistrovali. Ale ani ten se neosvědčil: „Sice se to dobře vyslovovalo, ale v podcastech se na něj špatně odkazovalo. Zpětně hodnotím, že jsme do toho měli narvat na začátku mnohem víc peněz – které jsme v té době ale neměli.“ </p>\"}]}',137),(3,1,3,'Lorem ipsum','Lorem ipsum','Lorem ipsum','','',0,1,0,0,0,'2025-05-28 12:59:00','2125-05-28 12:59:00',34,'2025-05-28 13:27:19','{}',NULL,'{\"content____6s8E_zTNkOorySYFWPzIh\":[{\"content\":\"<div>\\n<div><span>Lorem ipsum dolor sit amet consectetur adipisicing elit. Quisquam, quos.</span></div>\\n<div><span>Lorem ipsum dolor sit amet consectetur adipisicing elit. Quisquam, quos.</span></div>\\n</div>\"}]}',0);
/*!40000 ALTER TABLE `reference_localization` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `reference_tag`
--

DROP TABLE IF EXISTS `reference_tag`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `reference_tag` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `internalName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `reference_tag`
--

LOCK TABLES `reference_tag` WRITE;
/*!40000 ALTER TABLE `reference_tag` DISABLE KEYS */;
/*!40000 ALTER TABLE `reference_tag` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `reference_tag_localization`
--

DROP TABLE IF EXISTS `reference_tag_localization`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `reference_tag_localization` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `referenceTagId` int(11) NOT NULL,
  `mutationId` int(11) NOT NULL,
  `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `sort` int(11) NOT NULL DEFAULT '0',
  `nameAnchor` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `nameTitle` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `keywords` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `public` int(11) NOT NULL DEFAULT '0',
  `forceNoIndex` int(11) NOT NULL DEFAULT '0',
  `hideInSearch` int(11) NOT NULL DEFAULT '0',
  `hideInSitemap` int(11) NOT NULL DEFAULT '0',
  `edited` int(11) DEFAULT NULL,
  `editedTime` datetime DEFAULT NULL,
  `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `customContentJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `FK_blog_tag_mutation` (`mutationId`) USING BTREE,
  KEY `FK_blog_tag_localization_blog_tag` (`referenceTagId`),
  CONSTRAINT `reference_tag_localization_ibfk_2` FOREIGN KEY (`referenceTagId`) REFERENCES `reference_tag` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `reference_tag_localization_ibfk_3` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `reference_tag_localization`
--

LOCK TABLES `reference_tag_localization` WRITE;
/*!40000 ALTER TABLE `reference_tag_localization` DISABLE KEYS */;
/*!40000 ALTER TABLE `reference_tag_localization` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `reference_x_blog_tag`
--

DROP TABLE IF EXISTS `reference_x_blog_tag`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `reference_x_blog_tag` (
  `referenceId` int(11) NOT NULL,
  `blogTagId` int(11) NOT NULL,
  PRIMARY KEY (`referenceId`,`blogTagId`) USING BTREE,
  KEY `FK__blog` (`referenceId`) USING BTREE,
  KEY `FK__blog_tag` (`blogTagId`) USING BTREE,
  CONSTRAINT `reference_x_blog_tag_ibfk_1` FOREIGN KEY (`referenceId`) REFERENCES `reference` (`id`) ON DELETE CASCADE,
  CONSTRAINT `reference_x_blog_tag_ibfk_2` FOREIGN KEY (`blogTagId`) REFERENCES `blog_tag` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `reference_x_blog_tag`
--

LOCK TABLES `reference_x_blog_tag` WRITE;
/*!40000 ALTER TABLE `reference_x_blog_tag` DISABLE KEYS */;
INSERT INTO `reference_x_blog_tag` VALUES (1,5),(2,5);
/*!40000 ALTER TABLE `reference_x_blog_tag` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `reference_x_reference`
--

DROP TABLE IF EXISTS `reference_x_reference`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `reference_x_reference` (
  `referenceId` int(11) NOT NULL,
  `attachedReferenceId` int(11) NOT NULL,
  PRIMARY KEY (`referenceId`,`attachedReferenceId`) USING BTREE,
  KEY `FK_blog_x_blog_blog` (`referenceId`) USING BTREE,
  KEY `FK_blog_x_blog_blog_2` (`attachedReferenceId`) USING BTREE,
  CONSTRAINT `reference_x_reference_ibfk_3` FOREIGN KEY (`referenceId`) REFERENCES `reference` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `reference_x_reference_ibfk_4` FOREIGN KEY (`attachedReferenceId`) REFERENCES `reference` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `reference_x_reference`
--

LOCK TABLES `reference_x_reference` WRITE;
/*!40000 ALTER TABLE `reference_x_reference` DISABLE KEYS */;
/*!40000 ALTER TABLE `reference_x_reference` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `reference_x_reference_tag`
--

DROP TABLE IF EXISTS `reference_x_reference_tag`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `reference_x_reference_tag` (
  `referenceId` int(11) NOT NULL,
  `referenceTagId` int(11) NOT NULL,
  PRIMARY KEY (`referenceId`,`referenceTagId`) USING BTREE,
  KEY `FK__blog` (`referenceId`) USING BTREE,
  KEY `FK__blog_tag` (`referenceTagId`) USING BTREE,
  CONSTRAINT `reference_x_reference_tag_ibfk_1` FOREIGN KEY (`referenceId`) REFERENCES `reference` (`id`) ON DELETE CASCADE,
  CONSTRAINT `reference_x_reference_tag_ibfk_2` FOREIGN KEY (`referenceTagId`) REFERENCES `reference_tag` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `reference_x_reference_tag`
--

LOCK TABLES `reference_x_reference_tag` WRITE;
/*!40000 ALTER TABLE `reference_x_reference_tag` DISABLE KEYS */;
/*!40000 ALTER TABLE `reference_x_reference_tag` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `seolink`
--

DROP TABLE IF EXISTS `seolink`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `seolink` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `internalName` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `customFieldsJson` longtext COLLATE utf8mb4_unicode_520_ci,
  `parameterValuesIds` varchar(768) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `parameterValuesIds` (`parameterValuesIds`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `seolink`
--

LOCK TABLES `seolink` WRITE;
/*!40000 ALTER TABLE `seolink` DISABLE KEYS */;
/*!40000 ALTER TABLE `seolink` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `seolink_localization`
--

DROP TABLE IF EXISTS `seolink_localization`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `seolink_localization` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mutationId` int(11) NOT NULL DEFAULT '1',
  `seoLinkId` int(11) NOT NULL,
  `name` varchar(250) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `nameAnchor` varchar(250) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `nameTitle` varchar(250) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `description` text COLLATE utf8mb4_unicode_520_ci,
  `keywords` text COLLATE utf8mb4_unicode_520_ci,
  `title` varchar(250) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `customFieldsJson` longtext COLLATE utf8mb4_unicode_520_ci,
  `customContentJson` longtext COLLATE utf8mb4_unicode_520_ci,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `mutationId` (`mutationId`) USING BTREE,
  KEY `seoLinkId` (`seoLinkId`) USING BTREE,
  CONSTRAINT `seolink_localization_ibfk_1` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `seolink_localization_ibfk_2` FOREIGN KEY (`seoLinkId`) REFERENCES `seolink` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `seolink_localization`
--

LOCK TABLES `seolink_localization` WRITE;
/*!40000 ALTER TABLE `seolink_localization` DISABLE KEYS */;
/*!40000 ALTER TABLE `seolink_localization` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `seolink_x_parameter_value`
--

DROP TABLE IF EXISTS `seolink_x_parameter_value`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `seolink_x_parameter_value` (
  `seolinkId` int(11) NOT NULL,
  `parameterValueId` int(11) NOT NULL,
  PRIMARY KEY (`seolinkId`,`parameterValueId`) USING BTREE,
  KEY `seolinkId` (`seolinkId`) USING BTREE,
  KEY `parameterValueId` (`parameterValueId`) USING BTREE,
  CONSTRAINT `seolink_x_parameter_value_ibfk_1` FOREIGN KEY (`seolinkId`) REFERENCES `seolink` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `seolink_x_parameter_value_ibfk_2` FOREIGN KEY (`parameterValueId`) REFERENCES `parameter_value` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `seolink_x_parameter_value`
--

LOCK TABLES `seolink_x_parameter_value` WRITE;
/*!40000 ALTER TABLE `seolink_x_parameter_value` DISABLE KEYS */;
/*!40000 ALTER TABLE `seolink_x_parameter_value` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `state`
--

DROP TABLE IF EXISTS `state`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `state` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `public` tinyint(1) NOT NULL DEFAULT '1',
  `vatRates_standard` decimal(4,2) DEFAULT NULL,
  `vatRates_reduced` decimal(4,2) DEFAULT NULL,
  `vatRates_secondReduced` decimal(4,2) DEFAULT NULL,
  `vatRates_superReduced` decimal(4,2) DEFAULT NULL,
  `vatRates_parking` decimal(4,2) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=254 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `state`
--

LOCK TABLES `state` WRITE;
/*!40000 ALTER TABLE `state` DISABLE KEYS */;
INSERT INTO `state` VALUES (9,'AD','Andorra',1,21.00,15.00,10.00,NULL,NULL),(10,'AE','United Arab Emirates',1,21.00,15.00,10.00,NULL,NULL),(11,'AF','Afghanistan',1,21.00,15.00,10.00,NULL,NULL),(12,'AG','Antigua and Barbuda',1,21.00,15.00,10.00,NULL,NULL),(13,'AI','Anguilla',1,21.00,15.00,10.00,NULL,NULL),(14,'AL','Albania',1,21.00,15.00,10.00,NULL,NULL),(15,'AM','Armenia',1,21.00,15.00,10.00,NULL,NULL),(16,'AN','Netherlands Antilles',1,21.00,15.00,10.00,NULL,NULL),(17,'AO','Angola',1,21.00,15.00,10.00,NULL,NULL),(18,'AQ','Antarctica',1,21.00,15.00,10.00,NULL,NULL),(19,'AR','Argentina',1,21.00,15.00,10.00,NULL,NULL),(20,'AS','American Samoa',1,21.00,15.00,10.00,NULL,NULL),(21,'AT','Austria',1,21.00,15.00,10.00,NULL,NULL),(22,'AU','Australia',1,21.00,15.00,10.00,NULL,NULL),(23,'AW','Aruba',1,21.00,15.00,10.00,NULL,NULL),(24,'AZ','Azerbaijan',1,21.00,15.00,10.00,NULL,NULL),(25,'BA','Bosnia and Herzegovina',1,21.00,15.00,10.00,NULL,NULL),(26,'BB','Barbados',1,21.00,15.00,10.00,NULL,NULL),(27,'BD','Bangladesh',1,21.00,15.00,10.00,NULL,NULL),(28,'BE','Belgium',1,21.00,15.00,10.00,NULL,NULL),(29,'BF','Burkina Faso',1,21.00,15.00,10.00,NULL,NULL),(30,'BG','Bulgaria',1,21.00,15.00,10.00,NULL,NULL),(31,'BH','Bahrain',1,21.00,15.00,10.00,NULL,NULL),(32,'BI','Burundi',1,21.00,15.00,10.00,NULL,NULL),(33,'BJ','Benin',1,21.00,15.00,10.00,NULL,NULL),(34,'BM','Bermuda',1,21.00,15.00,10.00,NULL,NULL),(35,'BN','Brunei',1,21.00,15.00,10.00,NULL,NULL),(36,'BO','Bolivia',1,21.00,15.00,10.00,NULL,NULL),(37,'BR','Brazil',1,21.00,15.00,10.00,NULL,NULL),(38,'BS','Bahamas',1,21.00,15.00,10.00,NULL,NULL),(39,'BT','Bhutan',1,21.00,15.00,10.00,NULL,NULL),(40,'BV','Bouvet Island',1,21.00,15.00,10.00,NULL,NULL),(41,'BW','Botswana',1,21.00,15.00,10.00,NULL,NULL),(42,'BY','Belarus',1,21.00,15.00,10.00,NULL,NULL),(43,'BZ','Belize',1,21.00,15.00,10.00,NULL,NULL),(44,'CA','Canada',1,21.00,15.00,10.00,NULL,NULL),(45,'CC','Cocos [Keeling] Islands',1,21.00,15.00,10.00,NULL,NULL),(46,'CD','Congo [DRC]',1,21.00,15.00,10.00,NULL,NULL),(47,'CF','Central African Republic',1,21.00,15.00,10.00,NULL,NULL),(48,'CG','Congo [Republic]',1,21.00,15.00,10.00,NULL,NULL),(49,'CH','Switzerland',1,21.00,15.00,10.00,NULL,NULL),(50,'CI','Côte d\'Ivoire',1,21.00,15.00,10.00,NULL,NULL),(51,'CK','Cook Islands',1,21.00,15.00,10.00,NULL,NULL),(52,'CL','Chile',1,21.00,15.00,10.00,NULL,NULL),(53,'CM','Cameroon',1,21.00,15.00,10.00,NULL,NULL),(54,'CN','China',1,21.00,15.00,10.00,NULL,NULL),(55,'CO','Colombia',1,21.00,15.00,10.00,NULL,NULL),(56,'CR','Costa Rica',1,21.00,15.00,10.00,NULL,NULL),(57,'CU','Cuba',1,21.00,15.00,10.00,NULL,NULL),(58,'CV','Cape Verde',1,21.00,15.00,10.00,NULL,NULL),(59,'CX','Christmas Island',1,21.00,15.00,10.00,NULL,NULL),(60,'CY','Cyprus',1,21.00,15.00,10.00,NULL,NULL),(61,'CZ','Czech Republic',1,21.00,15.00,10.00,NULL,NULL),(62,'DE','Germany',1,21.00,15.00,10.00,NULL,NULL),(63,'DJ','Djibouti',1,21.00,15.00,10.00,NULL,NULL),(64,'DK','Denmark',1,21.00,15.00,10.00,NULL,NULL),(65,'DM','Dominica',1,21.00,15.00,10.00,NULL,NULL),(66,'DO','Dominican Republic',1,21.00,15.00,10.00,NULL,NULL),(67,'DZ','Algeria',1,21.00,15.00,10.00,NULL,NULL),(68,'EC','Ecuador',1,21.00,15.00,10.00,NULL,NULL),(69,'EE','Estonia',1,21.00,15.00,10.00,NULL,NULL),(70,'EG','Egypt',1,21.00,15.00,10.00,NULL,NULL),(71,'EH','Western Sahara',1,21.00,15.00,10.00,NULL,NULL),(72,'ER','Eritrea',1,21.00,15.00,10.00,NULL,NULL),(73,'ES','Spain',1,21.00,15.00,10.00,NULL,NULL),(74,'ET','Ethiopia',1,21.00,15.00,10.00,NULL,NULL),(75,'FI','Finland',1,21.00,15.00,10.00,NULL,NULL),(76,'FJ','Fiji',1,21.00,15.00,10.00,NULL,NULL),(77,'FK','Falkland Islands [Islas Malvinas]',1,21.00,15.00,10.00,NULL,NULL),(78,'FM','Micronesia',1,21.00,15.00,10.00,NULL,NULL),(79,'FO','Faroe Islands',1,21.00,15.00,10.00,NULL,NULL),(80,'FR','France',1,21.00,15.00,10.00,NULL,NULL),(81,'GA','Gabon',1,21.00,15.00,10.00,NULL,NULL),(82,'GB','United Kingdom',1,21.00,15.00,10.00,NULL,NULL),(83,'GD','Grenada',1,21.00,15.00,10.00,NULL,NULL),(84,'GE','Georgia',1,21.00,15.00,10.00,NULL,NULL),(85,'GF','French Guiana',1,21.00,15.00,10.00,NULL,NULL),(86,'GG','Guernsey',1,21.00,15.00,10.00,NULL,NULL),(87,'GH','Ghana',1,21.00,15.00,10.00,NULL,NULL),(88,'GI','Gibraltar',1,21.00,15.00,10.00,NULL,NULL),(89,'GL','Greenland',1,21.00,15.00,10.00,NULL,NULL),(90,'GM','Gambia',1,21.00,15.00,10.00,NULL,NULL),(91,'GN','Guinea',1,21.00,15.00,10.00,NULL,NULL),(92,'GP','Guadeloupe',1,21.00,15.00,10.00,NULL,NULL),(93,'GQ','Equatorial Guinea',1,21.00,15.00,10.00,NULL,NULL),(94,'GR','Greece',1,21.00,15.00,10.00,NULL,NULL),(95,'GS','South Georgia and the South Sandwich Islands',1,21.00,15.00,10.00,NULL,NULL),(96,'GT','Guatemala',1,21.00,15.00,10.00,NULL,NULL),(97,'GU','Guam',1,21.00,15.00,10.00,NULL,NULL),(98,'GW','Guinea-Bissau',1,21.00,15.00,10.00,NULL,NULL),(99,'GY','Guyana',1,21.00,15.00,10.00,NULL,NULL),(100,'GZ','Gaza Strip',1,21.00,15.00,10.00,NULL,NULL),(101,'HK','Hong Kong',1,21.00,15.00,10.00,NULL,NULL),(102,'HM','Heard Island and McDonald Islands',1,21.00,15.00,10.00,NULL,NULL),(103,'HN','Honduras',1,21.00,15.00,10.00,NULL,NULL),(104,'HR','Croatia',1,21.00,15.00,10.00,NULL,NULL),(105,'HT','Haiti',1,21.00,15.00,10.00,NULL,NULL),(106,'HU','Hungary',1,21.00,15.00,10.00,NULL,NULL),(107,'ID','Indonesia',1,21.00,15.00,10.00,NULL,NULL),(108,'IE','Ireland',1,21.00,15.00,10.00,NULL,NULL),(109,'IL','Israel',1,21.00,15.00,10.00,NULL,NULL),(110,'IM','Isle of Man',1,21.00,15.00,10.00,NULL,NULL),(111,'IN','India',1,21.00,15.00,10.00,NULL,NULL),(112,'IO','British Indian Ocean Territory',1,21.00,15.00,10.00,NULL,NULL),(113,'IQ','Iraq',1,21.00,15.00,10.00,NULL,NULL),(114,'IR','Iran',1,21.00,15.00,10.00,NULL,NULL),(115,'IS','Iceland',1,21.00,15.00,10.00,NULL,NULL),(116,'IT','Italy',1,21.00,15.00,10.00,NULL,NULL),(117,'JE','Jersey',1,21.00,15.00,10.00,NULL,NULL),(118,'JM','Jamaica',1,21.00,15.00,10.00,NULL,NULL),(119,'JO','Jordan',1,21.00,15.00,10.00,NULL,NULL),(120,'JP','Japan',1,21.00,15.00,10.00,NULL,NULL),(121,'KE','Kenya',1,21.00,15.00,10.00,NULL,NULL),(122,'KG','Kyrgyzstan',1,21.00,15.00,10.00,NULL,NULL),(123,'KH','Cambodia',1,21.00,15.00,10.00,NULL,NULL),(124,'KI','Kiribati',1,21.00,15.00,10.00,NULL,NULL),(125,'KM','Comoros',1,21.00,15.00,10.00,NULL,NULL),(126,'KN','Saint Kitts and Nevis',1,21.00,15.00,10.00,NULL,NULL),(127,'KP','North Korea',1,21.00,15.00,10.00,NULL,NULL),(128,'KR','South Korea',1,21.00,15.00,10.00,NULL,NULL),(129,'KW','Kuwait',1,21.00,15.00,10.00,NULL,NULL),(130,'KY','Cayman Islands',1,21.00,15.00,10.00,NULL,NULL),(131,'KZ','Kazakhstan',1,21.00,15.00,10.00,NULL,NULL),(132,'LA','Laos',1,21.00,15.00,10.00,NULL,NULL),(133,'LB','Lebanon',1,21.00,15.00,10.00,NULL,NULL),(134,'LC','Saint Lucia',1,21.00,15.00,10.00,NULL,NULL),(135,'LI','Liechtenstein',1,21.00,15.00,10.00,NULL,NULL),(136,'LK','Sri Lanka',1,21.00,15.00,10.00,NULL,NULL),(137,'LR','Liberia',1,21.00,15.00,10.00,NULL,NULL),(138,'LS','Lesotho',1,21.00,15.00,10.00,NULL,NULL),(139,'LT','Lithuania',1,21.00,15.00,10.00,NULL,NULL),(140,'LU','Luxembourg',1,21.00,15.00,10.00,NULL,NULL),(141,'LV','Latvia',1,21.00,15.00,10.00,NULL,NULL),(142,'LY','Libya',1,21.00,15.00,10.00,NULL,NULL),(143,'MA','Morocco',1,21.00,15.00,10.00,NULL,NULL),(144,'MC','Monaco',1,21.00,15.00,10.00,NULL,NULL),(145,'MD','Moldova',1,21.00,15.00,10.00,NULL,NULL),(146,'ME','Montenegro',1,21.00,15.00,10.00,NULL,NULL),(147,'MG','Madagascar',1,21.00,15.00,10.00,NULL,NULL),(148,'MH','Marshall Islands',1,21.00,15.00,10.00,NULL,NULL),(149,'MK','Macedonia [FYROM]',1,21.00,15.00,10.00,NULL,NULL),(150,'ML','Mali',1,21.00,15.00,10.00,NULL,NULL),(151,'MM','Myanmar [Burma]',1,21.00,15.00,10.00,NULL,NULL),(152,'MN','Mongolia',1,21.00,15.00,10.00,NULL,NULL),(153,'MO','Macau',1,21.00,15.00,10.00,NULL,NULL),(154,'MP','Northern Mariana Islands',1,21.00,15.00,10.00,NULL,NULL),(155,'MQ','Martinique',1,21.00,15.00,10.00,NULL,NULL),(156,'MR','Mauritania',1,21.00,15.00,10.00,NULL,NULL),(157,'MS','Montserrat',1,21.00,15.00,10.00,NULL,NULL),(158,'MT','Malta',1,21.00,15.00,10.00,NULL,NULL),(159,'MU','Mauritius',1,21.00,15.00,10.00,NULL,NULL),(160,'MV','Maldives',1,21.00,15.00,10.00,NULL,NULL),(161,'MW','Malawi',1,21.00,15.00,10.00,NULL,NULL),(162,'MX','Mexico',1,21.00,15.00,10.00,NULL,NULL),(163,'MY','Malaysia',1,21.00,15.00,10.00,NULL,NULL),(164,'MZ','Mozambique',1,21.00,15.00,10.00,NULL,NULL),(165,'NA','Namibia',1,21.00,15.00,10.00,NULL,NULL),(166,'NC','New Caledonia',1,21.00,15.00,10.00,NULL,NULL),(167,'NE','Niger',1,21.00,15.00,10.00,NULL,NULL),(168,'NF','Norfolk Island',1,21.00,15.00,10.00,NULL,NULL),(169,'NG','Nigeria',1,21.00,15.00,10.00,NULL,NULL),(170,'NI','Nicaragua',1,21.00,15.00,10.00,NULL,NULL),(171,'NL','Netherlands',1,21.00,15.00,10.00,NULL,NULL),(172,'NO','Norway',1,21.00,15.00,10.00,NULL,NULL),(173,'NP','Nepal',1,21.00,15.00,10.00,NULL,NULL),(174,'NR','Nauru',1,21.00,15.00,10.00,NULL,NULL),(175,'NU','Niue',1,21.00,15.00,10.00,NULL,NULL),(176,'NZ','New Zealand',1,21.00,15.00,10.00,NULL,NULL),(177,'OM','Oman',1,21.00,15.00,10.00,NULL,NULL),(178,'PA','Panama',1,21.00,15.00,10.00,NULL,NULL),(179,'PE','Peru',1,21.00,15.00,10.00,NULL,NULL),(180,'PF','French Polynesia',1,21.00,15.00,10.00,NULL,NULL),(181,'PG','Papua New Guinea',1,21.00,15.00,10.00,NULL,NULL),(182,'PH','Philippines',1,21.00,15.00,10.00,NULL,NULL),(183,'PK','Pakistan',1,21.00,15.00,10.00,NULL,NULL),(184,'PL','Poland',1,21.00,15.00,10.00,NULL,NULL),(185,'PM','Saint Pierre and Miquelon',1,21.00,15.00,10.00,NULL,NULL),(186,'PN','Pitcairn Islands',1,21.00,15.00,10.00,NULL,NULL),(187,'PR','Puerto Rico',1,21.00,15.00,10.00,NULL,NULL),(188,'PS','Palestinian Territories',1,21.00,15.00,10.00,NULL,NULL),(189,'PT','Portugal',1,21.00,15.00,10.00,NULL,NULL),(190,'PW','Palau',1,21.00,15.00,10.00,NULL,NULL),(191,'PY','Paraguay',1,21.00,15.00,10.00,NULL,NULL),(192,'QA','Qatar',1,21.00,15.00,10.00,NULL,NULL),(193,'RE','Réunion',1,21.00,15.00,10.00,NULL,NULL),(194,'RO','Romania',1,21.00,15.00,10.00,NULL,NULL),(195,'RS','Serbia',1,21.00,15.00,10.00,NULL,NULL),(196,'RU','Russia',1,21.00,15.00,10.00,NULL,NULL),(197,'RW','Rwanda',1,21.00,15.00,10.00,NULL,NULL),(198,'SA','Saudi Arabia',1,21.00,15.00,10.00,NULL,NULL),(199,'SB','Solomon Islands',1,21.00,15.00,10.00,NULL,NULL),(200,'SC','Seychelles',1,21.00,15.00,10.00,NULL,NULL),(201,'SD','Sudan',1,21.00,15.00,10.00,NULL,NULL),(202,'SE','Sweden',1,21.00,15.00,10.00,NULL,NULL),(203,'SG','Singapore',1,21.00,15.00,10.00,NULL,NULL),(204,'SH','Saint Helena',1,21.00,15.00,10.00,NULL,NULL),(205,'SI','Slovenia',1,21.00,15.00,10.00,NULL,NULL),(206,'SJ','Svalbard and Jan Mayen',1,21.00,15.00,10.00,NULL,NULL),(207,'SK','Slovakia',1,21.00,15.00,10.00,NULL,NULL),(208,'SL','Sierra Leone',1,21.00,15.00,10.00,NULL,NULL),(209,'SM','San Marino',1,21.00,15.00,10.00,NULL,NULL),(210,'SN','Senegal',1,21.00,15.00,10.00,NULL,NULL),(211,'SO','Somalia',1,21.00,15.00,10.00,NULL,NULL),(212,'SR','Suriname',1,21.00,15.00,10.00,NULL,NULL),(213,'ST','São Tomé and Príncipe',1,21.00,15.00,10.00,NULL,NULL),(214,'SV','El Salvador',1,21.00,15.00,10.00,NULL,NULL),(215,'SY','Syria',1,21.00,15.00,10.00,NULL,NULL),(216,'SZ','Swaziland',1,21.00,15.00,10.00,NULL,NULL),(217,'TC','Turks and Caicos Islands',1,21.00,15.00,10.00,NULL,NULL),(218,'TD','Chad',1,21.00,15.00,10.00,NULL,NULL),(219,'TF','French Southern Territories',1,21.00,15.00,10.00,NULL,NULL),(220,'TG','Togo',1,21.00,15.00,10.00,NULL,NULL),(221,'TH','Thailand',1,21.00,15.00,10.00,NULL,NULL),(222,'TJ','Tajikistan',1,21.00,15.00,10.00,NULL,NULL),(223,'TK','Tokelau',1,21.00,15.00,10.00,NULL,NULL),(224,'TL','Timor-Leste',1,21.00,15.00,10.00,NULL,NULL),(225,'TM','Turkmenistan',1,21.00,15.00,10.00,NULL,NULL),(226,'TN','Tunisia',1,21.00,15.00,10.00,NULL,NULL),(227,'TO','Tonga',1,21.00,15.00,10.00,NULL,NULL),(228,'TR','Turkey',1,21.00,15.00,10.00,NULL,NULL),(229,'TT','Trinidad and Tobago',1,21.00,15.00,10.00,NULL,NULL),(230,'TV','Tuvalu',1,21.00,15.00,10.00,NULL,NULL),(231,'TW','Taiwan',1,21.00,15.00,10.00,NULL,NULL),(232,'TZ','Tanzania',1,21.00,15.00,10.00,NULL,NULL),(233,'UA','Ukraine',1,21.00,15.00,10.00,NULL,NULL),(234,'UG','Uganda',1,21.00,15.00,10.00,NULL,NULL),(235,'UM','U.S. Minor Outlying Islands',1,21.00,15.00,10.00,NULL,NULL),(236,'US','United States',1,21.00,15.00,10.00,NULL,NULL),(237,'UY','Uruguay',1,21.00,15.00,10.00,NULL,NULL),(238,'UZ','Uzbekistan',1,21.00,15.00,10.00,NULL,NULL),(239,'VA','Vatican City',1,21.00,15.00,10.00,NULL,NULL),(240,'VC','Saint Vincent and the Grenadines',1,21.00,15.00,10.00,NULL,NULL),(241,'VE','Venezuela',1,21.00,15.00,10.00,NULL,NULL),(242,'VG','British Virgin Islands',1,21.00,15.00,10.00,NULL,NULL),(243,'VI','U.S. Virgin Islands',1,21.00,15.00,10.00,NULL,NULL),(244,'VN','Vietnam',1,21.00,15.00,10.00,NULL,NULL),(245,'VU','Vanuatu',1,21.00,15.00,10.00,NULL,NULL),(246,'WF','Wallis and Futuna',1,21.00,15.00,10.00,NULL,NULL),(247,'WS','Samoa',1,21.00,15.00,10.00,NULL,NULL),(248,'XK','Kosovo',1,21.00,15.00,10.00,NULL,NULL),(249,'YE','Yemen',1,21.00,15.00,10.00,NULL,NULL),(250,'YT','Mayotte',1,21.00,15.00,10.00,NULL,NULL),(251,'ZA','South Africa',1,21.00,15.00,10.00,NULL,NULL),(252,'ZM','Zambia',1,21.00,15.00,10.00,NULL,NULL),(253,'ZW','Zimbabwe',1,21.00,15.00,10.00,NULL,NULL);
/*!40000 ALTER TABLE `state` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `stock`
--

DROP TABLE IF EXISTS `stock`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `stock` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `alias` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `address` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `order` int(11) DEFAULT NULL,
  `deliveryHour` varchar(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '11:00',
  `extId` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `extId` (`extId`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `stock`
--

LOCK TABLES `stock` WRITE;
/*!40000 ALTER TABLE `stock` DISABLE KEYS */;
INSERT INTO `stock` VALUES (1,'prodejna','shop',NULL,NULL,'11:00',NULL),(2,'u dodavatele','supplier_store',NULL,NULL,'11:00',NULL);
/*!40000 ALTER TABLE `stock` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `stock_supplies`
--

DROP TABLE IF EXISTS `stock_supplies`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `stock_supplies` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `stockId` int(11) NOT NULL,
  `variantId` int(11) DEFAULT NULL,
  `amount` int(11) NOT NULL,
  `lastImport` datetime DEFAULT NULL,
  `deliveryDelay` int(11) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `stockId_variantId` (`stockId`,`variantId`),
  KEY `productVariantId` (`variantId`),
  KEY `stockId` (`stockId`),
  CONSTRAINT `stock_supplies_ibfk_3` FOREIGN KEY (`stockId`) REFERENCES `stock` (`id`) ON DELETE CASCADE,
  CONSTRAINT `stock_supplies_ibfk_4` FOREIGN KEY (`variantId`) REFERENCES `product_variant` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=1753 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `stock_supplies`
--

LOCK TABLES `stock_supplies` WRITE;
/*!40000 ALTER TABLE `stock_supplies` DISABLE KEYS */;
INSERT INTO `stock_supplies` VALUES (1715,1,44,3,NULL,0),(1716,2,44,10,NULL,0),(1717,1,50,10,NULL,0),(1718,2,50,100,NULL,0),(1719,1,48,0,NULL,0),(1720,2,48,0,NULL,0),(1721,1,46,0,NULL,0),(1722,2,46,0,NULL,0),(1723,1,47,0,NULL,0),(1724,2,47,0,NULL,0),(1725,1,51,0,NULL,0),(1726,2,51,0,NULL,0),(1727,1,52,15,NULL,0),(1728,2,52,0,NULL,0),(1731,1,53,0,NULL,0),(1732,2,53,0,NULL,0),(1737,1,56,0,NULL,0),(1738,2,56,0,NULL,0),(1739,1,59,0,NULL,0),(1740,2,59,0,NULL,0),(1743,1,57,10,NULL,0),(1744,2,57,10,NULL,0),(1745,1,58,0,NULL,0),(1746,2,58,0,NULL,0),(1747,1,61,0,NULL,0),(1748,2,61,0,NULL,0),(1749,1,62,0,NULL,0),(1750,2,62,0,NULL,0),(1751,1,63,0,NULL,0),(1752,2,63,0,NULL,0);
/*!40000 ALTER TABLE `stock_supplies` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `string`
--

DROP TABLE IF EXISTS `string`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `string` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `lg` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT 'cs',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `usedAt` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `lg_name` (`lg`,`name`),
  KEY `usedAt` (`usedAt`)
) ENGINE=InnoDB AUTO_INCREMENT=9402 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `string`
--

LOCK TABLES `string` WRITE;
/*!40000 ALTER TABLE `string` DISABLE KEYS */;
INSERT INTO `string` VALUES (3,'cs','title','Sedláková Legal',NULL),(4,'en','title','Sedláková Legal',NULL),(5,'cs','skip_main','##skip_main',NULL),(6,'en','skip_main','Přejít k obsahu',NULL),(7,'cs','skip_menu','##skip_menu',NULL),(8,'en','skip_menu','##skip_menu',NULL),(9,'cs','skip_search','##skip_search',NULL),(10,'en','skip_search','##skip_search',NULL),(11,'cs','logo','##logo',NULL),(12,'en','logo','##logo',NULL),(13,'cs','search_placeholder','Jak vám můžeme pomoci?',NULL),(14,'en','search_placeholder','##search_placeholder',NULL),(15,'cs','btn_search','Hledat',NULL),(16,'en','btn_search','##btn_search',NULL),(17,'cs','compare_title','Porovnání',NULL),(18,'en','compare_title','##compare_title',NULL),(19,'cs','login_title','Přihlášení',NULL),(20,'en','login_title','##login_title',NULL),(21,'cs','e-mail','##e-mail',NULL),(22,'en','e-mail','##e-mail',NULL),(23,'cs','email','E-mail',NULL),(24,'en','email','##email',NULL),(25,'cs','form_enter_username','##form_enter_username',NULL),(26,'en','form_enter_username','##form_enter_username',NULL),(27,'cs','heslo','##heslo',NULL),(28,'en','heslo','##heslo',NULL),(31,'cs','form_enter_password','##form_enter_password',NULL),(32,'en','form_enter_password','##form_enter_password',NULL),(33,'cs','link_lost_password','Zapomenuté heslo',NULL),(34,'en','link_lost_password','##link_lost_password',NULL),(35,'cs','link_registration','Registrace',NULL),(36,'en','link_registration','##link_registration',NULL),(37,'cs','btn_login','Přihlásit se',NULL),(38,'en','btn_login','##btn_login',NULL),(39,'cs','basket_empty','##basket_empty',NULL),(40,'en','basket_empty','##basket_empty',NULL),(41,'cs','basket_empty_title','##basket_empty_title',NULL),(42,'en','basket_empty_title','##basket_empty_title',NULL),(43,'cs','basket_empty_text','##basket_empty_text',NULL),(44,'en','basket_empty_text','##basket_empty_text',NULL),(45,'cs','please_rewrite_value','##please_rewrite_value',NULL),(46,'en','please_rewrite_value','##please_rewrite_value',NULL),(47,'cs','newsletter_title','novinky, akce a recenze do vaší pošty',NULL),(48,'en','newsletter_title','##newsletter_title',NULL),(49,'cs','enter_email','Zadejte e-mail',NULL),(50,'en','enter_email','##enter_email',NULL),(51,'cs','form_error','Chyba ve formuláři',NULL),(52,'en','form_error','##form_error',NULL),(53,'cs','copyright','SEDLAKOVA LEGAL s.r.o. Všechna práva vyhrazena.',NULL),(54,'en','copyright','##copyright',NULL),(55,'cs','profile_title','Můj účet',NULL),(56,'en','profile_title','##profile_title',NULL),(57,'cs','breadcrumb','##breadcrumb',NULL),(58,'en','breadcrumb','##breadcrumb',NULL),(59,'cs','breadcrumb_title','##breadcrumb_title',NULL),(60,'en','breadcrumb_title','##breadcrumb_title',NULL),(61,'cs','title_search_results','##title_search_results',NULL),(62,'en','title_search_results','##title_search_results',NULL),(63,'cs','search_results_categories','##search_results_categories',NULL),(64,'en','search_results_categories','##search_results_categories',NULL),(65,'cs','search_results_products','##search_results_products',NULL),(66,'en','search_results_products','##search_results_products',NULL),(67,'cs','search_results_blog','##search_results_blog',NULL),(68,'en','search_results_blog','##search_results_blog',NULL),(69,'cs','search_results_other','##search_results_other',NULL),(70,'en','search_results_other','##search_results_other',NULL),(71,'cs','search_empty_1','##search_empty_1',NULL),(72,'en','search_empty_1','##search_empty_1',NULL),(73,'cs','search_empty_2','##search_empty_2',NULL),(74,'en','search_empty_2','##search_empty_2',NULL),(75,'cs','contact_phone','##contact_phone',NULL),(76,'en','contact_phone','##contact_phone',NULL),(77,'cs','contact_hours','##contact_hours',NULL),(78,'en','contact_hours','##contact_hours',NULL),(79,'cs','filter_dial_color','##filter_dial_color',NULL),(80,'en','filter_dial_color','##filter_dial_color',NULL),(81,'cs','filter_flag_is_new','##filter_flag_is_new',NULL),(82,'en','filter_flag_is_new','##filter_flag_is_new',NULL),(83,'cs','filter_flag_is_old','##filter_flag_is_old',NULL),(84,'en','filter_flag_is_old','##filter_flag_is_old',NULL),(85,'cs','filter_flag_price_final_dph','##filter_flag_price_final_dph',NULL),(86,'en','filter_flag_price_final_dph','##filter_flag_price_final_dph',NULL),(87,'cs','filter_range_price_final_dph','##filter_range_price_final_dph',NULL),(88,'en','filter_range_price_final_dph','##filter_range_price_final_dph',NULL),(89,'cs','btn_filter_products','##btn_filter_products',NULL),(90,'en','btn_filter_products','##btn_filter_products',NULL),(91,'cs','is_new','##is_new',NULL),(92,'en','is_new','##is_new',NULL),(93,'cs','is_old','##is_old',NULL),(94,'en','is_old','##is_old',NULL),(95,'cs','filter_btn','##filter_btn',NULL),(96,'en','filter_btn','##filter_btn',NULL),(97,'cs','best_sellers','##best_sellers',NULL),(98,'en','best_sellers','##best_sellers',NULL),(99,'cs','latest','##latest',NULL),(100,'en','latest','##latest',NULL),(101,'cs','cheapest','##cheapest',NULL),(102,'en','cheapest','##cheapest',NULL),(103,'cs','most_expensive','##most_expensive',NULL),(104,'en','most_expensive','##most_expensive',NULL),(105,'cs','price_tax','s DPH',NULL),(106,'en','price_tax','##price_tax',NULL),(107,'cs','add_to_cart','##add_to_cart',NULL),(108,'en','add_to_cart','##add_to_cart',NULL),(109,'cs','blog_categories','##blog_categories',NULL),(110,'en','blog_categories','##blog_categories',NULL),(111,'cs','tags_title','##tags_title',NULL),(112,'en','tags_title','##tags_title',NULL),(113,'cs','product_flag_delivery','Doprava zdarma',NULL),(114,'en','product_flag_delivery','##product_flag_delivery',NULL),(115,'cs','stock_one_piece','Poslední kus',NULL),(116,'en','stock_one_piece','##stock_one_piece',NULL),(117,'cs','ihned-k-odberu','##ihned-k-odberu',NULL),(118,'en','ihned-k-odberu','##ihned-k-odberu',NULL),(119,'cs','stock_zero','Není skladem',NULL),(120,'en','stock_zero','##stock_zero',NULL),(121,'cs','btn_delivery_info','##btn_delivery_info',NULL),(122,'en','btn_delivery_info','##btn_delivery_info',NULL),(123,'cs','title_warranties','##title_warranties',NULL),(124,'en','title_warranties','##title_warranties',NULL),(125,'cs','title_special_services','##title_special_services',NULL),(126,'en','title_special_services','##title_special_services',NULL),(127,'cs','price_without_tax','bez DPH',NULL),(128,'en','price_without_tax','##price_without_tax',NULL),(129,'cs','count_title','##count_title',NULL),(130,'en','count_title','##count_title',NULL),(131,'cs','count_short','##count_short',NULL),(132,'en','count_short','##count_short',NULL),(133,'cs','product_id','##product_id',NULL),(134,'en','product_id','##product_id',NULL),(135,'cs','btn_compare_add','##btn_compare_add',NULL),(136,'en','btn_compare_add','##btn_compare_add',NULL),(137,'cs','title_parameters','Parametry',NULL),(138,'en','title_parameters','##title_parameters',NULL),(139,'cs','title_products','##title_products',NULL),(140,'en','title_products','##title_products',NULL),(141,'cs','contact_address_1','##contact_address_1',NULL),(142,'en','contact_address_1','##contact_address_1',NULL),(143,'cs','contact_address_2','##contact_address_2',NULL),(144,'en','contact_address_2','##contact_address_2',NULL),(145,'cs','contact_hours_long','##contact_hours_long',NULL),(146,'en','contact_hours_long','##contact_hours_long',NULL),(147,'cs','day_2','úterý',NULL),(148,'en','day_2','##day_2',NULL),(149,'cs','at_yours','##at_yours',NULL),(150,'en','at_yours','##at_yours',NULL),(151,'cs','day_3','středu',NULL),(152,'en','day_3','##day_3',NULL),(153,'cs','day_4','čtvrtek',NULL),(154,'en','day_4','##day_4',NULL),(155,'cs','free_delivery_treshold_logged','##free_delivery_treshold_logged',NULL),(156,'en','free_delivery_treshold_logged','##free_delivery_treshold_logged',NULL),(157,'cs','store_prague','##store_prague',NULL),(158,'en','store_prague','##store_prague',NULL),(159,'cs','store_map','##store_map',NULL),(160,'en','store_map','##store_map',NULL),(161,'cs','cards_accepted','##cards_accepted',NULL),(162,'en','cards_accepted','##cards_accepted',NULL),(163,'cs','free_parking','##free_parking',NULL),(164,'en','free_parking','##free_parking',NULL),(165,'cs','comment_form','##comment_form',NULL),(166,'en','comment_form','##comment_form',NULL),(167,'cs','form_valid_email','E-mailová adresa není platná.',NULL),(168,'en','form_valid_email','##form_valid_email',NULL),(169,'cs','message','##message',NULL),(170,'en','message','##message',NULL),(171,'cs','comment','##comment',NULL),(172,'en','comment','##comment',NULL),(173,'cs','btn_comment','##btn_comment',NULL),(174,'en','btn_comment','##btn_comment',NULL),(175,'cs','btn_last_visited','##btn_last_visited',NULL),(176,'en','btn_last_visited','##btn_last_visited',NULL),(177,'cs','btn_show_all','##btn_show_all',NULL),(178,'en','btn_show_all','##btn_show_all',NULL),(179,'cs','contact_form_title','##contact_form_title',NULL),(180,'en','contact_form_title','##contact_form_title',NULL),(181,'cs','contact_type_complaint','##contact_type_complaint',NULL),(182,'en','contact_type_complaint','##contact_type_complaint',NULL),(183,'cs','contact_type_cooperation','##contact_type_cooperation',NULL),(184,'en','contact_type_cooperation','##contact_type_cooperation',NULL),(185,'cs','contact_type_question','##contact_type_question',NULL),(186,'en','contact_type_question','##contact_type_question',NULL),(187,'cs','contact_type_acclaim','##contact_type_acclaim',NULL),(188,'en','contact_type_acclaim','##contact_type_acclaim',NULL),(189,'cs','contact_type_other','##contact_type_other',NULL),(190,'en','contact_type_other','##contact_type_other',NULL),(191,'cs','name','##name',NULL),(192,'en','name','##name',NULL),(193,'cs','surname','##surname',NULL),(194,'en','surname','##surname',NULL),(195,'cs','phone','##phone',NULL),(196,'en','phone','##phone',NULL),(197,'cs','text','##text',NULL),(198,'en','text','##text',NULL),(199,'cs','form_files','Soubory',NULL),(200,'en','form_files','##form_files',NULL),(201,'cs','select_file','##select_file',NULL),(202,'en','select_file','##select_file',NULL),(203,'cs','the-size-of-the-uploaded-file-can-be-up-to-d-bytes','##the-size-of-the-uploaded-file-can-be-up-to-d-bytes',NULL),(204,'en','the-size-of-the-uploaded-file-can-be-up-to-d-bytes','##the-size-of-the-uploaded-file-can-be-up-to-d-bytes',NULL),(205,'cs','form_agree','##form_agree',NULL),(206,'en','form_agree','##form_agree',NULL),(207,'cs','form_agree_text','##form_agree_text',NULL),(208,'en','form_agree_text','##form_agree_text',NULL),(209,'cs','form_btn_send','Odeslat formulář',NULL),(210,'en','form_btn_send','##form_btn_send',NULL),(211,'cs','message_antispam_error','##message_antispam_error',NULL),(212,'en','message_antispam_error','##message_antispam_error',NULL),(213,'cs','antispam_error','##antispam_error',NULL),(214,'en','antispam_error','##antispam_error',NULL),(215,'cs','error_voucher_bad_using_min_order','Slevový poukaz nelze uplatnit, minimální hodnota nákupu pro uplatnění slevy je %minOrderPrice%',NULL),(216,'cs','server_error_500_msg','Na našem serveru došlo k neočekávané chybě.<br>Mějte s námi strpení a zkuste to, prosím, znovu.',NULL),(217,'en','server_error_500_msg','We\'re sorry! The server encountered an internal error and was unable to complete your request. <br>Please try again later.',NULL),(218,'cs','title_personal_info','Osobní údaje',NULL),(219,'en','title_personal_info','##title_personal_info',NULL),(220,'cs','420','##420',NULL),(221,'en','420','##420',NULL),(222,'cs','421','##421',NULL),(223,'en','421','##421',NULL),(224,'cs','43','##43',NULL),(225,'en','43','##43',NULL),(226,'cs','49','##49',NULL),(227,'en','49','##49',NULL),(228,'cs','48','##48',NULL),(229,'en','48','##48',NULL),(230,'cs','39','##39',NULL),(231,'en','39','##39',NULL),(232,'cs','31','##31',NULL),(233,'en','31','##31',NULL),(234,'cs','44','##44',NULL),(235,'en','44','##44',NULL),(236,'cs','32','##32',NULL),(237,'en','32','##32',NULL),(238,'cs','385','##385',NULL),(239,'en','385','##385',NULL),(240,'cs','45','##45',NULL),(241,'en','45','##45',NULL),(242,'cs','372','##372',NULL),(243,'en','372','##372',NULL),(244,'cs','33','##33',NULL),(245,'en','33','##33',NULL),(246,'cs','353','##353',NULL),(247,'en','353','##353',NULL),(248,'cs','370','##370',NULL),(249,'en','370','##370',NULL),(250,'cs','371','##371',NULL),(251,'en','371','##371',NULL),(252,'cs','352','##352',NULL),(253,'en','352','##352',NULL),(254,'cs','36','##36',NULL),(255,'en','36','##36',NULL),(256,'cs','40','##40',NULL),(257,'en','40','##40',NULL),(258,'cs','386','##386',NULL),(259,'en','386','##386',NULL),(260,'cs','34','##34',NULL),(261,'en','34','##34',NULL),(262,'cs','street','##street',NULL),(263,'en','street','##street',NULL),(264,'cs','city','##city',NULL),(265,'en','city','##city',NULL),(266,'cs','zip','##zip',NULL),(267,'en','zip','##zip',NULL),(268,'cs','title_invoice_address','Fakturační adresa',NULL),(269,'en','title_invoice_address','##title_invoice_address',NULL),(270,'cs','state','##state',NULL),(271,'en','state','##state',NULL),(272,'cs','ceska-republika','##ceska-republika',NULL),(273,'en','ceska-republika','##ceska-republika',NULL),(274,'cs','slovensko','##slovensko',NULL),(275,'en','slovensko','##slovensko',NULL),(276,'cs','rakousko','##rakousko',NULL),(277,'en','rakousko','##rakousko',NULL),(278,'cs','nemecko','##nemecko',NULL),(279,'en','nemecko','##nemecko',NULL),(280,'cs','polsko','##polsko',NULL),(281,'en','polsko','##polsko',NULL),(282,'cs','italie','##italie',NULL),(283,'en','italie','##italie',NULL),(284,'cs','holandsko','##holandsko',NULL),(285,'en','holandsko','##holandsko',NULL),(286,'cs','anglie','##anglie',NULL),(287,'en','anglie','##anglie',NULL),(288,'cs','belgie','##belgie',NULL),(289,'en','belgie','##belgie',NULL),(290,'cs','chorvatsko','##chorvatsko',NULL),(291,'en','chorvatsko','##chorvatsko',NULL),(292,'cs','dansko','##dansko',NULL),(293,'en','dansko','Test',NULL),(294,'cs','estonsko','##estonsko',NULL),(295,'en','estonsko','##estonsko',NULL),(296,'cs','francie','##francie',NULL),(297,'en','francie','##francie',NULL),(298,'cs','irsko','##irsko',NULL),(299,'en','irsko','##irsko',NULL),(300,'cs','litva','##litva',NULL),(301,'en','litva','##litva',NULL),(302,'cs','lotyssko','##lotyssko',NULL),(303,'en','lotyssko','##lotyssko',NULL),(304,'cs','lucembursko','##lucembursko',NULL),(305,'en','lucembursko','##lucembursko',NULL),(306,'cs','madarsko','##madarsko',NULL),(307,'en','madarsko','##madarsko',NULL),(308,'cs','rumunsko','##rumunsko',NULL),(309,'en','rumunsko','##rumunsko',NULL),(310,'cs','slovinsko','##slovinsko',NULL),(311,'en','slovinsko','##slovinsko',NULL),(312,'cs','spanelsko','##spanelsko',NULL),(313,'en','spanelsko','##spanelsko',NULL),(314,'cs','company','##company',NULL),(315,'en','company','##company',NULL),(316,'cs','ic','##ic',NULL),(317,'en','ic','##ic',NULL),(320,'cs','title_delivery_address','Dodací adresa',NULL),(321,'en','title_delivery_address','##title_delivery_address',NULL),(322,'cs','add_address','Přidat další adresu',NULL),(323,'en','add_address','##add_address',NULL),(324,'cs','newsletter','##newsletter',NULL),(325,'en','newsletter','##newsletter',NULL),(326,'cs','btn_change','Uložit změny',NULL),(327,'en','btn_change','##btn_change',NULL),(328,'cs','title_password_change','Změnit heslo',NULL),(329,'en','title_password_change','##title_password_change',NULL),(332,'cs','form_password_not_same','Hesla se neshodují.',NULL),(333,'en','form_password_not_same','##form_password_not_same',NULL),(334,'cs','btn_change_password','Změnit heslo',NULL),(335,'en','btn_change_password','##btn_change_password',NULL),(7573,'cs','form_label_name','Jméno',NULL),(7574,'en','form_label_name','##form_label_name',NULL),(7575,'cs','form_label_surname','Příjmení',NULL),(7576,'en','form_label_surname','##form_label_surname',NULL),(7577,'cs','form_label_email','E-mail',NULL),(7578,'en','form_label_email','##form_label_email',NULL),(7579,'cs','form_label_phone','Telefon',NULL),(7580,'en','form_label_phone','##form_label_phone',NULL),(7581,'cs','form_label_text','Zpráva',NULL),(7582,'en','form_label_text','##form_label_text',NULL),(7583,'cs','form_label_file','Soubor',NULL),(7584,'en','form_label_file','##form_label_file',NULL),(7585,'cs','form_label_password','Heslo',NULL),(7586,'en','form_label_password','##form_label_password',NULL),(7587,'cs','user_logout','##user_logout',NULL),(7588,'en','user_logout','##user_logout',NULL),(7589,'cs','day_5','pátek',NULL),(7590,'en','day_5','##day_5',NULL),(7591,'cs','day_1','pondělí',NULL),(7592,'en','day_1','##day_1',NULL),(7593,'cs','form_label_message','Zpráva',NULL),(7594,'en','form_label_message','##form_label_message',NULL),(7595,'cs','free_delivery_treshold','##free_delivery_treshold',NULL),(7596,'en','free_delivery_treshold','##free_delivery_treshold',NULL),(7597,'cs','price_not_determined','##price_not_determined',NULL),(7598,'en','price_not_determined','##price_not_determined',NULL),(7599,'cs','availability_soldout','Vyprodáno',NULL),(7600,'en','availability_soldout','##availability_soldout',NULL),(7601,'cs','btn_watch_availability','Sledovat dostupnost',NULL),(7602,'en','btn_watch_availability','##btn_watch_availability',NULL),(7603,'cs','form_label_agree_text','Souhlasím se',NULL),(7604,'en','form_label_agree_text','##form_label_agree_text',NULL),(7605,'cs','form_label_agree_link','zpracováním osobních údajů',NULL),(7606,'en','form_label_agree_link','##form_label_agree_link',NULL),(7607,'cs','empty_filter_title','##empty_filter_title',NULL),(7608,'en','empty_filter_title','##empty_filter_title',NULL),(7609,'cs','title_files','Soubory ke stažení',NULL),(7610,'en','title_files','##title_files',NULL),(7611,'cs','title_links','##title_links',NULL),(7612,'en','title_links','##title_links',NULL),(7613,'cs','external_link','##external_link',NULL),(7614,'en','external_link','##external_link',NULL),(7615,'cs','title_images','##title_images',NULL),(7616,'en','title_images','##title_images',NULL),(7617,'cs','title_videos','Videa',NULL),(7618,'en','title_videos','##title_videos',NULL),(7619,'cs','basket_product_title','##basket_product_title',NULL),(7620,'en','basket_product_title','##basket_product_title',NULL),(7621,'cs','price_sum','##price_sum',NULL),(7622,'en','price_sum','##price_sum',NULL),(7623,'cs','continue','##continue',NULL),(7624,'en','continue','##continue',NULL),(7625,'cs','btn_basket','##btn_basket',NULL),(7626,'en','btn_basket','##btn_basket',NULL),(7627,'cs','title_prebasket','##title_prebasket',NULL),(7628,'en','title_prebasket','##title_prebasket',NULL),(7629,'cs','basket_added','##basket_added',NULL),(7630,'en','basket_added','##basket_added',NULL),(7631,'cs','btn_continue_shopping','##btn_continue_shopping',NULL),(7632,'en','btn_continue_shopping','##btn_continue_shopping',NULL),(7633,'cs','btn_enter_basket','##btn_enter_basket',NULL),(7634,'en','btn_enter_basket','##btn_enter_basket',NULL),(7635,'cs','shopping_basket','Nákupní košík',NULL),(7636,'en','shopping_basket','##shopping_basket',NULL),(7637,'cs','shipping_payment','Doprava a platba',NULL),(7638,'en','shipping_payment','##shipping_payment',NULL),(7639,'cs','personal_info','Doručovací údaje',NULL),(7640,'en','personal_info','##personal_info',NULL),(7641,'cs','order_success','Odeslání',NULL),(7642,'en','order_success','##order_success',NULL),(7643,'cs','title_product_buy','##title_product_buy',NULL),(7644,'en','title_product_buy','##title_product_buy',NULL),(7645,'cs','availability','Dostupnost',NULL),(7646,'en','availability','##availability',NULL),(7647,'cs','amount','##amount',NULL),(7648,'en','amount','##amount',NULL),(7649,'cs','unit_price_vat','##unit_price_vat',NULL),(7650,'en','unit_price_vat','##unit_price_vat',NULL),(7651,'cs','total_price_vat','Cena celkem',NULL),(7652,'en','total_price_vat','##total_price_vat',NULL),(7653,'cs','not_available','Nedostupný',NULL),(7654,'en','not_available','##not_available',NULL),(7655,'cs','title_voucher','##title_voucher',NULL),(7656,'en','title_voucher','##title_voucher',NULL),(7657,'cs','title_have_coupon','##title_have_coupon',NULL),(7658,'en','title_have_coupon','##title_have_coupon',NULL),(7659,'cs','title_coupon','##title_coupon',NULL),(7660,'en','title_coupon','##title_coupon',NULL),(7661,'cs','use','##use',NULL),(7662,'en','use','##use',NULL),(7663,'cs','total_price','Cena celkem bez DPH',NULL),(7664,'en','total_price','##total_price',NULL),(7665,'cs','btn_back_to_eshop','##btn_back_to_eshop',NULL),(7666,'en','btn_back_to_eshop','##btn_back_to_eshop',NULL),(7667,'cs','continue2','##continue2',NULL),(7668,'en','continue2','##continue2',NULL),(7669,'cs','btn_order_in_eshop','##btn_order_in_eshop',NULL),(7670,'en','btn_order_in_eshop','##btn_order_in_eshop',NULL),(7671,'cs','title_remove_from_basket','##title_remove_from_basket',NULL),(7672,'en','title_remove_from_basket','##title_remove_from_basket',NULL),(7673,'cs','btn_cancel_delete','##btn_cancel_delete',NULL),(7674,'en','btn_cancel_delete','##btn_cancel_delete',NULL),(7675,'cs','btn_allow_delete','##btn_allow_delete',NULL),(7676,'en','btn_allow_delete','##btn_allow_delete',NULL),(7677,'cs','title_transport_form','##title_transport_form',NULL),(7678,'en','title_transport_form','##title_transport_form',NULL),(7679,'cs','delivery_expected','##delivery_expected',NULL),(7680,'en','delivery_expected','##delivery_expected',NULL),(7681,'cs','filter_title_price','##filter_title_price',NULL),(7682,'en','filter_title_price','##filter_title_price',NULL),(7683,'cs','free','zdarma',NULL),(7684,'en','free','##free',NULL),(7685,'cs','title_payment_form','##title_payment_form',NULL),(7686,'en','title_payment_form','##title_payment_form',NULL),(7687,'cs','opening_hours','##opening_hours',NULL),(7688,'en','opening_hours','##opening_hours',NULL),(7689,'cs','btn_back','Zpět',NULL),(7690,'en','btn_back','##btn_back',NULL),(7691,'cs','order_sum_products_title','Shrnutí objednávky',NULL),(7692,'en','order_sum_products_title','##order_sum_products_title',NULL),(7693,'cs','ppl_modal_desc','##ppl_modal_desc',NULL),(7694,'en','ppl_modal_desc','##ppl_modal_desc',NULL),(7695,'cs','close_window','##close_window',NULL),(7696,'en','close_window','##close_window',NULL),(7697,'cs','post_modal_desc','##post_modal_desc',NULL),(7698,'en','post_modal_desc','##post_modal_desc',NULL),(7699,'cs','personal_modal_desc','##personal_modal_desc',NULL),(7700,'en','personal_modal_desc','##personal_modal_desc',NULL),(7701,'cs','on_delivery_modal_desc','##on_delivery_modal_desc',NULL),(7702,'en','on_delivery_modal_desc','##on_delivery_modal_desc',NULL),(7703,'cs','online_modal_desc','##online_modal_desc',NULL),(7704,'en','online_modal_desc','##online_modal_desc',NULL),(7705,'cs','cash_modal_desc','##cash_modal_desc',NULL),(7706,'en','cash_modal_desc','##cash_modal_desc',NULL),(7707,'cs','choose_billing_info','##choose_billing_info',NULL),(7708,'en','choose_billing_info','##choose_billing_info',NULL),(7709,'cs','form_label_street','Ulice',NULL),(7710,'en','form_label_street','##form_label_street',NULL),(7711,'cs','form_label_city','Město',NULL),(7712,'en','form_label_city','##form_label_city',NULL),(7713,'cs','form_label_zip','PSČ',NULL),(7714,'en','form_label_zip','##form_label_zip',NULL),(7715,'cs','note','##note',NULL),(7716,'en','note','##note',NULL),(7717,'cs','title_company_form','Vyplnit firemní údaje',NULL),(7718,'en','title_company_form','##title_company_form',NULL),(7719,'cs','form_label_company','Firma',NULL),(7720,'en','form_label_company','##form_label_company',NULL),(7721,'cs','form_label_ic','IČO',NULL),(7722,'en','form_label_ic','##form_label_ic',NULL),(7723,'cs','form_label_dic','DIČ',NULL),(7724,'en','form_label_dic','##form_label_dic',NULL),(7725,'cs','title_delivery_form','Jiná dodací adresa',NULL),(7726,'en','title_delivery_form','##title_delivery_form',NULL),(7727,'cs','form_label_state','Stát',NULL),(7728,'en','form_label_state','##form_label_state',NULL),(7729,'cs','form_label_info','##form_label_info',NULL),(7730,'en','form_label_info','##form_label_info',NULL),(7731,'cs','title_questionary_form','##title_questionary_form',NULL),(7732,'en','title_questionary_form','##title_questionary_form',NULL),(7733,'cs','order_agree_1','##order_agree_1',NULL),(7734,'en','order_agree_1','##order_agree_1',NULL),(7735,'cs','order_agree_2','##order_agree_2',NULL),(7736,'en','order_agree_2','##order_agree_2',NULL),(7737,'cs','order_agree_3','##order_agree_3',NULL),(7738,'en','order_agree_3','##order_agree_3',NULL),(7739,'cs','order_agree_4','##order_agree_4',NULL),(7740,'en','order_agree_4','##order_agree_4',NULL),(7741,'cs','btn_send_order','Odeslat objednávku',NULL),(7742,'en','btn_send_order','##btn_send_order',NULL),(7743,'cs','delivery','Doprava',NULL),(7744,'en','delivery','##delivery',NULL),(7745,'cs','payment','Platba',NULL),(7746,'en','payment','##payment',NULL),(7747,'cs','claim','##claim',NULL),(7748,'en','claim','##claim',NULL),(7749,'cs','footer_copyright','##footer_copyright',NULL),(7750,'en','footer_copyright','##footer_copyright',NULL),(7751,'cs','na-dotaz','##na-dotaz',NULL),(7752,'en','na-dotaz','##na-dotaz',NULL),(7753,'cs','order_sum_title','##order_sum_title',NULL),(7754,'en','order_sum_title','##order_sum_title',NULL),(7755,'cs','title_personal_recap','##title_personal_recap',NULL),(7756,'en','title_personal_recap','##title_personal_recap',NULL),(7757,'cs','address','##address',NULL),(7758,'en','address','##address',NULL),(7759,'cs','czech','##czech',NULL),(7760,'en','czech','##czech',NULL),(7761,'cs','title_order_number','##title_order_number',NULL),(7762,'en','title_order_number','##title_order_number',NULL),(7763,'cs','btn_watch_order','##btn_watch_order',NULL),(7764,'en','btn_watch_order','##btn_watch_order',NULL),(7765,'cs','title_order_social_1','##title_order_social_1',NULL),(7766,'en','title_order_social_1','##title_order_social_1',NULL),(7767,'cs','title_order_social_2','##title_order_social_2',NULL),(7768,'en','title_order_social_2','##title_order_social_2',NULL),(7769,'cs','please-enter-no-more-than-d-characters','##please-enter-no-more-than-d-characters',NULL),(7770,'en','please-enter-no-more-than-d-characters','##please-enter-no-more-than-d-characters',NULL),(7771,'cs','remove_address','##remove_address',NULL),(7772,'en','remove_address','##remove_address',NULL),(7775,'cs','form_label_newsletter','Chci dostávat novinky',NULL),(7776,'en','form_label_newsletter','I want to receive newsletters',NULL),(7777,'cs','form_label_password2','Heslo znovu',NULL),(7778,'en','form_label_password2','##form_label_password2',NULL),(7779,'cs','e-mail-is-required','##e-mail-is-required',NULL),(7780,'en','e-mail-is-required','##e-mail-is-required',NULL),(7781,'cs','registration_agree','Registrací souhlasíte se',NULL),(7782,'en','registration_agree','##registration_agree',NULL),(7783,'cs','sign_link','##sign_link',NULL),(7784,'en','sign_link','##sign_link',NULL),(7785,'cs','btn_register','Registrovat',NULL),(7786,'en','btn_register','##btn_register',NULL),(7787,'cs','title_registration','##title_registration',NULL),(7788,'en','title_registration','##title_registration',NULL),(7789,'cs','login_sale','##login_sale',NULL),(7790,'en','login_sale','##login_sale',NULL),(7791,'cs','login_news','##login_news',NULL),(7792,'en','login_news','##login_news',NULL),(7793,'cs','login_shipping','##login_shipping',NULL),(7794,'en','login_shipping','##login_shipping',NULL),(7795,'cs','login_summary','##login_summary',NULL),(7796,'en','login_summary','##login_summary',NULL),(7797,'cs','login_better','##login_better',NULL),(7798,'en','login_better','##login_better',NULL),(7803,'cs','form_profil_ok','Změny byly uloženy.',NULL),(7804,'en','form_profil_ok','##form_profil_ok',NULL),(7805,'cs','registration_email','E-mail',NULL),(7806,'en','registration_email','##registration_email',NULL),(7807,'cs','btn_lost_pwd','Odeslat',NULL),(7808,'en','btn_lost_pwd','##btn_lost_pwd',NULL),(7809,'cs','registration_agree_personal_data_link','zpracováním osobních údajů',NULL),(7810,'en','registration_agree_personal_data_link','##registration_agree_personal_data_link',NULL),(7811,'cs','form_send_reset_password','Na Váš e-mail byl odeslán odkaz pro nastavení nového hesla.',NULL),(7812,'en','form_send_reset_password','##form_send_reset_password',NULL),(7813,'cs','btn_save','Uložit',NULL),(7814,'en','btn_save','##btn_save',NULL),(7815,'cs','btn_create_account','##btn_create_account',NULL),(7816,'en','btn_create_account','##btn_create_account',NULL),(7817,'cs','filter_dial_select','##filter_dial_select',NULL),(7818,'en','filter_dial_select','##filter_dial_select',NULL),(7819,'cs','pname_113','Select CZ',NULL),(7820,'en','pname_113','Select',NULL),(7821,'cs','pvalue_6564','Hodnota 1',NULL),(7822,'en','pvalue_6564','Hodnota 1',NULL),(7823,'cs','pvalue_6565','Hodnota 2',NULL),(7824,'en','pvalue_6565','Hodnota 2',NULL),(7825,'cs','pvalue_6566','Hodnota 3',NULL),(7826,'en','pvalue_6566','Hodnota 3',NULL),(7827,'cs','filter_btn_remove','##filter_btn_remove',NULL),(7828,'en','filter_btn_remove','##filter_btn_remove',NULL),(7829,'cs','filter_cancel','##filter_cancel',NULL),(7830,'en','filter_cancel','##filter_cancel',NULL),(7831,'cs','pvalue_alias_6564','hodnota-1',NULL),(7832,'en','pvalue_alias_6564','hodnota-1',NULL),(7833,'cs','pvalue_alias_6565','hodnota-2',NULL),(7834,'en','pvalue_alias_6565','hodnota-2',NULL),(7835,'cs','pvalue_alias_6566','hodnota-3',NULL),(7836,'en','pvalue_alias_6566','hodnota-3',NULL),(7837,'cs','pvalue_6567','Hodnota 1',NULL),(7838,'en','pvalue_6567','Hodnota 1',NULL),(7839,'cs','pvalue_alias_6567','hodnota-1',NULL),(7840,'en','pvalue_alias_6567','hodnota-1',NULL),(7841,'cs','pvalue_6570','Hodnota 2',NULL),(7842,'en','pvalue_6570','Hodnota 2',NULL),(7843,'cs','pvalue_alias_6570','hodnota-2',NULL),(7844,'en','pvalue_alias_6570','hodnota-2',NULL),(7845,'cs','pvalue_6571','Hodnota 3',NULL),(7846,'en','pvalue_6571','Hodnota 3',NULL),(7847,'cs','pvalue_alias_6571','hodnota-3',NULL),(7848,'en','pvalue_alias_6571','hodnota-3',NULL),(7849,'cs','pname_114','Multiselect',NULL),(7850,'en','pname_114','Multiselect',NULL),(7851,'cs','catalog_seo_filter_and','a',NULL),(7852,'en','catalog_seo_filter_and','and',NULL),(7853,'cs','pname_tooltip_113','Popisek',NULL),(7854,'en','pname_tooltip_113','',NULL),(7855,'cs','pvalue_filter_6564','Hodnota 1 (Název ve filtru)',NULL),(7856,'en','pvalue_filter_6564','',NULL),(7857,'cs','pname_119','Číslo',NULL),(7858,'en','pname_119','Číslo',NULL),(7859,'cs','pname_tooltip_119','_',NULL),(7860,'en','pname_tooltip_119','_',NULL),(7861,'cs','pname_unit_119','Px',NULL),(7862,'en','pname_unit_119','',NULL),(7863,'cs','bad_login','Neplatný e-mail a/nebo heslo.',NULL),(7864,'en','bad_login','##bad_login',NULL),(7865,'cs','pname_unit_113','',NULL),(7866,'en','pname_unit_113','',NULL),(7867,'cs','mainbasket_empty_title','##mainbasket_empty_title',NULL),(7868,'en','mainbasket_empty_title','##mainbasket_empty_title',NULL),(7869,'cs','mainbasket_empty_text','##mainbasket_empty_text',NULL),(7870,'en','mainbasket_empty_text','##mainbasket_empty_text',NULL),(7871,'cs','mainbasket_empty_look','##mainbasket_empty_look',NULL),(7872,'en','mainbasket_empty_look','##mainbasket_empty_look',NULL),(7873,'cs','eshop','##eshop',NULL),(7874,'en','eshop','##eshop',NULL),(7875,'cs','title_last_blog_article','##title_last_blog_article',NULL),(7876,'en','title_last_blog_article','##title_last_blog_article',NULL),(7877,'cs','mail_exist_register','##mail_exist_register',NULL),(7878,'en','mail_exist_register','##mail_exist_register',NULL),(7879,'cs','flag_price_final_dph','##flag_price_final_dph',NULL),(7880,'en','flag_price_final_dph','##flag_price_final_dph',NULL),(7881,'cs','pvalue_filter_6565','',NULL),(7882,'en','pvalue_filter_6565','',NULL),(7883,'cs','pvalue_filter_6566','',NULL),(7884,'en','pvalue_filter_6566','',NULL),(7885,'cs','yes','##yes',NULL),(7886,'en','yes','##yes',NULL),(7887,'cs','message_error_step1','##message_error_step1',NULL),(7888,'en','message_error_step1','##message_error_step1',NULL),(7889,'cs','message_error_bad_combinaton','##message_error_bad_combinaton',NULL),(7890,'en','message_error_bad_combinaton','##message_error_bad_combinaton',NULL),(7891,'cs','germany','##germany',NULL),(7892,'en','germany','##germany',NULL),(7893,'cs','company_title','##company_title',NULL),(7894,'en','company_title','##company_title',NULL),(7895,'cs','dic','##dic',NULL),(7896,'en','dic','##dic',NULL),(7897,'cs','delivery_title','##delivery_title',NULL),(7898,'en','delivery_title','##delivery_title',NULL),(7899,'cs','title_company_recap','##title_company_recap',NULL),(7900,'en','title_company_recap','##title_company_recap',NULL),(7901,'cs','title_delivery_recap','##title_delivery_recap',NULL),(7902,'en','title_delivery_recap','##title_delivery_recap',NULL),(7903,'cs','error','##error',NULL),(7904,'en','error','##error',NULL),(7905,'cs','form_password_empty','##form_password_empty',NULL),(7906,'en','form_password_empty','##form_password_empty',NULL),(7907,'cs','error_voucher_bad','##error_voucher_bad',NULL),(7908,'en','error_voucher_bad','##error_voucher_bad',NULL),(7909,'cs','form_label_firstname','Jméno',NULL),(7910,'en','form_label_firstname','##form_label_firstname',NULL),(7911,'cs','form_label_lastname','Příjmení',NULL),(7912,'en','form_label_lastname','##form_label_lastname',NULL),(7913,'cs','availability_on_request','Na dotaz',NULL),(7914,'en','availability_on_request','##availability_on_request',NULL),(7915,'cs','stock_over_10','Více než 10',NULL),(7916,'en','stock_over_10','##stock_over_10',NULL),(7917,'cs','delivery_date_tomorrow','zítra',NULL),(7918,'en','delivery_date_tomorrow','##delivery_date_tomorrow',NULL),(7919,'cs','delivery_date_at_yours','u vás',NULL),(7920,'en','delivery_date_at_yours','##delivery_date_at_yours',NULL),(7921,'cs','form_profile_password_changed','Vaše heslo bylo změněno.',NULL),(7922,'en','form_profile_password_changed','##form_profile_password_changed',NULL),(7923,'cs','delivery_date_today','již dnes',NULL),(7924,'en','delivery_date_today','##delivery_date_today',NULL),(7925,'cs','order_status_progress','##order_status_progress',NULL),(7926,'en','order_status_progress','##order_status_progress',NULL),(7927,'cs','pvalue_filter_6567','',NULL),(7928,'en','pvalue_filter_6567','',NULL),(7929,'cs','pvalue_filter_6570','',NULL),(7930,'en','pvalue_filter_6570','',NULL),(7931,'cs','pvalue_filter_6571','',NULL),(7932,'en','pvalue_filter_6571','',NULL),(7933,'cs','pname_tooltip_114','',NULL),(7934,'en','pname_tooltip_114','',NULL),(7935,'cs','pname_unit_114','',NULL),(7936,'en','pname_unit_114','',NULL),(7937,'cs','title_company_info','##title_company_info',NULL),(7938,'en','title_company_info','##title_company_info',NULL),(7939,'cs','order_status_new','##order_status_new',NULL),(7940,'en','order_status_new','##order_status_new',NULL),(7941,'cs','form_reset_password','Vaše heslo bylo nastaveno.',NULL),(7942,'cs','reset_password_expired_link','Odkaz pro reset hesla není platný',NULL),(7943,'en','reset_password_expired_link','Password reset link is not valid',NULL),(8346,'cs','availability_in_stock','Skladem',NULL),(8347,'en','availability_in_stock','##availability_in_stock',NULL),(8348,'cs','stock_name_alias_shop','Na prodejně',NULL),(8349,'en','stock_name_alias_shop','##stock_name_alias_shop',NULL),(8350,'cs','stock_name_alias_supplier_store','U dodavatele',NULL),(8351,'en','stock_name_alias_supplier_store','##stock_name_alias_supplier_store',NULL),(8352,'cs','title_delivery_info','Doručení',NULL),(8353,'en','title_delivery_info','##title_delivery_info',NULL),(8354,'cs','stock_piece','ks',NULL),(8355,'en','stock_piece','##stock_piece',NULL),(8356,'cs','btn_remove_address','##btn_remove_address',NULL),(8357,'en','btn_remove_address','##btn_remove_address',NULL),(8358,'cs','delivery_date_at','v',NULL),(8359,'en','delivery_date_at','##delivery_date_at',NULL),(8360,'cs','stock_over_2','Více než 2',NULL),(8361,'en','stock_over_2','##stock_over_2',NULL),(8362,'cs','stock_over_5','Více než 5',NULL),(8363,'en','stock_over_5','##stock_over_5',NULL),(8364,'cs','filter_range_price','Cena',NULL),(8365,'en','filter_range_price','##filter_range_price',NULL),(8366,'cs','price','##price',NULL),(8367,'en','price','##price',NULL),(8416,'cs','pvalue_6572','10',NULL),(8417,'en','pvalue_6572','10',NULL),(8418,'cs','pvalue_alias_6572','6572',NULL),(8419,'en','pvalue_alias_6572','6572',NULL),(8420,'cs','pvalue_filter_6572','',NULL),(8421,'en','pvalue_filter_6572','',NULL),(8422,'cs','pvalue_6573','20',NULL),(8423,'en','pvalue_6573','20',NULL),(8424,'cs','pvalue_alias_6573','6573',NULL),(8425,'en','pvalue_alias_6573','6573',NULL),(8426,'cs','pvalue_filter_6573','',NULL),(8427,'en','pvalue_filter_6573','',NULL),(8428,'cs','pvalue_6574','88',NULL),(8429,'en','pvalue_6574','88',NULL),(8430,'cs','pvalue_alias_6574','6574',NULL),(8431,'en','pvalue_alias_6574','6574',NULL),(8432,'cs','pvalue_filter_6574','',NULL),(8433,'en','pvalue_filter_6574','',NULL),(8434,'cs','pvalue_6575','120',NULL),(8435,'en','pvalue_6575','120',NULL),(8436,'cs','pvalue_alias_6575','6575',NULL),(8437,'en','pvalue_alias_6575','6575',NULL),(8438,'cs','pvalue_filter_6575','',NULL),(8439,'en','pvalue_filter_6575','',NULL),(8440,'cs','pvalue_6576','2',NULL),(8441,'en','pvalue_6576','2',NULL),(8442,'cs','pvalue_alias_6576','6576',NULL),(8443,'en','pvalue_alias_6576','6576',NULL),(8444,'cs','pvalue_filter_6576','',NULL),(8445,'en','pvalue_filter_6576','',NULL),(8482,'cs','filter_dial_cislo','##filter_dial_cislo',NULL),(8483,'en','filter_dial_cislo','##filter_dial_cislo',NULL),(8484,'cs','filter_dial_multiselect','##filter_dial_multiselect',NULL),(8485,'en','filter_dial_multiselect','##filter_dial_multiselect',NULL),(8486,'cs','help','##help',NULL),(8487,'en','help','##help',NULL),(8584,'cs','filter_range_cislo','##filter_range_cislo',NULL),(8585,'en','filter_range_cislo','##filter_range_cislo',NULL),(8586,'cs','od','##od',NULL),(8587,'en','od','##od',NULL),(8592,'cs','form_label_add_address','Přidat adresu',NULL),(8593,'en','form_label_add_address','##form_label_add_address',NULL),(8594,'cs','form_label_remove_address','Odebrat adresu',NULL),(8595,'en','form_label_remove_address','##form_label_remove_address',NULL),(8596,'cs','delivery_date_at_inf','ve',NULL),(8597,'en','delivery_date_at_inf','##delivery_date_at_inf',NULL),(8598,'cs','attached_pages_title','##attached_pages_title',NULL),(8599,'en','attached_pages_title','##attached_pages_title',NULL),(8600,'cs','title_product_main_category','Hlavní kategorie',NULL),(8601,'en','title_product_main_category','##title_product_main_category',NULL),(8602,'cs','title_product_all_variants','Ostatní varianty',NULL),(8603,'en','title_product_all_variants','##title_product_all_variants',NULL),(8604,'cs','stock_two_pieces','Poslední 2 kusy',NULL),(8605,'en','stock_two_pieces','##stock_two_pieces',NULL),(8630,'cs','pname_116','pname_116',NULL),(8631,'en','pname_116','pname_116',NULL),(8632,'cs','pname_tooltip_116','pname_tooltip_116',NULL),(8633,'en','pname_tooltip_116','pname_tooltip_116',NULL),(8634,'cs','pname_unit_116','pname_unit_116',NULL),(8635,'en','pname_unit_116','pname_unit_116',NULL),(8696,'cs','product_ean','EAN',NULL),(8697,'en','product_ean','##product_ean',NULL),(8698,'cs','product_code','Kód produktu',NULL),(8699,'en','product_code','##product_code',NULL),(8700,'cs','filter_flag_is_new_unit','##filter_flag_is_new_unit',NULL),(8701,'en','filter_flag_is_new_unit','##filter_flag_is_new_unit',NULL),(8702,'cs','filter_flag_is_new_description','##filter_flag_is_new_description',NULL),(8703,'en','filter_flag_is_new_description','##filter_flag_is_new_description',NULL),(8704,'cs','filter_flag_is_old_unit','##filter_flag_is_old_unit',NULL),(8705,'en','filter_flag_is_old_unit','##filter_flag_is_old_unit',NULL),(8706,'cs','filter_flag_is_old_description','##filter_flag_is_old_description',NULL),(8707,'en','filter_flag_is_old_description','##filter_flag_is_old_description',NULL),(8710,'cs','filter_range_price_unit','v Kč',NULL),(8711,'en','filter_range_price_unit','##filter_range_price_unit',NULL),(8712,'cs','filter_range_price_description','Zvolte rozmezí ceny hledaného produktu',NULL),(8714,'en','filter_range_price_description','##filter_range_price_description',NULL),(8716,'cs','btn_more_values','##btn_more_values',NULL),(8717,'en','btn_more_values','##btn_more_values',NULL),(8718,'cs','btn_filter','Filtrovat',NULL),(8719,'en','btn_filter','##btn_filter',NULL),(8722,'cs','sort_cheapest','##sort_cheapest',NULL),(8724,'en','sort_cheapest','##sort_cheapest',NULL),(8726,'cs','sort_name','##sort_name',NULL),(8728,'en','sort_name','##sort_name',NULL),(8730,'cs','btn_add_to_basket','Vložit do košíku',NULL),(8732,'en','btn_add_to_basket','##btn_add_to_basket',NULL),(8734,'cs','btn_filter_remove','Resetovat celý filtr',NULL),(8735,'en','btn_filter_remove','##btn_filter_remove',NULL),(8736,'cs','delivery_info_address','##delivery_info_address',NULL),(8737,'en','delivery_info_address','##delivery_info_address',NULL),(8738,'cs','btn_filter_cancel','##btn_filter_cancel',NULL),(8739,'en','btn_filter_cancel','##btn_filter_cancel',NULL),(8740,'cs','paging_next','##paging_next',NULL),(8741,'en','paging_next','##paging_next',NULL),(8742,'cs','showing','##showing',NULL),(8743,'en','showing','##showing',NULL),(8744,'cs','of','##of',NULL),(8745,'en','of','##of',NULL),(8746,'cs','show_more_products','##show_more_products',NULL),(8747,'en','show_more_products','##show_more_products',NULL),(8748,'cs','pvalue_6580','##1',NULL),(8749,'en','pvalue_6580','##1',NULL),(8750,'cs','pvalue_alias_6580','##6580',NULL),(8751,'en','pvalue_alias_6580','##6580',NULL),(8752,'cs','pvalue_filter_6580','##',NULL),(8753,'en','pvalue_filter_6580','##',NULL),(8754,'cs','to','##to',NULL),(8755,'en','to','##to',NULL),(8756,'cs','pname_unit_117','##',NULL),(8757,'en','pname_unit_117','##',NULL),(8758,'cs','pname_unit_118','##',NULL),(8759,'en','pname_unit_118','##',NULL),(8760,'cs','pname_unit_120','##',NULL),(8761,'en','pname_unit_120','##',NULL),(8762,'cs','pname_tooltip_120','##',NULL),(8763,'en','pname_tooltip_120','##',NULL),(8764,'cs','pname_120','##Wysiwyg',NULL),(8765,'en','pname_120','##Wysiwyg',NULL),(8766,'cs','pname_tooltip_118','##',NULL),(8767,'en','pname_tooltip_118','##',NULL),(8768,'cs','pname_118','##Textarea',NULL),(8769,'en','pname_118','##Textarea',NULL),(8770,'cs','pname_tooltip_117','##',NULL),(8771,'en','pname_tooltip_117','##',NULL),(8772,'cs','pname_117','##Text',NULL),(8773,'en','pname_117','##Text',NULL),(8774,'cs','btn_show_more_products','##btn_show_more_products',NULL),(8775,'en','btn_show_more_products','##btn_show_more_products',NULL),(8776,'cs','pname_1','##Parametry',NULL),(8777,'en','pname_1','##Parametry',NULL),(8778,'cs','pname_tooltip_1','##',NULL),(8779,'en','pname_tooltip_1','##',NULL),(8780,'cs','pname_121','Kategorie zbraně',NULL),(8781,'en','pname_121','Enter a new name',NULL),(8782,'cs','pname_tooltip_121','',NULL),(8783,'en','pname_tooltip_121','',NULL),(8784,'cs','pname_unit_121','',NULL),(8785,'en','pname_unit_121','',NULL),(8798,'cs','pvalue_6582','C',NULL),(8799,'en','pvalue_6582','C',NULL),(8800,'cs','pvalue_alias_6582','c',NULL),(8801,'en','pvalue_alias_6582','c',NULL),(8802,'cs','pvalue_filter_6582','',NULL),(8803,'en','pvalue_filter_6582','',NULL),(8804,'cs','pvalue_6583','D',NULL),(8805,'en','pvalue_6583','D',NULL),(8806,'cs','pvalue_alias_6583','d',NULL),(8807,'en','pvalue_alias_6583','d',NULL),(8808,'cs','btn_old_product','##btn_old_product',NULL),(8809,'en','btn_old_product','##btn_old_product',NULL),(8810,'cs','title_prebasket_choose_variant','##title_prebasket_choose_variant',NULL),(8811,'en','title_prebasket_choose_variant','##title_prebasket_choose_variant',NULL),(8812,'cs','pvalue_filter_6583','',NULL),(8813,'en','pvalue_filter_6583','',NULL),(8832,'cs','pvalue_6584','##x',NULL),(8833,'en','pvalue_6584','##x',NULL),(8834,'cs','pname_122','##Enter a new name',NULL),(8835,'en','pname_122','##Enter a new name',NULL),(8836,'cs','pname_123','##Enter a new name',NULL),(8837,'en','pname_123','##Enter a new name',NULL),(8838,'cs','pname_124','##Enter a new name',NULL),(8839,'en','pname_124','##Enter a new name',NULL),(8840,'cs','pname_125','##Enter a new name',NULL),(8841,'en','pname_125','##Enter a new name',NULL),(8842,'cs','pname_tooltip_124','##',NULL),(8843,'en','pname_tooltip_124','##',NULL),(8844,'cs','pname_tooltip_122','##',NULL),(8845,'en','pname_tooltip_122','##',NULL),(8846,'cs','pname_unit_122','##',NULL),(8847,'en','pname_unit_122','##',NULL),(8848,'cs','pname_tooltip_123','##',NULL),(8849,'en','pname_tooltip_123','##',NULL),(8850,'cs','pname_unit_123','##',NULL),(8851,'en','pname_unit_123','##',NULL),(8852,'cs','pname_unit_124','##',NULL),(8853,'en','pname_unit_124','##',NULL),(8854,'cs','pname_tooltip_125','##',NULL),(8855,'en','pname_tooltip_125','##',NULL),(8856,'cs','pname_unit_125','##',NULL),(8857,'en','pname_unit_125','##',NULL),(8858,'cs','pname_126','##Enter a new name',NULL),(8859,'en','pname_126','##Enter a new name',NULL),(8860,'cs','pname_tooltip_126','##',NULL),(8861,'en','pname_tooltip_126','##',NULL),(8862,'cs','pname_unit_126','##',NULL),(8863,'en','pname_unit_126','##',NULL),(8864,'cs','price_from','cena od',NULL),(8865,'en','price_from','##price_from',NULL),(8866,'cs','availability_only_some_variants','jen některé varianty',NULL),(8867,'en','availability_only_some_variants','##availability_only_some_variants',NULL),(8868,'cs','paging_prev','##paging_prev',NULL),(8869,'en','paging_prev','##paging_prev',NULL),(8870,'cs','another_address_form','##another_address_form',NULL),(8871,'en','another_address_form','##another_address_form',NULL),(8872,'cs','form_label_agree','##form_label_agree',NULL),(8873,'en','form_label_agree','##form_label_agree',NULL),(8874,'cs','btn_send','##btn_send',NULL),(8875,'en','btn_send','##btn_send',NULL),(8876,'cs','availability_unavailable','##availability_unavailable',NULL),(8877,'en','availability_unavailable','##availability_unavailable',NULL),(8878,'cs','from','##from',NULL),(8879,'en','from','##from',NULL),(8880,'cs','cookie','##cookie',NULL),(8881,'en','cookie','##cookie',NULL),(8882,'cs','link_cookie','##link_cookie',NULL),(8883,'en','link_cookie','##link_cookie',NULL),(8884,'cs','btn_cookie','##btn_cookie',NULL),(8885,'en','btn_cookie','##btn_cookie',NULL),(8886,'cs','search_title','##search_title',NULL),(8887,'en','search_title','##search_title',NULL),(8888,'cs','search_nothing','##search_nothing',NULL),(8889,'en','search_nothing','##search_nothing',NULL),(8890,'cs','search_products','##search_products',NULL),(8891,'en','search_products','##search_products',NULL),(8892,'cs','search_show_all','##search_show_all',NULL),(8893,'en','search_show_all','##search_show_all',NULL),(8894,'cs','search_pages','##search_pages',NULL),(8895,'en','search_pages','##search_pages',NULL),(8896,'cs','filter_dial_main_category','##filter_dial_main_category',NULL),(8897,'en','filter_dial_main_category','##filter_dial_main_category',NULL),(8898,'cs','filter_main_category_description','##filter_main_category_description',NULL),(8899,'en','filter_main_category_description','##filter_main_category_description',NULL),(8900,'cs','souhlasim-s-odberem-newsletteru','##souhlasim-s-odberem-newsletteru',NULL),(8901,'en','souhlasim-s-odberem-newsletteru','##souhlasim-s-odberem-newsletteru',NULL),(8902,'cs','pvalue_alias_6584','##x',NULL),(8903,'en','pvalue_alias_6584','##x',NULL),(8904,'cs','pvalue_filter_6584','##',NULL),(8905,'en','pvalue_filter_6584','##',NULL),(8924,'cs','pname_127','Ráže',NULL),(8925,'en','pname_127','Enter a new name',NULL),(8926,'cs','pname_tooltip_127','',NULL),(8927,'en','pname_tooltip_127','',NULL),(8928,'cs','pname_unit_127','',NULL),(8929,'en','pname_unit_127','',NULL),(8936,'cs','pvalue_6586','4,5 mm',NULL),(8937,'en','pvalue_6586','4,5 mm',NULL),(8938,'cs','pvalue_alias_6586','4-5-mm',NULL),(8939,'en','pvalue_alias_6586','4-5-mm',NULL),(8940,'cs','pvalue_filter_6586','',NULL),(8941,'en','pvalue_filter_6586','',NULL),(8942,'cs','pvalue_6587','5 mm',NULL),(8943,'en','pvalue_6587','5 mm',NULL),(8944,'cs','pvalue_alias_6587','5-mm',NULL),(8945,'en','pvalue_alias_6587','5-mm',NULL),(8946,'cs','pvalue_filter_6587','',NULL),(8947,'en','pvalue_filter_6587','',NULL),(9020,'cs','pvalue_6588','##5,5 mm',NULL),(9021,'en','pvalue_6588','##5,5 mm',NULL),(9022,'cs','pvalue_alias_6588','##5-5-mm',NULL),(9023,'en','pvalue_alias_6588','##5-5-mm',NULL),(9024,'cs','pvalue_filter_6588','##',NULL),(9025,'en','pvalue_filter_6588','##',NULL),(9026,'cs','pname_128','Délka hlavně',NULL),(9027,'en','pname_128','Enter a new name',NULL),(9028,'cs','pname_tooltip_128','',NULL),(9029,'en','pname_tooltip_128','',NULL),(9030,'cs','pname_unit_128','mm',NULL),(9031,'en','pname_unit_128','',NULL),(9050,'cs','pname_112','##Stránky',NULL),(9051,'en','pname_112','##Stránky',NULL),(9052,'cs','pname_tooltip_112','##',NULL),(9053,'en','pname_tooltip_112','##',NULL),(9054,'cs','pname_unit_112','##',NULL),(9055,'en','pname_unit_112','##',NULL),(9056,'cs','comp_title','Porovnání',NULL),(9057,'cs','pvalue_6568','##Tag 1',NULL),(9058,'en','pvalue_6568','##Tag 1',NULL),(9059,'cs','pvalue_alias_6568','##tag-1',NULL),(9060,'en','pvalue_alias_6568','##tag-1',NULL),(9061,'cs','pvalue_filter_6568','##',NULL),(9062,'en','pvalue_filter_6568','##',NULL),(9063,'cs','pvalue_6569','##Tag 2',NULL),(9064,'en','pvalue_6569','##Tag 2',NULL),(9065,'cs','pvalue_alias_6569','##tag-2',NULL),(9066,'en','pvalue_alias_6569','##tag-2',NULL),(9067,'cs','pvalue_filter_6569','##',NULL),(9068,'en','pvalue_filter_6569','##',NULL),(9069,'cs','pname_115','##Tag',NULL),(9070,'en','pname_115','##Tag',NULL),(9071,'cs','pname_tooltip_115','##',NULL),(9072,'en','pname_tooltip_115','##',NULL),(9073,'cs','pname_unit_115','##',NULL),(9074,'en','pname_unit_115','##',NULL),(9075,'cs','newsletter_note','Novinky ze světa Balistas.cz přímo do e-mailu.',NULL),(9076,'cs','newsletter_link','zpracováním osobních údajů',NULL),(9077,'cs','contact_title_decor','Potřebujete s námi něco vyřídit?',NULL),(9078,'cs','contact_title','napište nám',NULL),(9085,'cs','message_buy_to_free_transport','Nakupte ještě za %priceToFreeTransport% a máte dopravu zdarma!',NULL),(9086,'en','message_buy_to_free_transport','##message_buy_to_free_transport',NULL),(9088,'cs','next','##next',NULL),(9089,'en','next','##next',NULL),(9090,'cs','order_login_1','##order_login_1',NULL),(9091,'en','order_login_1','##order_login_1',NULL),(9092,'cs','order_login_2','##order_login_2',NULL),(9093,'en','order_login_2','##order_login_2',NULL),(9094,'cs','order_login_3','##order_login_3',NULL),(9095,'en','order_login_3','##order_login_3',NULL),(9096,'cs','billing_info_title','Fakturační údaje',NULL),(9097,'en','billing_info_title','##billing_info_title',NULL),(9098,'cs','title_register_form','##title_register_form',NULL),(9099,'en','title_register_form','##title_register_form',NULL),(9130,'cs','form_label_heureka_disable','Nesouhlasím se zasláním dotazníku spokojenosti',NULL),(9131,'cs','search_categories','##search_categories',NULL),(9132,'en','search_categories','##search_categories',NULL),(9181,'cs','product_flag_new','##product_flag_new',NULL),(9182,'en','product_flag_new','##product_flag_new',NULL),(9183,'cs','free_transport_text_1_buy_to','Nakupte ještě za',NULL),(9184,'cs','free_transport_text_1_get','a získáte',NULL),(9185,'cs','free_transport_text_1_delivery_free','dopravu zdarma',NULL),(9186,'cs','title_tags','##title_tags',NULL),(9187,'en','title_tags','##title_tags',NULL),(9188,'cs','pname_130','##Enter a new name',NULL),(9189,'en','pname_130','##Enter a new name',NULL),(9190,'cs','pname_tooltip_130','##',NULL),(9191,'en','pname_tooltip_130','##',NULL),(9192,'cs','pname_unit_130','##',NULL),(9193,'en','pname_unit_130','##',NULL),(9194,'cs','title_menu','##title_menu',NULL),(9195,'en','title_menu','##title_menu',NULL),(9196,'cs','tag','##tag',NULL),(9197,'en','tag','##tag',NULL),(9198,'cs','article_reading_time','##article_reading_time',NULL),(9199,'en','article_reading_time','##article_reading_time',NULL),(9200,'cs','minutes','##minutes',NULL),(9201,'en','minutes','##minutes',NULL),(9202,'cs','title_authors_other','##title_authors_other',NULL),(9203,'en','title_authors_other','##title_authors_other',NULL),(9204,'cs','authors_all','##authors_all',NULL),(9205,'en','authors_all','##authors_all',NULL),(9206,'cs','search_tab_trees','##search_tab_trees',NULL),(9207,'en','search_tab_trees','##search_tab_trees',NULL),(9208,'cs','search_tab_blogs','##search_tab_blogs',NULL),(9209,'en','search_tab_blogs','##search_tab_blogs',NULL),(9210,'cs','search_tab_products','##search_tab_products',NULL),(9211,'en','search_tab_products','##search_tab_products',NULL),(9212,'cs','search_tab_categories','##search_tab_categories',NULL),(9213,'en','search_tab_categories','##search_tab_categories',NULL),(9214,'cs','filter_flag_is_in_discount','##filter_flag_is_in_discount',NULL),(9215,'en','filter_flag_is_in_discount','##filter_flag_is_in_discount',NULL),(9216,'cs','filter_flag_is_in_discount_unit','##filter_flag_is_in_discount_unit',NULL),(9217,'en','filter_flag_is_in_discount_unit','##filter_flag_is_in_discount_unit',NULL),(9218,'cs','filter_flag_is_in_discount_description','##filter_flag_is_in_discount_description',NULL),(9219,'en','filter_flag_is_in_discount_description','##filter_flag_is_in_discount_description',NULL),(9244,'cs','pvalue_6593','##h4',NULL),(9245,'en','pvalue_6593','##h4',NULL),(9246,'cs','pvalue_alias_6593','##h4',NULL),(9247,'en','pvalue_alias_6593','##h4',NULL),(9248,'cs','pvalue_filter_6593','##',NULL),(9249,'en','pvalue_filter_6593','##',NULL),(9250,'cs','pname_131','##Enter a new name',NULL),(9251,'en','pname_131','##Enter a new name',NULL),(9252,'cs','pname_tooltip_131','##',NULL),(9253,'en','pname_tooltip_131','##',NULL),(9254,'cs','pname_unit_131','##',NULL),(9255,'en','pname_unit_131','##',NULL),(9256,'cs','filter_order_status','##filter_order_status',NULL),(9257,'en','filter_order_status','##filter_order_status',NULL),(9258,'cs','filter_order_date_from','##filter_order_date_from',NULL),(9259,'en','filter_order_date_from','##filter_order_date_from',NULL),(9260,'cs','filter_order_date_to','##filter_order_date_to',NULL),(9261,'en','filter_order_date_to','##filter_order_date_to',NULL),(9262,'cs','filter_order_number','##filter_order_number',NULL),(9263,'en','filter_order_number','##filter_order_number',NULL),(9264,'cs','order_title','##order_title',NULL),(9265,'en','order_title','##order_title',NULL),(9266,'cs','order_status','##order_status',NULL),(9267,'en','order_status','##order_status',NULL),(9268,'cs','order_number','##order_number',NULL),(9269,'en','order_number','##order_number',NULL),(9270,'cs','order_amount','##order_amount',NULL),(9271,'en','order_amount','##order_amount',NULL),(9272,'cs','order_created','##order_created',NULL),(9273,'en','order_created','##order_created',NULL),(9274,'cs','order_delivery','##order_delivery',NULL),(9275,'en','order_delivery','##order_delivery',NULL),(9276,'cs','order_payment','##order_payment',NULL),(9277,'en','order_payment','##order_payment',NULL),(9278,'cs','order_total_price_vat','##order_total_price_vat',NULL),(9279,'en','order_total_price_vat','##order_total_price_vat',NULL),(9280,'cs','btn_show_all_orders','##btn_show_all_orders',NULL),(9281,'en','btn_show_all_orders','##btn_show_all_orders',NULL),(9282,'cs','order_detail_title','##order_detail_title',NULL),(9283,'en','order_detail_title','##order_detail_title',NULL),(9284,'cs','order_item_status','##order_item_status',NULL),(9285,'en','order_item_status','##order_item_status',NULL),(9286,'cs','unit_price','##unit_price',NULL),(9287,'en','unit_price','##unit_price',NULL),(9288,'cs','total_price_vat_total','##total_price_vat_total',NULL),(9289,'en','total_price_vat_total','##total_price_vat_total',NULL),(9290,'cs','filter_flag_is_in_store','##filter_flag_is_in_store',NULL),(9291,'en','filter_flag_is_in_store','##filter_flag_is_in_store',NULL),(9292,'cs','filter_flag_is_in_store_unit','##filter_flag_is_in_store_unit',NULL),(9293,'en','filter_flag_is_in_store_unit','##filter_flag_is_in_store_unit',NULL),(9294,'cs','filter_flag_is_in_store_description','##filter_flag_is_in_store_description',NULL),(9295,'en','filter_flag_is_in_store_description','##filter_flag_is_in_store_description',NULL),(9296,'cs','is_in_store','##is_in_store',NULL),(9297,'en','is_in_store','##is_in_store',NULL),(9298,'cs','pname_filter_prefix_114','##',NULL),(9299,'en','pname_filter_prefix_114','##',NULL),(9300,'cs','newsletter_already_added','##newsletter_already_added',NULL),(9301,'en','newsletter_already_added','##newsletter_already_added',NULL),(9302,'cs','title_attached_articles','##title_attached_articles',NULL),(9303,'en','title_attached_articles','##title_attached_articles',NULL),(9304,'cs','pname_filter_prefix_113','##',NULL),(9305,'en','pname_filter_prefix_113','##',NULL),(9306,'cs','author','##author',NULL),(9307,'en','author','##author',NULL),(9308,'en','user_not_found','Account not found',NULL),(9309,'cs','user_not_found','Účet nenalezen',NULL),(9310,'en','message_ok_login','Successfully logged in',NULL),(9311,'cs','message_ok_login','Úspěšně přihlášen',NULL),(9312,'en','message_bad_login','Login failed',NULL),(9313,'cs','message_bad_login','Přihlášení se nepovedlo',NULL),(9315,'cs','voucher_error_already_in_cart','Slevový kód se už nacházi v nákupném košíku.',NULL),(9316,'cs','voucher_error_voucher_bad_using_min_order','Slevový poukaz nelze uplatnit, minimální hodnota nákupu pro uplatnění slevy je %minOrderPrice%',NULL),(9317,'cs','voucher_error_voucher_expired','Slevový kód je neplatný.',NULL),(9318,'cs','voucher_error_voucher_no_exists','Slevový kód neexistuje.',NULL),(9319,'cs','voucher_error_voucher_used','Slevový kód již byl použit.',NULL),(9320,'cs','voucher_error_voucher_combination','Není možné kombinovat tyto slevové kódy.',NULL),(9321,'cs','cart_message_voucher_added','Slevový kód byl přidán do nákupného košíku.',NULL),(9322,'cs','cart_message_voucher_removed','Slevový kód (%code%) byl odebrán z nákupného košíku. Důvod: %reason%',NULL),(9323,'cs','btn_show','##btn_show',NULL),(9324,'en','btn_show','##btn_show',NULL),(9325,'cs','form_valid_filled','Políčko není správně vyplněno.',NULL),(9326,'en','form_valid_filled','##form_valid_filled',NULL),(9329,'cs','error_invalid_phone','##error_invalid_phone',NULL),(9330,'en','error_invalid_phone','##error_invalid_phone',NULL),(9331,'cs','page_404_name','##page_404_name',NULL),(9332,'en','page_404_name','##page_404_name',NULL),(9333,'cs','page_404_annotation','##page_404_annotation',NULL),(9334,'en','page_404_annotation','##page_404_annotation',NULL),(9335,'cs','page_404_description','##page_404_description',NULL),(9336,'en','page_404_description','##page_404_description',NULL),(9337,'cs','facebook','Facebook',NULL),(9338,'en','facebook','Facebook',NULL),(9339,'cs','linkedin','Linkedin',NULL),(9340,'en','linkedin','Linkedin',NULL),(9341,'cs','youtube','Youtube',NULL),(9342,'en','youtube','Youtube',NULL),(9343,'cs','edit_page','Upravit',NULL),(9344,'en','edit_page','Edit',NULL),(9345,'cs','form_placeholder_firstname','Vaše jméno',NULL),(9346,'en','form_placeholder_firstname','Your name',NULL),(9347,'cs','form_placeholder_email','Váš e-mail',NULL),(9348,'en','form_placeholder_email','##form_placeholder_email',NULL),(9349,'cs','form_placeholder_text','Napište nám, jak vám můžeme pomoci. Zpravidla reagujeme do 24 hodin.',NULL),(9350,'en','form_placeholder_text','Let us know how we can help you. We usually respond within 24 hours.',NULL),(9351,'cs','btn_send_arrow','Poslat',NULL),(9352,'en','btn_send_arrow','Send',NULL),(9353,'en','error_voucher_bad_using_min_order','##error_voucher_bad_using_min_order',NULL),(9354,'en','form_reset_password','##form_reset_password',NULL),(9355,'en','comp_title','##comp_title',NULL),(9356,'en','newsletter_note','##newsletter_note',NULL),(9357,'en','newsletter_link','##newsletter_link',NULL),(9358,'en','contact_title_decor','##contact_title_decor',NULL),(9359,'en','contact_title','##contact_title',NULL),(9360,'en','form_label_heureka_disable','##form_label_heureka_disable',NULL),(9361,'en','free_transport_text_1_buy_to','##free_transport_text_1_buy_to',NULL),(9362,'en','free_transport_text_1_get','##free_transport_text_1_get',NULL),(9363,'en','free_transport_text_1_delivery_free','##free_transport_text_1_delivery_free',NULL),(9364,'en','voucher_error_already_in_cart','##voucher_error_already_in_cart',NULL),(9365,'en','voucher_error_voucher_bad_using_min_order','##voucher_error_voucher_bad_using_min_order',NULL),(9366,'en','voucher_error_voucher_expired','##voucher_error_voucher_expired',NULL),(9367,'en','voucher_error_voucher_no_exists','##voucher_error_voucher_no_exists',NULL),(9368,'en','voucher_error_voucher_used','##voucher_error_voucher_used',NULL),(9369,'en','voucher_error_voucher_combination','##voucher_error_voucher_combination',NULL),(9370,'en','cart_message_voucher_added','##cart_message_voucher_added',NULL),(9371,'en','cart_message_voucher_removed','##cart_message_voucher_removed',NULL),(9372,'cs','social_media','Sociální sítě',NULL),(9373,'en','social_media','Social',NULL),(9374,'cs','btn_all_casestudies','Všechny case studies',NULL),(9375,'en','btn_all_casestudies','All case studies',NULL),(9376,'cs','contact_map','Mapa',NULL),(9377,'en','contact_map','Maps',NULL),(9378,'cs','tag_all','Vše',NULL),(9379,'en','tag_all','All',NULL),(9380,'cs','form_placeholder_surname','Vaše příjmení',NULL),(9381,'en','form_placeholder_surname','##form_placeholder_surname',NULL),(9382,'cs','btn_load_more','Načíst další ↓',NULL),(9383,'en','btn_load_more','Load more ↓',NULL),(9384,'cs','btn_back_blog','<- Zpět na blog',NULL),(9385,'en','btn_back_blog','<- Back to blog',NULL),(9386,'cs','title_contact_person_form','Kontaktujte nás',NULL),(9387,'en','title_contact_person_form','Contact us',NULL),(9388,'cs','contact_person_form_person','Kdo se vám bude věnovat?',NULL),(9389,'en','contact_person_form_person','##contact_person_form_person',NULL),(9390,'cs','share_title','Sdílejte tento článek na sociálních sítích',NULL),(9391,'en','share_title','##share_title',NULL),(9392,'cs','show_more','##show_more',NULL),(9393,'en','show_more','##show_more',NULL),(9394,'cs','btn_detail','Detail',NULL),(9395,'en','btn_detail','Detail',NULL),(9396,'cs','download_webinar','Stáhnout webinář',NULL),(9397,'en','download_webinar','##download_webinar',NULL),(9398,'cs','btn_read_more','více',NULL),(9399,'en','btn_read_more','more',NULL),(9400,'cs','search_label','##search_label',NULL),(9401,'en','search_label','##search_label',NULL);
/*!40000 ALTER TABLE `string` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `testimonial`
--

DROP TABLE IF EXISTS `testimonial`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `testimonial` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `internalName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `testimonial`
--

LOCK TABLES `testimonial` WRITE;
/*!40000 ALTER TABLE `testimonial` DISABLE KEYS */;
INSERT INTO `testimonial` VALUES (1,'Testovaci reference','{\"base\":[{\"bg\":\"u-bgc-green-light\",\"mainImage\":\"144\",\"quote\":\"“Due diligence máme v malíku, provedeme vás celým procesem bez zbytečných stresů.“\",\"biggerQuote\":true,\"name\":\"Joe Black\",\"position\":\"Managing director\",\"company\":\"@ Dropbox\",\"logo\":\"82\",\"button\":[{\"toggle\":\"systemHref\",\"systemHref\":[{\"page\":446,\"hrefName\":\"Vice referenci\"}]}]}]}'),(2,'Testimonial','{\"base\":[{\"bg\":\"u-bgc-tertiary\",\"mainImage\":\"145\",\"quote\":\"“Cením si především vaší profesionality a prozákaznicky orientovaného přístupu. Musím ale podtrhnout, že jste nám byli nápomocni 24 hodin denně a vždy měli prostor a porozumění pro naše mnohdy všetečné dotazy.“\",\"biggerQuote\":true,\"name\":\"Gevorg Avetisjan\",\"position\":\"Founder \",\"company\":\"@ MARLENKA international s.r.o.\",\"logo\":\"121\",\"button\":[{\"toggle\":\"systemHref\",\"systemHref\":[{\"page\":460,\"hrefName\":\"Další firmy, které jsou s námi spokojené\"}]}],\"images\":[\"116\",\"115\",\"102\"]}]}'),(3,'Další testimonial','{\"base\":[{\"title\":\"Forendors\",\"mainImage\":\"142\",\"quote\":\"Forendors je monetizační platforma, která propojuje tvůrce a jejich fanoušky, aby se lidé mohli živit v online prostředí tím, co je baví. Historie se začala psát v době covidu, kdy se lidé stáhli z offlinu. Forendors svým tvůrcům pomáhá budovat funkční komunity, vyvinuli mobilní aplikaci a mají vlastní podcastová studia.\\n\",\"name\":\"Joe Black\",\"position\":\"Managing director\",\"company\":\"@ Dropbox\",\"logo\":\"115\",\"button\":[{\"toggle\":\"customHref\",\"customHref\":[{\"href\":\"#\",\"hrefName\":\"Vice referenci\"}]}]}]}'),(4,'Služby','{\"base\":[{\"bg\":\"u-bgc-yellow\",\"mainImage\":\"147\",\"quote\":\"“Vysvětlíme vám právo tak, abyste rozuměli tomu, co říkáme, a hlavně abyste věděli, jaký dopad to celé bude mít na váš byznys.“\",\"biggerQuote\":true,\"name\":\"Jana Sedláková\",\"position\":\"Founder\",\"button\":[{\"toggle\":\"systemHref\"}]}]}'),(5,'Kariéra','{\"base\":[{\"bg\":\"u-bgc-green\",\"mainImage\":\"161\",\"quote\":\"Pracovat v Sedlakova Legal je boží, protože tady něco napíšeme a bude to tak, protože bychom přece nekecali :D naplněn text\",\"biggerQuote\":true,\"name\":\"Jana Sedláková\",\"position\":\"founder\",\"button\":[{\"toggle\":\"systemHref\",\"systemHref\":[{\"page\":\"460\",\"hrefName\":\"Vice referenci\"}]}]}]}');
/*!40000 ALTER TABLE `testimonial` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `testimonial_localization`
--

DROP TABLE IF EXISTS `testimonial_localization`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `testimonial_localization` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mutationId` int(11) NOT NULL,
  `testimonial` int(11) NOT NULL,
  `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `nameAnchor` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `nameTitle` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `keywords` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `isTop` int(11) NOT NULL DEFAULT '0',
  `public` int(11) NOT NULL DEFAULT '0',
  `forceNoIndex` int(11) NOT NULL DEFAULT '0',
  `hideInSearch` int(11) NOT NULL DEFAULT '0',
  `hideInSitemap` int(11) NOT NULL DEFAULT '0',
  `publicFrom` datetime DEFAULT NULL,
  `publicTo` datetime DEFAULT NULL,
  `edited` int(11) DEFAULT NULL,
  `editedTime` datetime DEFAULT NULL,
  `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `customContentSchemeJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `customContentJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `viewsNumber` int(10) unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `FK_blog_mutation` (`mutationId`) USING BTREE,
  KEY `FK_blog_localization_blog` (`testimonial`) USING BTREE,
  CONSTRAINT `testimonial_localization_ibfk_1` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE,
  CONSTRAINT `testimonial_localization_ibfk_2` FOREIGN KEY (`testimonial`) REFERENCES `testimonial` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `testimonial_localization`
--

LOCK TABLES `testimonial_localization` WRITE;
/*!40000 ALTER TABLE `testimonial_localization` DISABLE KEYS */;
INSERT INTO `testimonial_localization` VALUES (1,1,1,'Test reference','','','','',0,1,0,0,0,'2025-05-26 05:21:00','2125-05-26 05:21:00',34,'2025-05-30 08:14:34','{}',NULL,'{}',0),(2,1,2,'Marlenka','','','','',0,1,0,0,0,'2025-05-27 23:56:00','2125-05-27 23:56:00',34,'2025-05-30 10:28:35','{}',NULL,'{}',0),(3,1,3,'Další testimonial','','','','',0,1,0,0,0,'2025-05-27 23:56:00','2125-05-27 23:56:00',34,'2025-05-30 08:10:25','{}',NULL,'{}',0),(4,1,4,'Služby','','','','',0,1,0,0,0,'2025-05-29 13:36:00','2125-05-29 13:36:00',34,'2025-05-30 08:26:40','{}',NULL,'{}',0),(5,1,5,'Kariéra','','','','',0,1,0,0,0,'2025-06-06 06:07:00','2125-06-06 06:07:00',34,'2025-06-06 06:09:28','{}',NULL,'{}',0);
/*!40000 ALTER TABLE `testimonial_localization` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tree`
--

DROP TABLE IF EXISTS `tree`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tree` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mutationId` int(11) NOT NULL DEFAULT '1',
  `rootId` int(11) NOT NULL,
  `treeParentId` int(11) DEFAULT NULL,
  `parentId` int(11) DEFAULT NULL,
  `level` tinyint(4) DEFAULT NULL,
  `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `sort` int(11) DEFAULT NULL,
  `last` tinyint(1) DEFAULT NULL,
  `uid` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `created` int(11) NOT NULL,
  `createdTime` datetime NOT NULL,
  `createdTimeOrder` datetime DEFAULT NULL,
  `edited` int(11) DEFAULT NULL,
  `editedTime` datetime DEFAULT NULL,
  `template` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `type` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `publicFrom` datetime DEFAULT NULL,
  `publicTo` datetime DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `nameAnchor` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `nameTitle` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `keywords` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `public` int(11) NOT NULL DEFAULT '0',
  `forceNoIndex` int(11) NOT NULL DEFAULT '0',
  `hideInSearch` int(11) NOT NULL DEFAULT '0',
  `hideInSitemap` int(11) NOT NULL DEFAULT '0',
  `annotation` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `hideFirstImage` tinyint(1) DEFAULT NULL,
  `links` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `seoTitleFilter` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `seoAnnotationFilter` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `seoDescriptionFilter` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `videos` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `customFieldsJson` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `customContentJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `productAttachedId` int(11) DEFAULT NULL COMMENT 'produkt voucher napojený na skoleni ',
  `hasLinkedCategories` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idParent` (`parentId`),
  KEY `uid` (`uid`),
  KEY `public` (`public`),
  KEY `FK_tree_mutation` (`mutationId`),
  KEY `FK_tree_tree_parent` (`treeParentId`),
  CONSTRAINT `FK_tree_mutation` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `FK_tree_tree_parent` FOREIGN KEY (`treeParentId`) REFERENCES `tree_parent` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `tree_ibfk_1` FOREIGN KEY (`parentId`) REFERENCES `tree` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=487 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tree`
--

LOCK TABLES `tree` WRITE;
/*!40000 ALTER TABLE `tree` DISABLE KEYS */;
INSERT INTO `tree` VALUES (1,1,1,1,NULL,0,NULL,1,0,'title',0,'2017-01-27 14:53:52','2017-01-27 14:53:00',34,'2025-05-30 10:49:20',':Front:Homepage:default','common','2013-10-07 23:44:00','2100-01-01 00:00:00','Experti na  vaše IT výzvy','Úvod','Sedláková Legal','','',1,0,0,0,'?, jsme responzivní právní poradenství a specializujeme se na software, technologie a duševní vlastnictví.                    ','',1,'','','','','','{\"annot_hp\":[{\"btn\":[{\"toggle\":\"systemHref\",\"systemHref\":[{\"page\":462,\"hrefName\":\"Pojďme to probrat\"}]}],\"awards\":[{\"title\":\"Naše ocenění\",\"images\":[\"127\",\"126\",\"125\"]}],\"rating\":\"4,9\",\"authors\":[{\"author\":2},{\"author\":3},{\"author\":6},{\"author\":7},{\"author\":5},{\"author\":4}]}],\"logos\":[{\"images\":[\"132\",\"130\",\"81\",\"82\",\"97\",\"96\",\"95\",\"94\",\"102\",\"103\",\"104\",\"105\",\"109\",\"108\",\"107\",\"106\",\"110\",\"112\",\"113\"]}],\"bussines\":[{\"title\":\"Řešení pro váš byznys\",\"content\":\"<p>Nabízíme profesionální právní služby šité na míru vašim potřebám. Ať už řídíte firmu, vedete obchodní tým, pracujete s lidmi nebo řešíte právní otázky, jsme tu, abychom vám pomohli zjednodušit vaše podnikání</p>\",\"items\":[{\"image\":\"135\",\"name\":\"Sales & finance\",\"title\":\"Výzvy podnikání známe na vlastní kůži\",\"text\":\"Jsme partneři pro právo i byznys. Myslíme na to, abyste měli podnikání jednodušší.\",\"button\":[{\"toggle\":\"systemHref\",\"systemHref\":[{\"page\":471,\"hrefName\":\"Detail\"}]}]},{\"image\":\"134\",\"name\":\"Startupy & investoři\",\"title\":\"Uzavírejte obchody bez obav\",\"text\":\"Jsme partneři pro právo i byznys. Myslíme na to, abyste měli podnikání jednodušší.\",\"button\":[{\"toggle\":\"systemHref\",\"systemHref\":[{\"page\":473,\"hrefName\":\"Detail\"}]}]},{\"image\":\"133\",\"name\":\"HR & back office\",\"title\":\"Víme, že práce s lidmi může být složitá\",\"text\":\"Jsme partneři pro právo i byznys. Myslíme na to, abyste měli podnikání jednodušší.\",\"button\":[{\"toggle\":\"systemHref\",\"systemHref\":[{\"page\":477,\"hrefName\":\"Detail\"}]}]},{\"image\":\"133\",\"name\":\"Realitní kanceláře\",\"title\":\"Zvládneme každou specializaci\",\"text\":\"Jsme partneři pro právo i byznys. Myslíme na to, abyste měli podnikání jednodušší.\",\"button\":[{\"toggle\":\"systemHref\",\"systemHref\":[{\"page\":474,\"hrefName\":\"Detail\"}]}]}]}],\"support\":[{\"title\":\"Komplexní podpora pro každou etapu podnikání\",\"content\":\"<p>Nabízíme specializované služby pro firmy všech velikostí - od začínajících startupů po etablované společnosti.</p>\\n<p>Díky synergii našich odborných týmů a partnerských firem poskytujeme komplexní řešení pro každou fázi životního cyklu vašeho podnikání - od prvotního nápadu, přes růst a expanzi, až po optimalizaci zralého byznysu či úspěšný exit.</p>\",\"button\":[{\"toggle\":\"systemHref\",\"systemHref\":[{\"page\":462,\"hrefName\":\"CTA TEXT\"}]}],\"hasAnimation\":true}],\"partners\":[{\"title\":\"Po našem boku stojí\",\"content\":\"Poskytujeme právní služby po celém světě s lokálním porozuměním a mezinárodní perspektivou\",\"items\":[{\"image\":\"131\",\"text\":\"Advokátní kancelář specializující se na všechny oblasti práva.\"},{\"image\":\"128\",\"text\":\"Vedení účetnictví, mzdová agendy a daňové problematiky.\"},{\"image\":\"132\",\"text\":\"Navigace v otázkách fúzí, akvizic, kapitálu a transformace podniků.\"},{\"image\":\"130\",\"text\":\"Fond na podporu startupů s cílem velikosti 25 milionů eur.\"},{\"image\":\"129\",\"text\":\"Investiční fond pro zaměstnance Sedlakova Legal.\"}]}],\"casestudies\":[{\"title\":\"Tady jsou případovky\",\"items\":[{\"blog\":1},{\"blog\":2},{\"blog\":3},{\"blog\":4}],\"link\":[{\"toggle\":\"systemHref\",\"systemHref\":[{\"page\":446,\"hrefName\":\"Na všechny případovky tudy\"}]}]}]}','{\"branches_map____PfAor05unM0tHwvYKBXj1\":[{\"show\":true,\"button\":[{\"toggle\":\"systemHref\",\"systemHref\":[{\"page\":483,\"hrefName\":\"Vice o pobočkách a partnerech\"}]}]}]}',NULL,NULL),(2,2,2,1,NULL,0,NULL,12,0,'title',0,'2022-09-04 20:02:34','2022-09-04 20:02:34',3,'2022-09-05 10:06:22',':Front:Homepage:default','common','2022-09-04 20:02:34','2122-09-04 20:02:34','En','En','En','','',1,0,0,0,'','',NULL,'',NULL,NULL,NULL,'','{}','{}',NULL,NULL),(8,1,1,8,1,1,'1|',9,1,'faq',40,'2017-01-27 14:53:52','2017-01-27 14:53:00',34,'2025-06-05 11:31:56',':Front:Page:faq','common','2021-07-08 17:00:00','2100-01-01 00:00:00','FAQ','FAQ','FAQ','','',1,0,0,0,'Jsme tu pro vás! A protože se snažíme na vaše požadavky nejen reagovat, ale naopak je i předvídat, sepsali jsme základní odpovědi na otázky, které byste mohli mít. Pokud nějakou odpověď níže nenajdete, kontaktujte nás a my se obratem ozveme.\r\n\r\nA pokud raději voláte než píšete, rovnou si s námi domluvte schůzku.','',1,'','','','','https://www.youtube.com/watch?v=nUwTnJ8yFXY|Tacos','{\"annot\":[{\"bg\":\"u-bgc-yellow\",\"btn\":[{\"toggle\":\"systemHref\"}],\"right\":[{\"image\":\"87\"}]}]}','{\"faq____Qc4u94yY4FUX6JpGP5m3T\":[{\"title\":\"Často se ptáte\",\"list\":[{\"question\":\"Můžu s vámi realizovat schůzku i vzdáleně? \",\"answer\":\"<p><span>Určitě můžete, tento způsob komunikace s klienty využíváme již od vzniku naší kanceláře. Můžeme se spojit přes Skype, Microsoft Teams, Zoom, Google Hangouts, Calendly i další aplikace. Samozřejmě je možnost spojit se i telefonicky a realizovat tímto způsobem celou schůzku.</span></p>\"},{\"question\":\"Kolik mě vaše právní pomoc bude stát?\",\"answer\":\"<p><span>Váš případ s vámi vždy nejprve zkonzultujeme, tato první konzultace je zdarma. Následně vám připravíme cenovou nabídku na míru vašim požadavkům. Pokud je to jen trochu možné, vždy vám před započetím prací sdělíme pevnou částku, kterou vám budeme účtovat. V některých případech však nelze částku odhadnout, jde převážně o pravidelné konzultace či zastupování před soudem. V takovém případě účtujeme hodinovou sazbou, která se pohybuje v rozsahu 2 800 - 3 200 Kč + DPH. Pokud víte, že naše služby budete potřebovat dlouhodobě a pravidelně, lze se domluvit i na paušální měsíční úhradě. V takovém případě máte vždy jistotu, že vám předem dohodnutý počet hodin v daném měsíci budeme věnovat.</span></p>\"},{\"question\":\"Máte smlouvu? A mohl bych ji vidět?\",\"answer\":\"<p><span>Ano, máme smlouvu o poskytování právních služeb, kterou můžeme společně uzavřít. Níže naleznete odkaz, kde se můžete na naši smlouvu můžete podívat, ať víte, co můžete očekávat.</span></p>\\n<p><span><a class=\\\"btn btn--primary\\\" href=\\\"#\\\"> <span class=\\\"btn__text\\\"><span>Smlouva o poskytování právních služeb --&gt;</span></span></a></span></p>\"},{\"question\":\"Musím vám něco zaplatit předem?\",\"answer\":\"<p>Většinou ne ? . Náš vztah je založený na důvěře a vaše slovo (a podepsaná<span> </span><a rel=\\\"noopener\\\" href=\\\"https://www.sedlakovalegal.cz/uploads/AB5z8B8fdu805Bpb3OA6sC1T7mncrwjB.pdf\\\" target=\\\"_blank\\\">smlouva o poskytování právních služeb ↗</a>) nám stačí. Zálohu vyžadujeme pouze pokud bychom měli s prací pro vás zvýšené hotové výdaje (například pokud vám zajišťujeme právní pomoc od našich zahraničních kolegů), nebo pokud se jedná o opravdu rozsáhlý projekt na několik měsíců.</p>\"},{\"question\":\"Potřebuji poradit, za jak dlouho mi odpovíte?\",\"answer\":\"<p><span>Na dotazy reagujeme v pracovní dny vždy do 24h od jejich obdržení. U složitých a časově náročných případů samozřejmě není možné je v této době zpracovat, ve lhůtě 24h od nás však vždy obdržíte odpověď s návrhem dalšího postupu a odhadovanou časovou náročností. I ve složitých věcech však uděláme vše pro to, abyste měli první výstup z naší strany nejpozději do týdne od potvrzení Vašeho zájmu o naše služby, případě od uhrazení zálohy. V jednodušších případech obvykle zasíláme výstup do 3 pracovních dnů.</span></p>\"},{\"question\":\"Mám urgentní problém a je nepracovní den, odpovíte mi?\",\"answer\":\"<p><span>Děláme vše pro to, abychom našim klientům byli v urgentních případech k dispozici i v nepracovní dny. Pokud tedy máte problém, který nepočká, můžete nám vždy napsat e-mail na </span><a href=\\\"mailto:<EMAIL>\\\"><EMAIL></a><span>. Zprávu lze zaslat i přímo na Facebooku na náš profil </span><a href=\\\"https://www.facebook.com/sedlakovalegal/\\\">SEDLAKOVA LEGAL ↗<span> </span></a><span>.</span></p>\"},{\"question\":\"Máte newsletter?\",\"answer\":\"<p><span>Máme <a class=\\\"btn btn--primary\\\" href=\\\"#\\\"> <span class=\\\"btn__text\\\"><span>Tady --&gt;</span></span></a></span></p>\"}]}]}',NULL,NULL),(12,1,1,12,43,2,'1|43|',4,1,'cookie',5,'2017-01-24 14:19:44','2017-01-24 14:19:00',34,'2025-04-25 08:35:29',':Front:Page:default','common','2017-01-24 14:19:00','2100-01-01 00:00:00','Prohlášení o používání cookies','Prohlášení o používání cookies','Prohlášení o používání cookies','','',1,0,0,0,'Prohlášení o používání cookies společností ...\r\n','<h2 class=\"left\">Co jsou cookies</h2>\r\n<p class=\"left\">Cookies jsou krátké textové soubory vytvářené webovým serverem a ukládané ve Vašem počítači prostřednictvím prohlížeče. Když se později vrátíte na stejný web, prohlížeč pošle uloženou cookie zpět a server tak získá všechny informace, které si u vás předtím uložil. Cookies využívá pro svou činnost naprostá většina webových stránek.</p>\r\n<h2 class=\"left\">Jak se dělí cookies</h2>\r\n<p class=\"left\">Cookies lze rozdělit podle toho, kdo je k Vám na web umisťuje, tj. na:</p>\r\n<ul class=\"left\">\r\n<li>Cookie první strany (first party cookie) – jejich platnost je omezena na doménu webu, který prohlížíte. Tyto cookies jsou považovány za bezpečnější.</li>\r\n<li>Cookie třetí strany (third party cookie) – jsou umístěny pomocí skriptu z jiné domény. Uživatele tak lze sledovat napříč doménami. Používají se často pro vyhodnocení účinnosti reklamních kanálů.</li>\r\n</ul>\r\n<p class=\"left\">Podle trvanlivosti lze cookies rozdělit na:</p>\r\n<ul>\r\n<li class=\"left\">Krátkodobé (session cookie) – vymažou se z vašeho počítače po zavření prohlížeče.</li>\r\n<li class=\"left\">Dlouhodobé (persistent cookie) – po zavření prohlížeče zůstávají zachovány, vymažou se teprve po uplynutí velmi dlouhé doby (ta záleží na nastavení Vašeho prohlížeče a nastavení cookie). Můžete je také ručně odstranit.</li>\r\n</ul>\r\n<h2 class=\"left\">K čemu cookies používáme</h2>\r\n<p class=\"left\">Na našem webu používáme tyto cookies:</p>\r\n<ul class=\"left\">\r\n<li>Technické – první strany, krátkodobé. Zajišťují základní technickou funkčnost webu, tj. přihlašování, využívání služeb apod.</li>\r\n<li>Google Analytics – první strany, dlouhodobé. Jsou využity ke generování anonymních statistik o používání webu.</li>\r\n<li>\r\n<p>Hotjar – první strany, krátkodobé i dlouhodobé. Pro analýzu návštěvnosti a zlepšení ovladatelnosti tohoto webu používáme nástroj Hotjar.</p>\r\n</li>\r\n</ul>\r\n<p class=\"left\">Do cookies nikdy neumisťujeme citlivá nebo osobní data.</p>\r\n<h2 class=\"left\">Jak lze upravit využívání cookies</h2>\r\n<h3 class=\"left\">Vymazání</h3>\r\n<p class=\"left\">Vymazat můžete cookies ve Vašem prohlížeči – zpravidla bývá umístěno v „Historii“ navštívených stránek.</p>\r\n<h3 class=\"left\">Blokování</h3>\r\n<p class=\"left\">Prohlížeče umožňují umísťování cookies na Vás počítač zablokovat. V takovém případě bude ale funkcionalita těchto stránek omezena. Informace o nastavení ukládání souborů cookies ve Vašem prohlížeči najdete na stránkách poskytovatele konkrétního prohlížeče:</p>\r\n<ul class=\"left\">\r\n<li><a href=\"https://support.google.com/accounts/answer/61416?hl=cs\">Chrome</a></li>\r\n<li><a href=\"https://support.mozilla.org/cs/kb/Práce%20s%20cookies\">Firefox</a></li>\r\n<li><a href=\"http://support.microsoft.com/gp/cookies/cs\">Internet Explorer</a></li>\r\n<li><a href=\"https://support.google.com/xoom/answer/169022?rd=1\">Android</a></li>\r\n</ul>\r\n<p class=\"left\">Další informace o cookies a jejich využití najdete na stránkách <a href=\"http://aboutcookies.org\">AboutCookies.org</a></p>\r\n<h2 class=\"left\">Tento web používá Google Analytics</h2>\r\n<p class=\"left\">Tato stránka používá službu Google Analytics, poskytovanou společností Google, Inc. (dále jen \"Google\"). Služba Google Analytics používá souborů cookies. Informace o užívání stránky spolu s obsahem souboru <a href=\"#cookies\">cookie</a> bude společností Google přenesen a uložen na serverech ve Spojených státech. Google bude užívat těchto informací pro účely vyhodnocování užívání stránky a vytváření zpráv o její aktivitě, určených pro její provozovatele, a pro poskytování dalších služeb týkajících se činností na stránce a užívání internetu vůbec. Google může také poskytnout tyto informace třetím osobám, bude-li to požadováno zákonem nebo budu-li takovéto třetí osoby zpracovávat tyto informace pro Google.</p>\r\n<p class=\"left\">Služba Google Analytics je rozšířena o související reklamní funkce poskytované společností Google, a to:</p>\r\n<ul class=\"left\">\r\n<li>přehledy zobrazení v reklamní síti Google,</li>\r\n<li>remarketing (zobrazování reklam v obsahové síti na základě zhlédnutých produktů),</li>\r\n<li>rozšířené demografické přehledy (reportování anonymních demografických dat).</li>\r\n</ul>\r\n<p class=\"left\">Více informací o zpracování a využití dat najdete ve <a href=\"http://www.google.com/intl/cs/policies/privacy/partners/\">smluvních podmínkách společnosti Google</a></p>\r\n<h2 class=\"left\">Jak zakázat sledování Google Analytics</h2>\r\n<p class=\"left\">Pokud nechcete poskytovat anonymní data o používání webu službě Google Analytics, můžete použít <a href=\"https://tools.google.com/dlpage/gaoptout\">plugin poskytovaný společností Google</a>. Po nainstalování do Vašeho prohlížeče a aktivaci nebudou dále data odesílána.</p>',0,'','','','','','{\"cookies\":[{\"title\":\"Cookies\",\"text\":\"<p><strong>Tato webová stránka používá cookies</strong></p>\\n<p>K personalizaci obsahu a reklam, poskytování funkcí sociálních médií a analýze naší návštěvnosti využíváme soubory cookie. Informace o tom, jak náš web používáte, sdílíme se svými partnery pro sociální média, inzerci a analýzy. Partneři tyto údaje mohou zkombinovat s dalšími informacemi, které jste jim poskytli nebo které získali v důsledku toho, že používáte jejich služby.</p>\",\"btnSetPreferences\":\"Nastavení preferencí cookies \",\"btnReject\":\"Odmítnout\",\"btnConsentAndContinuation\":\"Souhlasím a pokračovat\",\"consentsTitle\":\"Nastavení preferencí\",\"necessarilyLink\":\"Technické cookies\",\"necessarilyText\":\"<p>Nutné cookies pomáhají, aby byla webová stránka použitelná tak, že umožní základní funkce jako navigace stránky a přístup k zabezpečeným sekcím webové stránky. Webová stránka nemůže správně fungovat bez těchto cookies.</p>\",\"preferenceslLink\":\"Statistické cookies\",\"preferencesText\":\"<p>Statistické cookies pomáhají majitelům webových stránek, aby porozuměli, jak návštěvníci používají webové stránky. Anonymně sbírají a sdělují informace.</p>\",\"analyticsLink\":\"Analytické cookies\",\"analyticsText\":\"<p>Analytické cookies</p>\",\"marketingLink\":\"Marketingové cookies\",\"marketingText\":\"<p>Marketingové cookies jsou používány pro sledování návštěvníků na webových stránkách. Záměrem je zobrazit reklamu, která je relevantní a zajímavá pro jednotlivého uživatele a tímto hodnotnější pro vydavatele a inzerenty třetích stran.</p>\",\"btnConfirmSelected\":\"Potvrdit vybrané\",\"btnAcceptEverything\":\"Přijmout vše\"}]}','{}',NULL,NULL),(21,1,1,21,1,1,'1|',12,0,'eshop',0,'2018-09-05 17:11:03','2018-09-05 17:11:00',34,'2025-04-24 11:39:19',':Front:Catalog:default','catalog','2018-09-05 17:11:00','2118-09-05 17:11:00','E-shop','E-shop','E-shop a půjčovna','','',0,0,0,0,'Anotace','<p>Obsah</p>',1,'','','','','','{}','{}',NULL,0),(26,1,1,26,72,2,'1|72|',8,1,'resetPassword',0,'2018-02-08 02:46:27','2018-02-08 02:46:27',12,'2021-06-21 18:22:30',':Front:User:resetPassword','common','2018-02-08 02:46:27','2118-02-08 02:46:27','Reset hesla','Změna hesla','Změna hesla','','',1,0,0,1,'','',0,'','','','','','{}',NULL,NULL,NULL),(37,1,1,37,72,2,'1|72|',6,1,'userLogin',36,'2017-03-15 15:05:46','2017-03-15 15:05:46',2319,'2018-09-05 17:42:28',':Front:User:login','common','2017-03-15 15:05:46','2100-01-01 00:00:00','Přihlášení','Přihlášení','Přihlášení','','',1,0,0,0,'','',0,'',NULL,NULL,NULL,'',NULL,NULL,NULL,NULL),(40,1,1,40,43,2,'1|43|',3,1,'conditions',5,'2017-01-24 13:44:14','2017-01-24 13:44:14',4,'2019-01-14 10:40:50',':Front:Page:default','common','2017-01-24 13:44:14','2100-01-01 00:00:00','Obchodní podmínky','Obchodní podmínky','Obchodní podmínky','','',1,0,0,0,'Anotace','<p>Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Nunc dapibus tortor vel mi dapibus sollicitudin. Integer malesuada. Nullam justo enim, consectetuer nec, ullamcorper ac, vestibulum in, elit. Fusce tellus. Nunc dapibus tortor vel mi dapibus sollicitudin. Nulla est. Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Fusce tellus odio, dapibus id fermentum quis, suscipit id erat. Curabitur sagittis hendrerit ante. Sed convallis magna eu sem. Fusce suscipit libero eget elit. Nullam sit amet magna in magna gravida vehicula. Etiam neque. Nulla accumsan, elit sit amet varius semper, nulla mauris mollis quam, tempor suscipit diam nulla vel leo. Curabitur bibendum justo non orci. Phasellus faucibus molestie nisl.</p>\r\n<p>Maecenas sollicitudin. Curabitur sagittis hendrerit ante. Fusce dui leo, imperdiet in, aliquam sit amet, feugiat eu, orci. Praesent id justo in neque elementum ultrices. In laoreet, magna id viverra tincidunt, sem odio bibendum justo, vel imperdiet sapien wisi sed libero. Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas. Ut tempus purus at lorem. Etiam commodo dui eget wisi. Aenean placerat. Nulla accumsan, elit sit amet varius semper, nulla mauris mollis quam, tempor suscipit diam nulla vel leo. Morbi imperdiet, mauris ac auctor dictum, nisl ligula egestas nulla, et sollicitudin sem purus in lacus. Fusce consectetuer risus a nunc. Nunc dapibus tortor vel mi dapibus sollicitudin. Nunc tincidunt ante vitae massa. Duis risus. Praesent in mauris eu tortor porttitor accumsan. Nulla non arcu lacinia neque faucibus fringilla. Praesent id justo in neque elementum ultrices. In laoreet, magna id viverra tincidunt, sem odio bibendum justo, vel imperdiet sapien wisi sed libero. Nunc dapibus tortor vel mi dapibus sollicitudin.</p>\r\n<p>Etiam bibendum elit eget erat. Nunc auctor. Nullam dapibus fermentum ipsum. Quis autem vel eum iure reprehenderit qui in ea voluptate velit esse quam nihil molestiae consequatur, vel illum qui dolorem eum fugiat quo voluptas nulla pariatur? Maecenas lorem. Fusce suscipit libero eget elit. In dapibus augue non sapien. Fusce wisi. Nunc tincidunt ante vitae massa. Mauris tincidunt sem sed arcu. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Aliquam ante. Nulla pulvinar eleifend sem. Duis sapien nunc, commodo et, interdum suscipit, sollicitudin et, dolor. Duis bibendum, lectus ut viverra rhoncus, dolor nunc faucibus libero, eget facilisis enim ipsum id lacus. Mauris tincidunt sem sed arcu. Curabitur vitae diam non enim vestibulum interdum. Morbi leo mi, nonummy eget tristique non, rhoncus non leo.</p>\r\n<p>Cras elementum. Nam libero tempore, cum soluta nobis est eligendi optio cumque nihil impedit quo minus id quod maxime placeat facere possimus, omnis voluptas assumenda est, omnis dolor repellendus. Aenean vel massa quis mauris vehicula lacinia. Etiam ligula pede, sagittis quis, interdum ultricies, scelerisque eu. Integer rutrum, orci vestibulum ullamcorper ultricies, lacus quam ultricies odio, vitae placerat pede sem sit amet enim. Nulla quis diam. Nulla est. Aliquam erat volutpat. Cum sociis natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus. Nulla accumsan, elit sit amet varius semper, nulla mauris mollis quam, tempor suscipit diam nulla vel leo. Vivamus porttitor turpis ac leo. Etiam dui sem, fermentum vitae, sagittis id, malesuada in, quam. Fusce consectetuer risus a nunc. Nullam eget nisl. Mauris suscipit, ligula sit amet pharetra semper, nibh ante cursus purus, vel sagittis velit mauris vel metus. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>\r\n<p>Maecenas ipsum velit, consectetuer eu lobortis ut, dictum at dui. Etiam egestas wisi a erat. Nulla turpis magna, cursus sit amet, suscipit a, interdum id, felis. Nunc auctor. Proin in tellus sit amet nibh dignissim sagittis. Integer vulputate sem a nibh rutrum consequat. In dapibus augue non sapien. Proin pede metus, vulputate nec, fermentum fringilla, vehicula vitae, justo. Nunc auctor. Etiam ligula pede, sagittis quis, interdum ultricies, scelerisque eu. Etiam dictum tincidunt diam. Fusce dui leo, imperdiet in, aliquam sit amet, feugiat eu, orci.</p>',0,'',NULL,NULL,NULL,'','{}',NULL,NULL,NULL),(42,1,1,42,43,2,'1|43|',5,1,'search',1,'2015-07-29 09:42:10','2015-07-29 09:42:10',4,'2018-10-30 08:07:58',':Front:Search:default','common','2015-07-29 09:42:10','2100-01-01 00:00:00','Výsledky vyhledávání','Výsledky vyhledávání','Výsledky vyhledávání','','',1,0,0,0,'','',0,'',NULL,NULL,NULL,'','{}',NULL,NULL,NULL),(43,1,1,43,1,1,'1|',14,0,'',0,'2018-09-05 17:18:45','2018-09-05 17:18:45',2319,'2018-09-05 17:20:46',':Front:Page:default','common','2018-09-05 17:18:45','2118-09-05 17:18:45','Stránky mimo hlavní navigaci','Stránky mimo hlavní navigaci','Stránky mimo hlavní navigaci','','',0,0,0,0,'','',0,'',NULL,NULL,NULL,'',NULL,NULL,NULL,NULL),(72,1,1,72,1,1,'1|',13,0,'userSection',1,'2013-11-06 15:34:00','2013-11-06 15:34:00',34,'2025-04-24 11:48:06',':Front:User:default','common','2013-11-06 15:34:00','2100-01-01 00:00:00','Uživatelská sekce','Uživatelská sekce','Uživatelská sekce','','',0,0,0,0,'','<h2 class=\"text-center\">Děkujeme za přihlášení</h2>\r\n<p class=\"text-center\">Vítejte ve svém účtu.</p>',0,'http://wwww|www|','','','','https://www.youtube.com/watch?v=CPjyJiPNnmk|','{\"userMenuUnloggedUser\":[{\"tree\":37},{\"tree\":76},{\"tree\":74},{}],\"userMenuLoggedUser\":[{\"tree\":72},{\"tree\":75},{\"tree\":455},{\"tree\":456},{\"tree\":405}],\"userSideMenu\":[{\"tree\":72},{\"tree\":413},{\"tree\":75},{\"tree\":455},{\"tree\":456},{\"tree\":405}]}','{}',NULL,NULL),(74,1,1,74,72,2,'1|72|',7,1,'lostPassword',1,'2013-11-06 15:36:28','2013-11-06 15:36:28',9,'2021-06-25 11:31:04',':Front:User:lostPassword','common','2013-11-06 15:36:28','2100-01-01 00:00:00','Zapomenuté heslo','Zapomenuté heslo','Zapomenuté heslo','','',1,0,0,0,'Zapomněli jste své heslo? Nevadí, na e-mail vám pošleme nové.\r\nStačí zadat e-mail, který jste uvedli při registraci.\r\n','',0,'',NULL,NULL,NULL,'','{}',NULL,NULL,NULL),(75,1,1,75,72,2,'1|72|',1,1,'userProfil',1,'2013-11-06 17:17:48','2013-11-06 17:17:48',12,'2021-05-18 12:50:11',':Front:User:profil','common','2013-11-06 17:17:48','2100-01-01 00:00:00','Můj účet','Můj účet','Můj účet','','',1,0,0,1,'','',0,'','','','','','{}',NULL,NULL,NULL),(76,1,1,76,72,2,'1|72|',5,1,'registration',1,'2013-11-07 06:49:48','2013-11-07 06:49:48',4,'2018-10-23 17:22:53',':Front:User:registration','common','2013-11-07 06:49:48','2100-01-01 00:00:00','Registrace','Registrace','Registrace','','',1,0,0,0,'','',0,'',NULL,NULL,NULL,'','{}',NULL,NULL,NULL),(77,1,1,77,21,2,'1|21|',2,0,'eshop-1',0,'2018-09-06 12:43:30','2018-09-06 12:43:30',9,'2021-07-04 18:13:57',':Front:Catalog:default','catalog','2018-09-06 12:43:30','2118-09-06 12:43:30','Pobyt v přírodě','Pobyt v přírodě','Pobyt v přírodě','','',1,0,0,0,'Anotace','<p>Obsah</p>',1,'','<select> aaa','<select>','<select>','','{}',NULL,NULL,0),(78,1,1,78,21,2,'1|21|',4,0,'eshop-2',0,'2018-09-06 12:46:12','2018-09-06 12:46:12',9,'2021-07-04 18:15:11',':Front:Catalog:default','catalog','2018-09-06 12:46:12','2118-09-06 12:46:12','Airsoft','Airsoft','Airsoft','','',1,0,0,0,'','',1,'','','','','','{}',NULL,NULL,0),(94,1,1,94,78,3,'1|21|78|',1,1,'',0,'2018-10-10 09:54:13','2018-10-10 09:54:13',1,'2021-07-07 17:09:01',':Front:Catalog:default','catalog','2018-10-10 09:54:12','2118-10-10 09:54:12','Pušky','Pušky','Pušky','','',1,0,0,0,'','',0,'','Nože s hodnotou <select> samostatně a <značkou [manufacturer], která neexistuje> <s délkou čepele [select]>','Nože <značky [manufacturer]> <s délkou čepele [select] cm>','Nože <značky [manufacturer]> <s délkou čepele [select] cm>','','{}',NULL,NULL,0),(102,1,1,102,1,1,'1|',4,1,'aboutUs',0,'2018-10-10 13:56:24','2018-10-10 13:56:00',34,'2025-05-23 05:13:44',':Author:Front:Author:default','common','2021-07-08 17:00:00','2118-10-10 13:56:00','O nás, ale hlavně o vás','O nás','O nás, ale hlavně o vás','','',1,0,0,0,'Víme, že právo často popisuje svět včerejška, zatímco vy žijete v zítřku. My umíme vše sladit. Pro práci v pohybu, při které není čas ztrácet cokoli. Mluvíme tak, abychom si porozuměli. POužíváme technologie, abychom si vše usnadnili. \r\n','<h2>Heading h2</h2>\r\n<p>Suspendisse <a href=\"#\">link</a> vel nisi accumsan pretium. Etiam id massa ut neque iaculis auctor. Fermentum eu dictum risus consequat. Cras magna justo, iaculis non scelerisque eget, condimentum in tellus. Maecenas a sem quam. Suspendisse facilisis neque tempor enim imperdiet sodales. Vitae lobortis sapien vestibulum. Suspendisse vehicula libero vel nisi accumsan. Suspendisse vehicula libero vel nisi accumsan pretium. Etiam id massa ut neque iaculis auctor. Fermentum eu dictum risus consequat. Cras magna justo, iaculis non scelerisque eget, condimentum in tellus. Maecenas a sem quam. Suspendisse.</p>\r\n<p>Nam liber tempor cum soluta nobis eleifend option congue nihil imperdiet doming id quod mazim placerat facer possim assuiber tempor cum soluta nobis eleifend option congue nihil imperdiet doming id quod mazim placerat facer possim assum.</p>\r\n<figure><img src=\"/data/images-lg/20-superkoderi-oldrichhrb-1200px-01.jpg\" alt=\"\" />\r\n<figcaption>Image description</figcaption>\r\n</figure>\r\n<h3>Heading h3</h3>\r\n<ul>\r\n<li><strong>Etiam ante sem,</strong> porta a porttitor ut, varius varius metus.</li>\r\n<li><strong>Nunc eu felis</strong> quis metus volutpat pellentesque.</li>\r\n<li><strong>Duis gravida</strong> tincidunt enim sed cursus.</li>\r\n<li><strong>Nunc eu felis</strong> quis metus volutpat pellentesque.</li>\r\n<li><strong>Duis gravida</strong> tincidunt enim sed cursus.</li>\r\n</ul>\r\n<ol>\r\n<li><strong>Etiam ante sem,</strong> porta a porttitor ut, varius varius metus.</li>\r\n<li><strong>Nunc eu felis</strong> quis metus volutpat pellentesque.</li>\r\n<li><strong>Duis gravida</strong> tincidunt enim sed cursus.</li>\r\n<li><strong>Nunc eu felis</strong> quis metus volutpat pellentesque.</li>\r\n<li><strong>Duis gravida</strong> tincidunt enim sed cursus.</li>\r\n</ol>\r\n<p>Suspendisse <a href=\"#\">link</a> vel nisi accumsan pretium. Etiam id massa ut neque iaculis auctor. Fermentum eu dictum risus consequat. Cras magna justo, iaculis non scelerisque eget, condimentum in tellus. Maecenas a sem quam. Suspendisse facilisis neque tempor enim imperdiet sodales. Vitae lobortis sapien vestibulum. Suspendisse vehicula libero vel nisi accumsan. Suspendisse vehicula libero vel nisi accumsan pretium. Etiam id massa ut neque iaculis auctor. Fermentum eu dictum risus consequat. Cras magna justo, iaculis non scelerisque eget, condimentum in tellus. Maecenas a sem quam. Suspendisse.</p>\r\n<p>Nam liber tempor cum soluta nobis eleifend option congue nihil imperdiet doming id quod mazim placerat facer possim assuiber tempor cum soluta nobis eleifend option congue nihil imperdiet doming id quod mazim placerat facer possim assum.</p>\r\n<blockquote>\r\n<p><strong>Blockquote</strong> – Lorem ipsum dolor sit amet, consectetur adipisicing elit. Dolorum quia ipsa corrupti temporibus ratione voluptatibus, voluptatem eos culpa a, numquam suscipit deleniti veniam libero. Dicta soluta sint, officiis enim voluptate.</p>\r\n</blockquote>\r\n<p>Mirum est notare quam littera gothica, quam nunc putamus parum claram, anteposuerit litterarum formas humanitatis per seacula quarta decima et quinta decima. Eodem modo typi, qui nunc nobis videntur parum clari, fiant sollemnes in futurum.<br />Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis at vero eros et accumsan et iusto odio dignissim qui blandit praesent luptatum zzril delenit augue duis dolore te feugait nulla facilisi. Nam <strong>liber tempor cum soluta nobis</strong> eleifend option congue nihil imperdiet doming id quod mazim placerat facer possim assum.</p>\r\n<h4>Heading 4 úrovně</h4>\r\n<ol>\r\n<li>Lorem ipsum dolor sit amet.\r\n<ol>\r\n<li>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Facere consequuntur id quidem, expedita, dicta, eos temporibus incidunt, mollitia aspernatur vitae excepturi vel nam dolorem voluptates fuga assumenda reprehenderit? Aut, nesciunt.</li>\r\n<li>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Tenetur deleniti aliquid, voluptatibus ratione! Id consequatur aperiam iusto nam in beatae atque a, voluptate iste fugit ea ex officia architecto impedit?</li>\r\n</ol>\r\n</li>\r\n<li>Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod</li>\r\n<li>tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, uis nostrud exercitation ullamco laboris nisi ut iquip x ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse</li>\r\n<li>cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</li>\r\n</ol>\r\n<ul>\r\n<li>Lorem ipsum dolor sit amet.\r\n<ul>\r\n<li>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Facere consequuntur id quidem, expedita, dicta, eos temporibus incidunt, mollitia aspernatur vitae excepturi vel nam dolorem voluptates fuga assumenda reprehenderit? Aut, nesciunt.</li>\r\n<li>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Tenetur deleniti aliquid, voluptatibus ratione! Id consequatur aperiam iusto nam in beatae atque a, voluptate iste fugit ea ex officia architecto impedit?</li>\r\n</ul>\r\n</li>\r\n<li>Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod</li>\r\n<li>tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, uis nostrud exercitation ullamco laboris nisi ut iquip x ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse</li>\r\n<li>cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</li>\r\n</ul>\r\n<hr />\r\n<p>Nam liber tempor cum soluta nobis eleifend option congue nihil imperdiet doming id quod mazim placerat facer possim assum.</p>\r\n<dl>\r\n<dt>Definition List Title</dt>\r\n<dd>This is a definition list division.</dd>\r\n<dt>Definition List Title</dt>\r\n<dd>This is a definition list division.</dd>\r\n<dt>Definition List Title</dt>\r\n<dd>This is a definition list division.</dd>\r\n</dl>\r\n<h5>Heading 5 úrovně</h5>\r\n<p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Vitae odio minima nostrum, blanditiis maxime ex reiciendis laborum voluptatum molestiae sequi optio, repudiandae sit explicabo. Obcaecati neque eligendi, totam debitis aperiam!</p>\r\n<h6>Heading 6 úrovně</h6>\r\n<p><cite>Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis at vero eros et accumsan et iusto odio dignissim qui blandit praesent luptatum zzril delenit augue duis dolore te feugait nulla facilisi. Nam liber tempor cum soluta nobis eleifend option congue nihil imperdiet doming id quod mazim placerat facer possim assum.</cite></p>\r\n<h2>Tabular data</h2>\r\n<table border=\"0\"><caption>Table Caption</caption>\r\n<thead>\r\n<tr>\r\n<th>Table Heading 1</th>\r\n<th>Table Heading 2</th>\r\n<th>Table Heading 3</th>\r\n<th>Table Heading 4</th>\r\n<th>Table Heading 5</th>\r\n</tr>\r\n</thead>\r\n<tbody>\r\n<tr>\r\n<td>Table Cell 1</td>\r\n<td>Table Cell 2</td>\r\n<td>Table Cell 3</td>\r\n<td>Table Cell 4</td>\r\n<td>Table Cell 5</td>\r\n</tr>\r\n<tr>\r\n<td>Table Cell 1</td>\r\n<td>Table Cell 2</td>\r\n<td>Table Cell 3</td>\r\n<td>Table Cell 4</td>\r\n<td>Table Cell 5</td>\r\n</tr>\r\n<tr>\r\n<td>Table Cell 1</td>\r\n<td>Table Cell 2</td>\r\n<td>Table Cell 3</td>\r\n<td>Table Cell 4</td>\r\n<td>Table Cell 5</td>\r\n</tr>\r\n<tr>\r\n<td>Table Cell 1</td>\r\n<td>Table Cell 2</td>\r\n<td>Table Cell 3</td>\r\n<td>Table Cell 4</td>\r\n<td>Table Cell 5</td>\r\n</tr>\r\n</tbody>\r\n<tfoot>\r\n<tr>\r\n<th>Table Footer 1</th>\r\n<th>Table Footer 2</th>\r\n<th>Table Footer 3</th>\r\n<th>Table Footer 4</th>\r\n<th>Table Footer 5</th>\r\n</tr>\r\n</tfoot>\r\n</table>\r\n<h2>Headings</h2>\r\n<p>Heading 1</p>\r\n<h2>Heading 2</h2>\r\n<h3>Heading 3</h3>\r\n<h4>Heading 4</h4>\r\n<h5>Heading 5</h5>\r\n<h6>Heading 6</h6>\r\n<h2>Paragraphs</h2>\r\n<p>A paragraph (from the Greek paragraphos, “to write beside” or “written beside”) is a self-contained unit of a discourse in writing dealing with a particular point or idea. A paragraph consists of one or more sentences. Though not required by the syntax of any language, paragraphs are usually an expected part of formal writing, used to organize longer prose.</p>\r\n<h2>Blockquotes</h2>\r\n<blockquote>\r\n<p>A block quotation (also known as a long quotation or extract) is a quotation in a written document, that is set off from the main text as a paragraph, or block of text.</p>\r\n<p>It is typically distinguished visually using indentation and a different typeface or smaller size quotation. It may or may not include a citation, usually placed at the bottom.</p>\r\n<cite> <a href=\"#\"> Said no one, ever. </a> </cite></blockquote>\r\n<h2>Lists</h2>\r\n<h3>Definition list</h3>\r\n<dl>\r\n<dt>Definition List Title</dt>\r\n<dd>This is a definition list division.</dd>\r\n</dl>\r\n<h3>Ordered List</h3>\r\n<ol>\r\n<li>List Item 1</li>\r\n<li>List Item 2</li>\r\n<li>List Item 3</li>\r\n</ol>\r\n<h3>Unordered List</h3>\r\n<ul>\r\n<li>List Item 1</li>\r\n<li>List Item 2</li>\r\n<li>List Item 3</li>\r\n</ul>\r\n<h2>Horizontal rules</h2>\r\n<hr />\r\n<h2>Tabular data</h2>\r\n<table border=\"0\"><caption>Table Caption</caption>\r\n<thead>\r\n<tr>\r\n<th>Table Heading 1</th>\r\n<th>Table Heading 2</th>\r\n<th>Table Heading 3</th>\r\n<th>Table Heading 4</th>\r\n<th>Table Heading 5</th>\r\n</tr>\r\n</thead>\r\n<tbody>\r\n<tr>\r\n<td>Table Cell 1</td>\r\n<td>Table Cell 2</td>\r\n<td>Table Cell 3</td>\r\n<td>Table Cell 4</td>\r\n<td>Table Cell 5</td>\r\n</tr>\r\n<tr>\r\n<td>Table Cell 1</td>\r\n<td>Table Cell 2</td>\r\n<td>Table Cell 3</td>\r\n<td>Table Cell 4</td>\r\n<td>Table Cell 5</td>\r\n</tr>\r\n<tr>\r\n<td>Table Cell 1</td>\r\n<td>Table Cell 2</td>\r\n<td>Table Cell 3</td>\r\n<td>Table Cell 4</td>\r\n<td>Table Cell 5</td>\r\n</tr>\r\n<tr>\r\n<td>Table Cell 1</td>\r\n<td>Table Cell 2</td>\r\n<td>Table Cell 3</td>\r\n<td>Table Cell 4</td>\r\n<td>Table Cell 5</td>\r\n</tr>\r\n</tbody>\r\n<tfoot>\r\n<tr>\r\n<th>Table Footer 1</th>\r\n<th>Table Footer 2</th>\r\n<th>Table Footer 3</th>\r\n<th>Table Footer 4</th>\r\n<th>Table Footer 5</th>\r\n</tr>\r\n</tfoot>\r\n</table>\r\n<h2>Code</h2>\r\n<p><strong>Keyboard input:</strong> <kbd>Cmd</kbd></p>\r\n<p><strong>Inline code:</strong> <code>&lt;div&gt;code&lt;/div&gt;</code></p>\r\n<p><strong>Sample output:</strong> <samp>This is sample output from a computer program.</samp></p>\r\n<h2>Pre-formatted text</h2>\r\n<pre>P R E F O R M A T T E D T E X T\r\n! \" # $ % &amp; \' ( ) * + , - . /\r\n0 1 2 3 4 5 6 7 8 9 : ; &lt; = &gt; ?\r\n@ A B C D E F G H I J K L M N O\r\nP Q R S T U V W X Y Z [ \\ ] ^ _\r\n` a b c d e f g h i j k l m n o\r\np q r s t u v w x y z { | } ~ </pre>\r\n<h2>Inline elements</h2>\r\n<p><a href=\"#\">This is a text link</a>.</p>\r\n<p><strong>Strong is used to indicate strong importance.</strong></p>\r\n<p><em>This text has added emphasis.</em></p>\r\n<p><del>This text is deleted</del> and <ins>This text is inserted</ins>.</p>\r\n<p>This text has a strikethrough.</p>\r\n<p>Superscript<sup>®</sup>.</p>\r\n<p>Subscript for things like H<sub>2</sub>O.</p>\r\n<p>Abbreviation: <abbr title=\"HyperText Markup Language\">HTML</abbr></p>\r\n<p><q cite=\"https://developer.mozilla.org/en-US/docs/HTML/Element/q\">This text is a short inline quotation.</q></p>\r\n<p><cite>This is a citation.</cite></p>\r\n<p>The <dfn>dfn element</dfn> indicates a definition.</p>\r\n<p>The mark element indicates a highlight.</p>\r\n<p>The <var>variable element</var>, such as <var>x</var> = <var>y</var>.</p>\r\n<p>The time element: 2 weeks ago</p>\r\n<p><a class=\"btn\"><span class=\"btn__text\">Odkaz</span></a></p>\r\n<p><img src=\"/data/images-l/28-cameron-venti-pqyvyqqa87s-unsplash.jpeg\" alt=\"cameron-venti-pqyvyqqa87s-unsplash\" width=\"1024\" height=\"682\" /></p>',0,'','','','','https://www.youtube.com/watch?v=B8P2fewY4n4|Google mapy\nhttps://www.youtube.com/watch?v=o3_TEKZ-axw|When JavaScript bytes','{\"annot\":[{\"bg\":\"u-bgc-yellow\",\"btn\":[{\"toggle\":\"systemHref\"}],\"right\":[{\"image\":\"90\"}]}]}','{}',NULL,NULL),(255,1,1,255,43,2,'1|43|',6,1,'personalData',0,'2018-12-18 10:07:15','2018-12-18 10:07:15',4,'2019-03-05 09:09:48',':Front:Page:default','common','2018-12-18 10:07:14','2118-12-18 10:07:14','Zpracováním osobních údajů','Zpracováním osobních údajů','Zpracováním osobních údajů','','',1,0,0,0,'aaaa','<p>ddddddd</p>',0,'','','','','','{}',NULL,NULL,NULL),(394,1,1,394,398,2,'1|398|',1,1,'popupContact',0,'2019-03-21 13:35:53','2019-03-21 13:35:52',3,'2021-07-08 12:11:49',':Front:Internal:default','common','2019-03-21 13:35:52','2119-03-21 13:35:52','Popup','Popup','Popup','','',1,0,1,1,'','',0,'','','','','','{}',NULL,NULL,NULL),(398,1,1,398,1,1,'1|',15,0,'systemPageId',0,'2020-09-25 15:13:45','2020-09-25 15:13:44',3,'2021-07-08 12:12:18',':Front:Homepage:default','common','2020-09-25 15:13:44','2120-09-25 15:13:44','System pages (only develpers)','System pages (only develpers)','System pages (only develpers)','','',0,0,1,1,'','',0,'','','','','','{}',NULL,NULL,NULL),(405,1,1,405,72,2,'1|72|',1,1,'userLogout',0,'2021-05-19 12:36:35','2021-05-19 12:36:35',12,'2021-05-19 12:37:10',':Front:User:default','common','2021-05-19 12:36:35','2121-05-19 12:36:35','Odhlášení','Odhlásit','Odhlásit','','',1,1,1,1,'','',0,'','','','','','{}',NULL,NULL,NULL),(413,1,1,413,72,2,'1|72|',1,1,'userChangePassword',0,'2021-06-21 18:21:49','2021-06-21 18:21:49',12,'2021-06-21 19:06:46',':Front:User:default','common','2021-06-21 18:21:49','2121-06-21 18:21:49','Změna hesla','Změna hesla','Změna hesla','','',1,0,1,1,'','',0,'',NULL,NULL,NULL,'','{}',NULL,NULL,NULL),(445,1,1,445,398,2,'1|398|',1,1,'',0,'2021-07-14 17:23:51','2021-07-14 17:23:51',1,'2021-07-14 17:24:03',':Front:Page:styleguide','common','2021-07-14 17:23:51','2121-07-14 17:23:51','Styleguide','Styleguide','Styleguide','','',1,0,0,0,'','',0,'',NULL,NULL,NULL,'','{}',NULL,NULL,NULL),(446,1,1,446,1,1,'1|',7,0,'blog',0,'2021-08-16 16:41:04','2021-08-16 16:41:04',3,'2021-08-16 16:41:52',':Blog:Front:Blog:default','common','2021-08-16 16:41:04','2121-08-16 16:41:04','Blog','Blog','Blog','','',1,0,0,0,'','',0,'',NULL,NULL,NULL,'','{}',NULL,NULL,NULL),(449,1,1,449,1,1,'1|',1,0,'services',0,'2021-08-24 10:20:43','2021-08-24 10:20:00',34,'2025-05-29 15:26:40',':Feature:Front:Feature:servicesMain','common','2021-08-24 10:20:00','2121-08-24 10:20:00','S čím vám můžeme pomoct?','Služby','Služby','','',1,0,0,0,'annot-feature','',0,'',NULL,NULL,NULL,'','{}','{\"tabs____Iji2TcAPTLwav_PCPNsaj\":[{\"title\":\"Jak probíhá spolupráce s námi?\",\"items\":[{\"name\":\"Začneme\",\"content\":\"<h2>Poznáme vás a vaše potřeby</h2>\\n<p>Chcete s námi spolupracovat? Nejprve si domluvme setkání – zavolejte, napište e-mail nebo vyplňte kontaktní formulář na webu a řekněte nám ve stručnosti, co vás trápí. Schůzku naplánujeme tak, jak vám to nejvíc vyhovuje. Rádi se potkáváte osobně, nebo dáváte přednost online meetingům? Přizpůsobíme se vám.</p>\\n<p>Po schůzce vám pošleme doplňující otázky a všechno důkladně vysvětlíme tak, aby vám byl každý bod naší spolupráce jasný. Paragrafy jsou sice naše živobytí, ale v komunikaci je používáme střídmě, abychom vás nezahltili právničinou. Nemusíte se bát, že vás zavalíme odbornými termíny, zakládáme si na tom, aby vše dávalo smysl vám, i vašim kolegům.</p>\"},{\"name\":\"Promyslíme\",\"content\":\"<h2>Myslíme za vás a v souvislostech</h2>\\n<p>Hned jak od vás dostaneme všechny podklady, pustíme se do práce. Napříč právními týmy se zamyslíme nad všemi možnými souvislostmi. Nejenže projdeme, co je potřeba vyřešit podle zákona, ale zamyslíme se i nad tím, jak mohou právní otázky ovlivnit vaše podnikání. Vždycky se snažíme předvídat možná rizika a problémy, které by mohly přijít.</p>\\n<p>Pokud narazíme na něco, co by vás mohlo dostat do potíží, hned vás na to upozorníme a včas najdeme řešení. Naprosto totiž chápeme, že právní služby musí jít ruku v ruce s vašim podnikáním. Když je třeba, přicházíme s novými nápady, jak váš byznys lépe ochránit a posunout.</p>\"},{\"name\":\" Sepíšeme\",\"content\":\"<div id=\\\"tabs_5r2yf8ja9gj2c2is\\\" class=\\\" col-lg-12 col-md-12 col-sm-12 col-xs-12 element-class   element element-tabs\\\" data-element=\\\"tabs\\\" data-position=\\\"5r2yf8ja9gj2c2is\\\">\\n<div class=\\\"main\\\">\\n<div class=\\\"main row page-element-tabs__elements\\\">\\n<div id=\\\"wysiwyg_ts4qmvk4ydfqnafr\\\" class=\\\" col-lg-12 col-md-12 col-sm-12 col-xs-12 element-class   element element-wysiwyg\\\" data-element=\\\"wysiwyg\\\" data-position=\\\"ts4qmvk4ydfqnafr\\\">\\n<div class=\\\"page-element-wysiwyg-style wysiwyg-content\\\">\\n<h2>Píšeme tak, abyste tomu rozuměli</h2>\\n<p>Řekli jsme si vše podstatné? Jakmile máme jasno, připravíme první návrh dokumentace. Sami nemáme rádi těžkopádný styl komunikace, můžete si být tedy jistí, že naše smlouva bude přehledná, jasná a srozumitelná. Věříme totiž, že složitosti do smluv nepatří a chceme, aby našim výstupům rozuměli i neprávníci.</p>\\n<p>Respektujeme a zohledníme váš tone of voice. Dokumentaci můžeme připravit v lehkém přátelském jazyce, nebo právnicky formálně – jak si budete přát. Chcete, aby byl text vtipný nebo spíš seriózní? Umíme oboje. A pokud, stejně jako my, preferujete online spolupráci na úpravě dokumentů, revize nám půjdou pěkně od ruky.</p>\\n</div>\\n</div>\\n</div>\\n</div>\\n</div>\\n<div id=\\\"quotesection_pg7zsyo5lhrzg9s5\\\" class=\\\" col-lg-12 col-md-12 col-sm-12 col-xs-12 element-class   element element-quotesection\\\" data-element=\\\"quotesection\\\" data-position=\\\"pg7zsyo5lhrzg9s5\\\"></div>\"},{\"name\":\"Vyjednáme\",\"content\":\"<h2>Vyrážíme do světa</h2>\\n<p>Máme hotovo, dokumentace je na stole. Odpovídá zadání a respektuje vaše byznysové potřeby. Aby však nezůstala v šuplíku, pomůžeme vám i při vyjednávání s druhou stranou. Víme, že co právník, to jiný názor, a proto vám pomůžeme najít kompromis, který bude fungovat pro všechny. Hodnocení rizik a vyjednávání o smlouvě je naše silná disciplína.</p>\\n<p>Pokud potřebujete uzavřít smlouvu, můžete se na nás spolehnout, jsme na vaší straně. Aktivně za vás budeme komunikovat s úřady, soudy nebo jinými institucemi. Dokumenty, které připravíme, známe do posledního slova, a když přijdete s vlastními, rychle se v nich zorientujeme.</p>\"}]}],\"testimonial____01MtWuRxjvH8XiCad7fkK\":[{\"testimonial\":4}]}',NULL,NULL),(450,1,1,450,398,2,'1|398|',3,1,'precart',0,'2024-03-01 11:44:30','2024-03-01 11:44:00',35,'2024-03-01 12:03:59',':Front:Precart:default','common','2024-03-01 11:44:00','2124-03-01 11:44:00','Predkošík','Predkošík','Predkošík','','',1,1,1,0,'','',NULL,NULL,NULL,NULL,NULL,NULL,'{}','{}',NULL,NULL),(451,1,1,451,398,2,'1|398|',4,1,'cart',0,'2024-03-01 13:44:03','2024-03-01 13:44:00',35,'2024-03-01 13:44:29',':Front:Order:default','common','2024-03-01 13:44:00','2124-03-01 13:44:00','Košík','Košík','Košík','','',1,1,1,0,'','',NULL,NULL,NULL,NULL,NULL,NULL,'{}','{}',NULL,NULL),(452,1,1,450,398,2,'1|398|',5,1,'step1',0,'2024-03-14 09:28:31','2024-03-14 09:28:00',33,'2024-03-14 09:29:19',':Front:Order:step1','common','2024-03-14 09:28:00','2124-03-14 09:28:00','Objednávka 1. krok','Objednávka 1. krok','Objednávka 1. krok','','',1,1,1,0,'','',NULL,NULL,NULL,NULL,NULL,NULL,'{}','{}',NULL,NULL),(453,1,1,451,398,2,'1|398|',6,1,'step2',0,'2024-03-14 09:29:30','2024-03-14 09:29:00',33,'2024-03-14 09:30:12',':Front:Order:step2','common','2024-03-14 09:29:00','2124-03-14 09:29:00','Objednávka 2. krok','Objednávka 2. krok','Objednávka 2. krok','','',1,1,1,0,'','',NULL,NULL,NULL,NULL,NULL,NULL,'{}','{}',NULL,NULL),(454,1,1,452,398,2,'1|398|',7,1,'step3',0,'2024-03-14 09:30:15','2024-03-14 09:30:00',33,'2024-03-14 09:30:48',':Front:Order:step3','common','2024-03-14 09:30:00','2124-03-14 09:30:00','Objednávka dokončena','Objednávka dokončena','Objednávka dokončena','','',1,1,1,0,'','',NULL,NULL,NULL,NULL,NULL,NULL,'{}','{}',NULL,NULL),(455,1,1,451,72,2,'1|72|',9,1,'userAddress',0,'2024-03-20 08:56:26','2024-03-20 08:56:00',33,'2024-03-20 09:05:29',':Front:User:default','common','2024-03-20 08:56:00','2124-03-20 08:56:00','Moje adresy','Moje adresy','Moje adresy','','',1,1,1,0,'','',NULL,NULL,NULL,NULL,NULL,NULL,'{}','{}',NULL,NULL),(456,1,1,452,72,2,'1|72|',10,1,'userOrderHistory',0,'2024-03-20 08:58:38','2024-03-20 08:58:00',33,'2024-03-20 09:02:48',':Front:User:orderHistory','common','2024-03-20 08:58:00','2124-03-20 08:58:00','Historie objednávek','Historie objednávek','Historie objednávek','','',1,1,1,0,'','',NULL,NULL,NULL,NULL,NULL,NULL,'{}','{}',NULL,NULL),(457,1,1,453,72,2,'1|72|',11,1,'userOrderHistoryDetail',0,'2024-03-20 09:02:19','2024-03-20 09:02:00',33,'2024-03-20 09:06:13',':Front:User:orderHistory','common','2024-03-20 09:02:00','2124-03-20 09:02:00','Historie objednávek detail','Historie objednávek detail','Historie objednávek detail','','',1,1,1,0,'','',NULL,NULL,NULL,NULL,NULL,NULL,'{}','{}',NULL,NULL),(458,1,1,450,1,1,'1|',2,0,'forWho',0,'2025-04-24 11:41:47','2025-04-24 11:41:00',34,'2025-05-28 08:10:06',':Front:Page:default','common','2025-04-24 11:41:00','2125-04-24 11:41:00','Pro koho','Pro koho','Pro koho','','',1,0,0,0,'','',NULL,NULL,NULL,NULL,NULL,NULL,'{}','{}',NULL,NULL),(459,1,1,451,1,1,'1|',5,0,'career',0,'2025-04-24 11:42:34','2025-04-24 11:42:00',34,'2025-06-06 06:31:44',':Front:Page:career','common','2025-04-24 11:42:00','2125-04-24 11:42:00','Děláme právo  srozumitelně','Kariéra','Děláme právo  srozumitelně','','',1,0,0,0,'Lorem ipsum dolor sit amet, consectetuer adipiscing elit. In enim a arcu imperdiet malesuada. Donec vitae arcu. Nunc dapibus tortor vel mi dapibus sollicitudin.','',NULL,NULL,NULL,NULL,NULL,NULL,'{\"annot_career\":[{\"images\":[\"160\",\"158\",\"159\",\"157\"]}]}','{\"highlights____c2cyquCgQlnHVzk5tfoEp\":[{\"title\":\"Poznej naše hodnoty\",\"items\":[{\"image\":\"135\",\"name\":\"Profesionalita\",\"text\":\"Lorem ipsum dolor sit amet, consectetuer adipiscing elit. In enim a arcu imperdiet malesuada. Donec vitae arcu. Nunc dapibus tortor vel mi dapibus sollicitudin. \"},{\"image\":\"134\",\"name\":\"Týmová práce\",\"text\":\"Lorem ipsum dolor sit amet, consectetuer adipiscing elit. In enim a arcu imperdiet malesuada. Donec vitae arcu. Nunc dapibus tortor vel mi dapibus sollicitudin. \"},{\"image\":\"133\",\"name\":\"Vzdělávání\",\"text\":\"Lorem ipsum dolor sit amet, consectetuer adipiscing elit. In enim a arcu imperdiet malesuada. Donec vitae arcu. Nunc dapibus tortor vel mi dapibus sollicitudin. \"}]}],\"testimonial____Bahkmnx5wBbJIQHOeKA3B\":[{\"testimonial\":5}],\"highlights____YmixMvjAkNQ1wdIjXZirb\":[{\"title\":\"Práce, co ti sedne\",\"items\":[{\"image\":\"135\",\"name\":\"Advokát\",\"text\":\"Solve a problem or close a sale in real-time with chat. If no one is available, customers are seamlessly routed to email without confusion.\",\"link\":[{\"toggle\":\"systemHref\",\"systemHref\":[{\"page\":\"486\",\"hrefName\":\"To je přesně pro mě!\"}]}]},{\"image\":\"135\",\"name\":\"Markeťák\",\"text\":\"Solve a problem or close a sale in real-time with chat. If no one is available, customers are seamlessly routed to email without confusion.\",\"link\":[{\"toggle\":\"systemHref\",\"systemHref\":[{\"page\":\"486\",\"hrefName\":\"To je přesně pro mě!\"}]}]},{\"image\":\"135\",\"name\":\"Právní koncipient/ka\",\"text\":\"Solve a problem or close a sale in real-time with chat. If no one is available, customers are seamlessly routed to email without confusion.\",\"link\":[{\"toggle\":\"systemHref\",\"systemHref\":[{\"page\":\"486\",\"hrefName\":\"To je přesně pro mě!\"}]}]}]}]}',NULL,NULL),(460,1,1,452,1,1,'1|',6,1,'references',0,'2025-04-24 11:43:00','2025-04-24 11:43:00',34,'2025-06-06 14:54:33',':Reference:Front:Reference:default','common','2025-04-24 11:43:00','2125-04-24 11:43:00','Inspirativní příběhy  našich klientů','Reference','Reference','','',1,0,0,0,'Lorem ipsum dolor sit amet, consectetuer adipiscing elit. In enim a arcu imperdiet malesuada. Donec vitae arcu. Nunc dapibus tortor vel mi dapibus sollicitudin.','',NULL,NULL,NULL,NULL,NULL,NULL,'{\"annot\":[{\"bg\":\"u-bgc-green\",\"btn\":[{\"toggle\":\"customHref\",\"customHref\":[{\"href\":\"#\",\"hrefName\":\"Chci vyřešit ochranu značky\"}]}],\"right\":[{\"link\":\"https://www.youtube.com/watch?v=FOIZ86CDW8I\"}]}]}','{\"logos____NPUW3vks2UibxCPdXJykp\":[{\"images\":[\"132\",\"130\",\"123\",\"122\",\"118\",\"119\",\"120\",\"121\",\"116\",\"117\"]}]}',NULL,NULL),(461,1,1,453,1,1,'1|',8,1,'action',0,'2025-04-24 11:43:47','2025-04-24 11:43:00',34,'2025-06-05 14:53:44',':Material:Front:Material:default','common','2025-04-24 11:43:00','2125-04-24 11:43:00','Vyberte si, co vás zajímá','Akce','Akce','','',1,0,0,0,'Lorem ipsum dolor sit amet, consectetuer adipiscing elit. In enim a arcu imperdiet malesuada. Donec vitae arcu. Nunc dapibus tortor vel mi dapibus sollicitudin.','',NULL,NULL,NULL,NULL,NULL,NULL,'{\"annot\":[{\"bg\":\"u-bgc-green\",\"btn\":[{\"toggle\":\"customHref\",\"customHref\":[{\"href\":\"#\",\"hrefName\":\"Chci vyřešit ochranu značky\"}]}],\"right\":[{\"link\":\"https://www.youtube.com/watch?v=FOIZ86CDW8I\"}]}]}','{}',NULL,NULL),(462,1,1,454,1,1,'1|',10,1,'contact',0,'2025-04-24 14:21:35','2025-04-24 14:21:00',34,'2025-05-15 12:01:27',':Front:Page:contact','common','2025-04-24 14:21:00','2125-04-24 14:21:00','Proberte to s námi','Kontakt','Kontakt','','',1,0,0,0,'','',NULL,NULL,NULL,NULL,NULL,NULL,'{\"branches\":[{\"title\":\"Najdete nás\",\"items\":[{\"image\":\"91\",\"city\":\"Praha\",\"address\":\"Opletalova 1015/55\",\"hours\":\"pondělí – pátek\\nod 9:00 do 18:00\",\"map\":\"https://maps.app.goo.gl/ieWR9Q52PzCiG7Wp7\"},{\"image\":\"93\",\"city\":\"Brno\",\"address\":\"Purkyňova 648/125\",\"hours\":\"pondělí – pátek\\nod 9:00 do 18:00\",\"map\":\"https://maps.app.goo.gl/ffTiH1CgbTU2HktM7\"},{\"image\":\"92\",\"city\":\"Ostrava\",\"address\":\"Purkyňova 648/125\",\"hours\":\"pondělí – pátek\\ndle domluvy\",\"map\":\"https://maps.app.goo.gl/ffTiH1CgbTU2HktM7\"}]}],\"socials\":[{\"facebook\":\"https://www.facebook.com/sedlakovalegal\",\"linkedin\":\"https://www.linkedin.com/company/sedlakova-legal\",\"youtube\":\"https://www.youtube.com/@sedlakovalegal\"}],\"contact_info\":[{\"phone\":\"+*********** 958\",\"mail\":\"<EMAIL>\",\"address\":\"SEDLAKOVA LEGAL s. r. o.\\nPurkyňova 648/125\\nBrno-Medlánky, 612 00\",\"ico\":\"05669871\",\"dic\":\"**********\"}]}','{}',NULL,NULL),(463,1,1,455,43,2,'1|43|',1,1,'',0,'2025-04-25 09:42:03','2025-04-25 09:42:00',34,'2025-04-25 09:42:55',':Front:Page:default','common','2025-04-25 09:42:00','2125-04-25 09:42:00','E-book','E-book','E-book','','',1,0,0,0,'','',NULL,NULL,NULL,NULL,NULL,NULL,'{}','{}',NULL,NULL),(464,1,1,456,43,2,'1|43|',2,1,'',0,'2025-04-25 09:45:11','2025-04-25 09:45:00',34,'2025-04-25 09:45:21',':Front:Page:default','common','2025-04-25 09:45:00','2125-04-25 09:45:00','Podcast','Podcast','Podcast','','',1,0,0,0,'','',NULL,NULL,NULL,NULL,NULL,NULL,'{}','{}',NULL,NULL),(465,1,1,457,43,2,'1|43|',7,1,'',0,'2025-04-25 09:48:11','2025-04-25 09:48:00',34,'2025-04-25 09:48:20',':Front:Page:default','common','2025-04-25 09:48:00','2125-04-25 09:48:00','Softwarové právo','Softwarové právo','Softwarové právo','','',1,0,0,0,'','',NULL,NULL,NULL,NULL,NULL,NULL,'{}','{}',NULL,NULL),(466,1,1,458,43,2,'1|43|',8,1,'',0,'2025-04-25 09:48:41','2025-04-25 09:48:00',34,'2025-04-25 09:48:49',':Front:Page:default','common','2025-04-25 09:48:00','2125-04-25 09:48:00','Kybernetická bezpečnost','Kybernetická bezpečnost','Kybernetická bezpečnost','','',1,0,0,0,'','',NULL,NULL,NULL,NULL,NULL,NULL,'{}','{}',NULL,NULL),(467,1,1,459,43,2,'1|43|',9,1,'',0,'2025-04-25 09:49:07','2025-04-25 09:49:00',34,'2025-04-25 09:49:16',':Front:Page:default','common','2025-04-25 09:49:00','2125-04-25 09:49:00','Právo duševního vlastnictví','Právo duševního vlastnictví','Právo duševního vlastnictví','','',1,0,0,0,'','',NULL,NULL,NULL,NULL,NULL,NULL,'{}','{}',NULL,NULL),(468,1,1,460,43,2,'1|43|',10,1,'',0,'2025-04-25 09:49:43','2025-04-25 09:49:00',34,'2025-04-25 09:49:52',':Front:Page:default','common','2025-04-25 09:49:00','2125-04-25 09:49:00','Nemovitosti','Nemovitosti','Nemovitosti','','',1,0,0,0,'','',NULL,NULL,NULL,NULL,NULL,NULL,'{}','{}',NULL,NULL),(469,1,1,461,43,2,'1|43|',11,1,'',0,'2025-04-25 09:50:07','2025-04-25 09:50:00',34,'2025-04-25 09:50:16',':Front:Page:default','common','2025-04-25 09:50:00','2125-04-25 09:50:00','Korporátní právo','Korporátní právo','Korporátní právo','','',1,0,0,0,'','',NULL,NULL,NULL,NULL,NULL,NULL,'{}','{}',NULL,NULL),(470,1,1,462,43,2,'1|43|',12,1,'',0,'2025-04-25 09:50:33','2025-04-25 09:50:00',34,'2025-04-25 09:50:40',':Front:Page:default','common','2025-04-25 09:50:00','2125-04-25 09:50:00','Pracovní právo','Pracovní právo','Pracovní právo','','',1,0,0,0,'','',NULL,NULL,NULL,NULL,NULL,NULL,'{}','{}',NULL,NULL),(471,1,1,463,458,2,'1|458|',1,1,'',0,'2025-05-05 14:21:19','2025-05-05 14:21:00',34,'2025-05-30 09:00:50',':Front:Page:default','common','2025-05-05 14:21:00','2125-05-05 14:21:00','Velké a střední firmy','Velké a střední firmy','Velké a střední firmy','','',1,0,0,0,'Lorem ipsum','',NULL,NULL,NULL,NULL,NULL,NULL,'{\"annot\":[{\"bg\":\"u-bgc-yellow\",\"btn\":[{\"toggle\":\"systemHref\"}],\"right\":[{\"image\":\"90\"}]}]}','{\"highlights____fId2WTzPCv5xfLAY3IlSj\":[{\"items\":[{\"image\":\"151\",\"name\":\"Komplexnost\",\"text\":\"Vždycky je lepší poznat firmu ze všech úhlů a všechny právní věci spojit do jednoho fungujícího celku. I když máte svého firemního právníka, těžko dokáže pokrýt všechno.\"},{\"image\":\"153\",\"name\":\"Byznysový insight\",\"text\":\"Vysvětlíme vám smlouvy a paragrafy tak, abyste rozuměli tomu, co říkáme, ale hlavně abyste věděli, jaký dopad to celé bude mít na váš byznys. A to všechno responzivně, do 24 hodin.\"},{\"image\":\"152\",\"name\":\"Léta zkušeností s firmami, jako je ta vaše\",\"text\":\"Učíme se od velkých – mezi našimi klienty najdete banky, mezinárodní přepravce i výrobní giganty. Jak v Česku, tak po celé Evropské unii, ale i mimo ni.\"}]}]}',NULL,NULL),(472,1,1,464,458,2,'1|458|',10,1,'',0,'2025-05-05 14:21:32','2025-05-05 14:21:00',34,'2025-05-30 10:27:16',':Front:Page:forWho','common','2025-05-05 14:21:00','2125-05-05 14:21:00','Technologické firmy','Technologické firmy','Technologické firmy','','',1,0,0,0,'Ochrana duševního vlastnictví, licenční smlouvy nebo právní compliance – máme to pokryté. Nemusíte se bát, že vašim technologiím nebudeme stačit.','',NULL,NULL,NULL,NULL,NULL,NULL,'{\"annot\":[{\"bg\":\"u-bgc-yellow\",\"btn\":[{\"toggle\":\"systemHref\",\"systemHref\":[{\"page\":462,\"hrefName\":\"Pojďme to probrat\"}]}],\"right\":[{\"image\":\"154\"}]}],\"blogs\":[{\"title\":\"Podobným tématům se věnujeme i na našem blogu\",\"items\":[{\"blog\":1},{\"blog\":2}]}]}','{\"highlights____bYa8AWbq_rPzcvYVbfzOZ\":[{\"title\":\"Proč si technologické firmy vybírají ke spolupráci právě nás?\",\"items\":[{\"image\":\"153\",\"name\":\"8 let mezi špičkami v IT právu\",\"text\":\"Za tu dobu na našich smlouvách vyrostli malí inovátoři v globální hráče. Důvěřují nám vývojářská studia, SaaS startupy i hardwaroví průkopníci. Děláme svět technologičtější.\"},{\"image\":\"152\",\"name\":\"Rozumíme vašemu jazyku\",\"text\":\"Když řeknete, že vyvíjíte agilně, zeptáme se na projektové řízení. Když zmíníte open-source, půjdeme po licenčních podmínkách. IT je pro nás stejně srozumitelné, jako naše smlouvy pro vás.\"},{\"image\":\"151\",\"name\":\"Jsme součástí tech komunit\",\"text\":\"Nejsme jen právníci, co píší a vyjednávají smlouvy. Publikujeme, přednášíme, mentorujeme, pořádáme eventy pro technologické firmy, zakladatele a klíčové osobnosti byznysu.\"}]}],\"testimonial____TgClNcFypU71MHg3hB1kE\":[{\"testimonial\":\"2\"}]}',NULL,NULL),(473,1,1,465,458,2,'1|458|',2,1,'',0,'2025-05-05 14:21:46','2025-05-05 14:21:00',5,'2025-05-05 14:21:49',':Front:Page:default','common','2025-05-05 14:21:00','2125-05-05 14:21:00','Startupy a investoři','Startupy a investoři','Startupy a investoři','','',1,0,0,0,'','',NULL,NULL,NULL,NULL,NULL,NULL,'{}','{}',NULL,NULL),(474,1,1,466,458,2,'1|458|',3,1,'',0,'2025-05-05 14:21:55','2025-05-05 14:21:00',5,'2025-05-05 14:21:58',':Front:Page:default','common','2025-05-05 14:21:00','2125-05-05 14:21:00','Realitní kanceláře a developeři','Realitní kanceláře a developeři','Realitní kanceláře a developeři','','',1,0,0,0,'','',NULL,NULL,NULL,NULL,NULL,NULL,'{}','{}',NULL,NULL),(475,1,1,467,458,2,'1|458|',4,1,'',0,'2025-05-05 14:22:06','2025-05-05 14:22:00',5,'2025-05-05 14:22:09',':Front:Page:default','common','2025-05-05 14:22:00','2125-05-05 14:22:00','Vývojáři a IT','Vývojáři a IT','Vývojáři a IT','','',1,0,0,0,'','',NULL,NULL,NULL,NULL,NULL,NULL,'{}','{}',NULL,NULL),(476,1,1,468,458,2,'1|458|',5,1,'',0,'2025-05-05 14:22:17','2025-05-05 14:22:00',5,'2025-05-05 14:22:20',':Front:Page:default','common','2025-05-05 14:22:00','2125-05-05 14:22:00','Marketing','Marketing','Marketing','','',1,0,0,0,'','',NULL,NULL,NULL,NULL,NULL,NULL,'{}','{}',NULL,NULL),(477,1,1,469,458,2,'1|458|',6,1,'',0,'2025-05-05 14:22:27','2025-05-05 14:22:00',5,'2025-05-05 14:22:29',':Front:Page:default','common','2025-05-05 14:22:00','2125-05-05 14:22:00','HR a backoffice','HR a backoffice','HR a backoffice','','',1,0,0,0,'','',NULL,NULL,NULL,NULL,NULL,NULL,'{}','{}',NULL,NULL),(478,1,1,470,458,2,'1|458|',7,1,'',0,'2025-05-05 14:22:37','2025-05-05 14:22:00',5,'2025-05-05 14:22:40',':Front:Page:default','common','2025-05-05 14:22:00','2125-05-05 14:22:00','Sales a finance','Sales a finance','Sales a finance','','',1,0,0,0,'','',NULL,NULL,NULL,NULL,NULL,NULL,'{}','{}',NULL,NULL),(479,1,1,471,458,2,'1|458|',8,1,'',0,'2025-05-05 14:22:48','2025-05-05 14:22:00',5,'2025-05-05 14:22:50',':Front:Page:default','common','2025-05-05 14:22:00','2125-05-05 14:22:00','Security a compliance','Security a compliance','Security a compliance','','',1,0,0,0,'','',NULL,NULL,NULL,NULL,NULL,NULL,'{}','{}',NULL,NULL),(480,1,1,472,458,2,'1|458|',9,1,'',0,'2025-05-05 14:22:57','2025-05-05 14:22:00',5,'2025-05-05 14:23:00',':Front:Page:default','common','2025-05-05 14:22:00','2125-05-05 14:22:00','Legal','Legal','Legal','','',1,0,0,0,'','',NULL,NULL,NULL,NULL,NULL,NULL,'{}','{}',NULL,NULL),(481,2,2,8,2,1,'2|',8,1,'faq',40,'2017-01-27 14:53:52','2017-01-27 14:53:00',34,'2025-05-07 14:49:53',':Front:Page:faq','common','2021-07-08 17:00:00','2100-01-01 00:00:00','FAQ','FAQ','FAQ','','',0,0,0,0,'','',NULL,NULL,'','','',NULL,'{\"faq\":[{\"items\":[{\"question\":\"Můžu s vámi realizovat schůzku i vzdáleně? \",\"answer\":\"<p><span>Určitě můžete, tento způsob komunikace s klienty využíváme již od vzniku naší kanceláře. Můžeme se spojit přes Skype, Microsoft Teams, Zoom, Google Hangouts, Calendly i další aplikace. Samozřejmě je možnost spojit se i telefonicky a realizovat tímto způsobem celou schůzku.</span></p>\"},{\"question\":\"Kolik mě vaše právní pomoc bude stát?\",\"answer\":\"<p><span>Váš případ s vámi vždy nejprve zkonzultujeme, tato první konzultace je zdarma. Následně vám připravíme cenovou nabídku na míru vašim požadavkům. Pokud je to jen trochu možné, vždy vám před započetím prací sdělíme pevnou částku, kterou vám budeme účtovat. V některých případech však nelze částku odhadnout, jde převážně o pravidelné konzultace či zastupování před soudem. V takovém případě účtujeme hodinovou sazbou, která se pohybuje v rozsahu 2 800 - 3 200 Kč + DPH. Pokud víte, že naše služby budete potřebovat dlouhodobě a pravidelně, lze se domluvit i na paušální měsíční úhradě. V takovém případě máte vždy jistotu, že vám předem dohodnutý počet hodin v daném měsíci budeme věnovat.</span></p>\"},{\"question\":\"Máte smlouvu? A mohl bych ji vidět?\",\"answer\":\"<p><span>Ano, máme smlouvu o poskytování právních služeb, kterou můžeme společně uzavřít. Níže naleznete odkaz, kde se můžete na naši smlouvu můžete podívat, ať víte, co můžete očekávat.</span></p>\\n<p><span><a class=\\\"btn \\\" href=\\\"#\\\"> <span class=\\\"btn__text\\\"><span>Smlouva o poskytování právních služeb -&gt;</span></span></a></span></p>\"},{\"question\":\"Musím vám něco zaplatit předem?\",\"answer\":\"<p><span>Většinou ne ? . Náš vztah je založený na důvěře a vaše slovo (a podepsaná </span><a rel=\\\"noopener\\\" href=\\\"https://www.sedlakovalegal.cz/uploads/AB5z8B8fdu805Bpb3OA6sC1T7mncrwjB.pdf\\\" target=\\\"_blank\\\">smlouva o poskytování právních služeb ↗</a><span>) nám stačí. Zálohu vyžadujeme pouze pokud bychom měli s prací pro vás zvýšené hotové výdaje (například pokud vám zajišťujeme právní pomoc od našich zahraničních kolegů), nebo pokud se jedná o opravdu rozsáhlý projekt na několik měsíců.</span></p>\"},{\"question\":\"Potřebuji poradit, za jak dlouho mi odpovíte?\",\"answer\":\"<p><span>Na dotazy reagujeme v pracovní dny vždy do 24h od jejich obdržení. U složitých a časově náročných případů samozřejmě není možné je v této době zpracovat, ve lhůtě 24h od nás však vždy obdržíte odpověď s návrhem dalšího postupu a odhadovanou časovou náročností. I ve složitých věcech však uděláme vše pro to, abyste měli první výstup z naší strany nejpozději do týdne od potvrzení Vašeho zájmu o naše služby, případě od uhrazení zálohy. V jednodušších případech obvykle zasíláme výstup do 3 pracovních dnů.</span></p>\"},{\"question\":\"Mám urgentní problém a je nepracovní den, odpovíte mi?\",\"answer\":\"<p><span>Děláme vše pro to, abychom našim klientům byli v urgentních případech k dispozici i v nepracovní dny. Pokud tedy máte problém, který nepočká, můžete nám vždy napsat e-mail na </span><a href=\\\"mailto:<EMAIL>\\\"><EMAIL></a><span>. Zprávu lze zaslat i přímo na Facebooku na náš profil </span><a href=\\\"https://www.facebook.com/sedlakovalegal/\\\">SEDLAKOVA LEGAL ↗<span> </span></a><span>.</span></p>\"},{\"question\":\"Máte newsletter?\",\"answer\":\"<p>Máme <a class=\\\"btn \\\" rel=\\\"noopener\\\" href=\\\"https://sedlakovalegal.ecomailapp.cz/public/form/3-b6fcc542fb021c84fdaff536dd0a74a1\\\" target=\\\"_blank\\\"> <span class=\\\"btn__text\\\"><span>Tady -&gt;</span></span></a></p>\"}]}]}','{\"faq____bdiCgV28GtKrD3NQpzUAN\":[{\"items\":[{\"question\":\"1 otazka\",\"answer\":\"<p>dasf</p>\"}]}]}',NULL,NULL),(482,1,1,473,43,2,'1|43|',13,1,'',0,'2025-05-13 20:42:22','2025-05-13 20:42:00',11,'2025-05-13 20:42:26',':Front:Page:default','common','2025-05-13 20:42:00','2125-05-13 20:42:00','Vnitřní oznamovací systém','Vnitřní oznamovací systém','Vnitřní oznamovací systém','','',1,0,0,0,'','',NULL,NULL,NULL,NULL,NULL,NULL,'{\"annot\":[{\"bg\":\"u-bgc-yellow\",\"btn\":[{\"toggle\":\"systemHref\"}]}]}','{}',NULL,NULL),(483,1,1,474,1,1,'1|',11,1,'abroad',0,'2025-05-13 20:42:44','2025-05-13 20:42:00',34,'2025-06-05 11:37:01',':Front:Page:abroad','common','2025-05-13 20:42:00','2125-05-13 20:42:00','Zahraničí','Zahraničí','Zahraničí','','',1,0,0,0,'Lorem ipsum dolor sit amet, consectetuer adipiscing elit. In enim a arcu imperdiet malesuada. Donec vitae arcu. Nunc dapibus tortor vel mi dapibus sollicitudin.','',NULL,NULL,NULL,NULL,NULL,NULL,'{\"annot\":[{\"bg\":\"u-bgc-yellow\",\"btn\":[{\"toggle\":\"customHref\",\"systemHref\":[{\"hrefName\":\"Chci řešit zahraničí ->\"}],\"customHref\":[{\"href\":\"#contact\",\"hrefName\":\"Chci řešit zahraničí\"}]}],\"right\":[{\"link\":\"https://www.youtube.com/watch?v=FOIZ86CDW8I\"}]}],\"branches_map\":[{\"title\":\"Jsme tam, kam vás byznys zavede\",\"annot\":\"Poskytujeme právní služby po celém světě s lokálním porozuměním a mezinárodní perspektivou\",\"branches\":[{\"name\":\"Česká republika\",\"count\":\"2\"},{\"name\":\"Slovensko\"},{\"name\":\"Maďarsko\"},{\"name\":\"Německo\"},{\"name\":\"USA\"},{\"name\":\"Kanada\"},{\"name\":\"Irsko\"},{\"name\":\"Vietnam\"},{\"name\":\"Indonesie\"}],\"image\":\"88\",\"numbers\":[{\"number\":\"9\",\"text1\":\"zemí\",\"text2\":\"a globální síť kanceláří\"},{\"number\":\"8\",\"text1\":\"let zkušeností\",\"text2\":\"a stovky spokojených klientů\"},{\"number\":\"30\",\"text1\":\"advokátů\",\"text2\":\"a tým expertů ze světa\"}]}]}','{\"highlights____pw7dIBaxGNFUEPa8kdTfh\":[{\"title\":\"S čím pomůžeme\",\"items\":[{\"image\":\"85\",\"name\":\"Highlight 1\",\"text\":\"Solve a problem or close a sale in real-time with chat. If no one is available, customers are seamlessly routed to email without confusion.\"},{\"name\":\"Highlight 2\",\"text\":\"Solve a problem or close a sale in real-time with chat. If no one is available.\"},{\"image\":\"85\",\"name\":\"Highlight 3\",\"text\":\"Solve a problem or close a sale in real-time with chat. If no one is available, customers are seamlessly routed to email without confusion.\"}]}],\"branches_map____SqEZddAmTAl_MO9qdNP8u\":[{\"show\":true}],\"casestudies_____3yIMVGx7gXppEvZ_qz8k\":[{\"title\":\"Nadpis\",\"annot\":\"Tady patří popis sekce\",\"items\":[{\"blog\":4},{\"blog\":3},{\"blog\":2},{\"blog\":1}]}],\"partners____PaITs2NiODMExHH49Fnfb\":[{\"title\":\"Naši zahraniční partneři\",\"items\":[{\"image\":\"89\",\"name\":\"Jméno Příjmení\",\"position\":\"Of-Counsel\",\"country\":\"GERMANY\"}]}]}',NULL,NULL),(484,1,1,475,43,2,'1|43|',14,1,'ccDemo',0,'2025-05-13 20:47:50','2025-05-13 20:47:00',34,'2025-06-06 08:14:02',':Front:Page:default','common','2025-05-13 20:47:00','2125-05-13 20:47:00','Rozcestník komponent','Rozcestník komponent','Rozcestník komponent','','',1,0,0,0,'Zde naleznete komponenty, které je možné využívat v rámci Jazykově závislého modulárního obsahu','',NULL,NULL,NULL,NULL,NULL,NULL,'{\"annot\":[{\"bg\":\"u-bgc-yellow\",\"btn\":[{\"toggle\":\"systemHref\"}],\"right\":[{\"image\":\"87\"}]}]}','{\"highlights____oStFuhXyC2fCCHciVjFXr\":[{\"title\":\"S čím pomůžeme\",\"items\":[{\"image\":\"85\",\"name\":\"Highlight 1\",\"text\":\"Solve a problem or close a sale in real-time with chat. If no one is available, customers are seamlessly routed to email without confusion.\"},{\"name\":\"Highlight 2\",\"text\":\"Solve a problem or close a sale in real-time with chat. If no one is available.\"},{\"image\":\"85\",\"name\":\"Highlight 3\",\"text\":\"Solve a problem or close a sale in real-time with chat. If no one is available, customers are seamlessly routed to email without confusion.\"}]}],\"content____2BE36hTyOmuXBaOjDM9A0\":[{\"content\":\"<h2>Heading 2</h2>\\n<h3>Heading 3</h3>\\n<h4>Heading 4</h4>\\n<h5>Heading 5</h5>\\n<h6>Heading 6</h6>\\n<p><span>Solve a problem or close a sale in real-time with chat. If no one is available, customers are seamlessly routed to email without confusion.</span></p>\\n<blockquote>Obsah blokové citace</blockquote>\\n<ol>\\n<li>Lorem ipsum dolor sit amet.\\n<ol>\\n<li>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Facere consequuntur id quidem, expedita, dicta, eos temporibus incidunt, mollitia aspernatur vitae excepturi vel nam dolorem voluptates fuga assumenda reprehenderit? Aut, nesciunt.</li>\\n<li>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Tenetur deleniti aliquid, voluptatibus ratione! Id consequatur aperiam iusto nam in beatae atque a, voluptate iste fugit ea ex officia architecto impedit?</li>\\n</ol>\\n</li>\\n<li>Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod</li>\\n<li>tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, uis nostrud exercitation ullamco laboris nisi ut iquip x ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse</li>\\n<li>cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</li>\\n</ol>\\n<ul>\\n<li>Lorem ipsum dolor sit amet.\\n<ul>\\n<li>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Facere consequuntur id quidem, expedita, dicta, eos temporibus incidunt, mollitia aspernatur vitae excepturi vel nam dolorem voluptates fuga assumenda reprehenderit? Aut, nesciunt.</li>\\n<li>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Tenetur deleniti aliquid, voluptatibus ratione! Id consequatur aperiam iusto nam in beatae atque a, voluptate iste fugit ea ex officia architecto impedit?</li>\\n</ul>\\n</li>\\n<li>Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod</li>\\n<li>tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, uis nostrud exercitation ullamco laboris nisi ut iquip x ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse</li>\\n<li>cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</li>\\n</ul>\\n<table border=\\\"0\\\"><caption>Table Caption</caption>\\n<thead>\\n<tr>\\n<th>Table Heading 1</th>\\n<th>Table Heading 2</th>\\n<th>Table Heading 3</th>\\n<th>Table Heading 4</th>\\n<th>Table Heading 5</th>\\n</tr>\\n</thead>\\n<tbody>\\n<tr>\\n<td>Table Cell 1</td>\\n<td>Table Cell 2</td>\\n<td>Table Cell 3</td>\\n<td>Table Cell 4</td>\\n<td>Table Cell 5</td>\\n</tr>\\n<tr>\\n<td>Table Cell 1</td>\\n<td>Table Cell 2</td>\\n<td>Table Cell 3</td>\\n<td>Table Cell 4</td>\\n<td>Table Cell 5</td>\\n</tr>\\n<tr>\\n<td>Table Cell 1</td>\\n<td>Table Cell 2</td>\\n<td>Table Cell 3</td>\\n<td>Table Cell 4</td>\\n<td>Table Cell 5</td>\\n</tr>\\n<tr>\\n<td>Table Cell 1</td>\\n<td>Table Cell 2</td>\\n<td>Table Cell 3</td>\\n<td>Table Cell 4</td>\\n<td>Table Cell 5</td>\\n</tr>\\n</tbody>\\n<tfoot>\\n<tr>\\n<th>Table Footer 1</th>\\n<th>Table Footer 2</th>\\n<th>Table Footer 3</th>\\n<th>Table Footer 4</th>\\n<th>Table Footer 5</th>\\n</tr>\\n</tfoot>\\n</table>\"}],\"branches_map____bkBMBcya_yn_BuqQGk_7c\":[{\"show\":true}],\"casestudies____JNDb99xYGZySMf5nzg2FX\":[{\"title\":\"Nadpis\",\"annot\":\"Tady patří popis sekce\",\"items\":[{\"blog\":2},{\"blog\":1}]}],\"partners____L2g03TwpxGWxe6CklxonV\":[{\"title\":\"Naši zahraniční partneři\",\"items\":[{\"image\":\"89\",\"name\":\"Jméno a příjmení\",\"position\":\"Of-Counsel\",\"country\":\"USA\"},{\"image\":\"89\",\"name\":\"Jméno a příjmení\",\"position\":\"Of-Counsel\",\"country\":\"Germany\"},{\"image\":\"89\",\"name\":\"Jméno a příjmení\",\"position\":\"Of-Counsel\",\"country\":\"Portugal\"}]}],\"branches____5izH6XTv_FIV3NzInYntR\":[{\"show\":true}],\"faq____NEH_IiUkl2vg91Zf3rZ9z\":[{\"list\":[{\"question\":\"Můžu s vámi realizovat schůzku i vzdáleně?\",\"answer\":\"<p><span>Určitě můžete, tento způsob komunikace s klienty využíváme již od vzniku naší kanceláře. Můžeme se spojit přes Skype, Microsoft Teams, Zoom, Google Hangouts, Calendly i další aplikace. Samozřejmě je možnost spojit se i telefonicky a realizovat tímto způsobem celou schůzku.</span></p>\"}]}],\"tabs____tb0coCjOOYDCuZOBk9bzo\":[{\"title\":\"Jak probíhá spolupráce s námi?\",\"items\":[{\"name\":\"Začneme\",\"content\":\"<h3>Poznáme vás a vaše potřeby</h3>\\n<p>Chcete s námi spolupracovat? Nejprve si domluvme setkání – zavolejte, napište e-mail nebo vyplňte kontaktní formulář na webu a řekněte nám ve stručnosti, co vás trápí. Schůzku naplánujeme tak, jak vám to nejvíc vyhovuje. Rádi se potkáváte osobně, nebo dáváte přednost online meetingům? Přizpůsobíme se vám.</p>\\n<p>Po schůzce vám pošleme doplňující otázky a všechno důkladně vysvětlíme tak, aby vám byl každý bod naší spolupráce jasný. Paragrafy jsou sice naše živobytí, ale v komunikaci je používáme střídmě, abychom vás nezahltili právničinou. Nemusíte se bát, že vás zavalíme odbornými termíny, zakládáme si na tom, aby vše dávalo smysl vám, i vašim kolegům.</p>\"},{\"name\":\"Promyslíme\",\"content\":\"<h3>Myslíme za vás a v souvislostech</h3>\\n<p>Hned jak od vás dostaneme všechny podklady, pustíme se do práce. Napříč právními týmy se zamyslíme nad všemi možnými souvislostmi. Nejenže projdeme, co je potřeba vyřešit podle zákona, ale zamyslíme se i nad tím, jak mohou právní otázky ovlivnit vaše podnikání. Vždycky se snažíme předvídat možná rizika a problémy, které by mohly přijít.</p>\\n<p>Pokud narazíme na něco, co by vás mohlo dostat do potíží, hned vás na to upozorníme a včas najdeme řešení. Naprosto totiž chápeme, že právní služby musí jít ruku v ruce s vašim podnikáním. Když je třeba, přicházíme s novými nápady, jak váš byznys lépe ochránit a posunout.</p>\"},{\"name\":\"Sepíšeme\",\"content\":\"<h3>Píšeme tak, abyste tomu rozuměli</h3>\\n<p>Řekli jsme si vše podstatné? Jakmile máme jasno, připravíme první návrh dokumentace. Sami nemáme rádi těžkopádný styl komunikace, můžete si být tedy jistí, že naše smlouva bude přehledná, jasná a srozumitelná. Věříme totiž, že složitosti do smluv nepatří a chceme, aby našim výstupům rozuměli i neprávníci.</p>\\n<p>Respektujeme a zohledníme váš tone of voice. Dokumentaci můžeme připravit v lehkém přátelském jazyce, nebo právnicky formálně – jak si budete přát. Chcete, aby byl text vtipný nebo spíš seriózní? Umíme oboje. A pokud, stejně jako my, preferujete online spolupráci na úpravě dokumentů, revize nám půjdou pěkně od ruky.</p>\"},{\"name\":\"Vyjednáme\",\"content\":\"<h3>Vyrážíme do světa</h3>\\n<p>Máme hotovo, dokumentace je na stole. Odpovídá zadání a respektuje vaše byznysové potřeby. Aby však nezůstala v šuplíku, pomůžeme vám i při vyjednávání s druhou stranou. Víme, že co právník, to jiný názor, a proto vám pomůžeme najít kompromis, který bude fungovat pro všechny. Hodnocení rizik a vyjednávání o smlouvě je naše silná disciplína.</p>\\n<p>Pokud potřebujete uzavřít smlouvu, můžete se na nás spolehnout, jsme na vaší straně. Aktivně za vás budeme komunikovat s úřady, soudy nebo jinými institucemi. Dokumenty, které připravíme, známe do posledního slova, a když přijdete s vlastními, rychle se v nich zorientujeme.</p>\"}]}],\"logos____6ZHbV0S_ZfyF4TZs3YRjH\":[{\"images\":[\"124\",\"123\",\"122\",\"121\",\"117\",\"118\",\"119\",\"120\",\"116\",\"115\",\"114\",\"113\",\"109\",\"110\",\"111\",\"112\"]}],\"testimonial____InTnR5rluUIi0PWChnZw7\":[{\"testimonial\":3}],\"testimonial____GEVj9_86Q979vUdO6BOOz\":[{\"testimonial\":1}],\"testimonial____mvsCeEnOB_F7Ngg1OFVwy\":[{\"testimonial\":2}],\"features____qIQhHU4vBbJNTVsOq4IfD\":[{\"title\":\"Seznam služeb\",\"features\":[{\"feature\":\"2\"},{\"feature\":\"3\"}],\"link\":[{\"toggle\":\"systemHref\",\"systemHref\":[{\"page\":\"449\",\"hrefName\":\"Všechny služby\"}]}]}]}',NULL,NULL),(485,1,1,476,449,2,'1|449|',1,1,'',0,'2025-05-29 13:47:20','2025-05-29 13:47:00',34,'2025-05-30 10:51:25',':Feature:Front:Feature:servicesCategory','common','2025-05-29 13:47:00','2125-05-29 13:47:00','IT právo a právo TMT','IT právo a právo TMT','V oblasti IT práva a práva TMT nás nic nezaskočí. S čím vám můžeme pomoct?','Lorem ipsum dolor sit amet, consectetuer adipiscing elit. In enim a arcu imperdiet malesuada. Donec vitae arcu. Nunc dapibus tortor vel mi dapibus sollicitudin. \n','',1,0,0,0,'IT právo je naše nejsilnější disciplína. Technologiím rozumíme, stejně tak vašemu jazyku, a vy budete určitě rozumět našim smlouvám. Našimi klienty jsou velké softwarové firmy, telekomunikační úřady i malí vývojáři.','',NULL,NULL,NULL,NULL,NULL,NULL,'{\"annot\":[{\"bg\":\"u-bgc-yellow\",\"btn\":[{\"toggle\":\"systemHref\"}],\"right\":[{\"image\":\"145\"}]}]}','{\"tabs____R__As54_uvkyusFSlfCPe\":[{\"title\":\"Jak probíhá spolupráce s námi?\",\"items\":[{\"name\":\"Začneme\",\"content\":\"<h2>Poznáme vás a vaše potřeby</h2>\\n<p>Chcete s námi spolupracovat? Nejprve si domluvme setkání – zavolejte, napište e-mail nebo vyplňte kontaktní formulář na webu a řekněte nám ve stručnosti, co vás trápí. Schůzku naplánujeme tak, jak vám to nejvíc vyhovuje. Rádi se potkáváte osobně, nebo dáváte přednost online meetingům? Přizpůsobíme se vám.</p>\\n<p>Po schůzce vám pošleme doplňující otázky a všechno důkladně vysvětlíme tak, aby vám byl každý bod naší spolupráce jasný. Paragrafy jsou sice naše živobytí, ale v komunikaci je používáme střídmě, abychom vás nezahltili právničinou. Nemusíte se bát, že vás zavalíme odbornými termíny, zakládáme si na tom, aby vše dávalo smysl vám, i vašim kolegům.</p>\"},{\"name\":\"Promyslíme\",\"content\":\"<p>Lorem ipsum</p>\"}]}],\"testimonial____G0I00NSvUZsbxyebPjl_d\":[{\"testimonial\":2}]}',NULL,NULL),(486,1,1,477,459,2,'1|459|',1,1,'',0,'2025-06-06 06:13:00','2025-06-06 06:13:00',34,'2025-06-06 06:33:18',':Front:Page:careerDetail','common','2025-06-06 06:13:00','2125-06-06 06:13:00','Advokát/ka na IT právo a smluvní dokumentaci','Advokát/ka na IT právo a smluvní dokumentaci','Advokát/ka na IT právo a smluvní dokumentaci','','',1,0,0,0,'V životě firmy občas nastane moment, kdy zjistíte, že váš název už neslouží tak, jak by měl. Možná se až příliš podobá jiné značce, možná se špatně vyslovuje, nebo vás jednoduše brzdí v růstu. ','',NULL,NULL,NULL,NULL,NULL,NULL,'{\"annot\":[{\"bg\":\"u-bgc-green\",\"btn\":[{\"toggle\":\"customHref\",\"customHref\":[{\"href\":\"#snippet-contactForm-form\",\"hrefName\":\"Chci tady pracovat\"}]}],\"right\":[{\"image\":\"162\"}]}]}','{\"content____KZ2KfNjucXcOe0_QbkdPo\":[{\"content\":\"<h2>Advokát/ka na smluvní právo a IT  (Brno)</h2>\\n<p class=\\\"par\\\">Hledáme nového kolegu, který se nebojí technologií a chce právo dělat jinak –<span> </span><strong>srozumitelně, efektivně a s reálným dopadem na byznys</strong>. Nepotřebujeme do týmu „právní encyklopedii“, ale skutečného parťáka, který dokáže propojit právní znalosti s byznysovým myšlením. Pokud tě láká pracovat s tech startupy a chceš se přidat do týmu<span> </span><a rel=\\\"noopener\\\" href=\\\"https://www.linkedin.com/in/pavel-cech-/\\\" target=\\\"_blank\\\">Pavla Čecha</a>, tak čti dál.? </p>\\n<h2 class=\\\"h2\\\">Co tě u nás čeká?</h2>\\n<p><span data-contrast=\\\"auto\\\">?</span><span> </span><strong><span data-contrast=\\\"auto\\\">Smlouvy, které dávají smysl:</span></strong><span data-contrast=\\\"auto\\\"> Budeš tvořit a revidovat smlouvy pro zákazníky a dodavatele. V B2B světě, který vyžaduje strategické přemýšlení a hledání řešení, která fungují nejen na papíře.</span></p>\\n<p><span data-contrast=\\\"auto\\\">?</span><span> </span><strong><span data-contrast=\\\"auto\\\">Compliance, IT &amp; IP:</span></strong><span data-contrast=\\\"auto\\\"> Podíváš se pod pokličku firem, které tvoří technologie zítřka. Pomůžeš jim nastavit pravidla hry tak, aby nešláply vedle v oblasti práva duševního vlastnictví a IT regulací.</span></p>\\n<p><span data-contrast=\\\"auto\\\">?</span><span> </span><strong><span data-contrast=\\\"auto\\\">Due diligence:</span></strong><span data-contrast=\\\"auto\\\"> Zapojíš se do právních prověrek zaměřených na duševní vlastnictví a odhalíš, co může startupy a technologické firmy na jejich cestě vpřed ohrozit.</span></p>\\n<h2 class=\\\"h2\\\"><span data-ccp-props=\\\"{\\\">Měl/a bys mít</span></h2>\\n<p><span data-contrast=\\\"auto\\\">?</span><span> </span><strong><span data-contrast=\\\"auto\\\">Touhu dělat právo jinak</span></strong><span data-contrast=\\\"auto\\\">: Jednoduše, moderně a bez zbytečných složitostí.</span></p>\\n<p><span data-contrast=\\\"auto\\\">?</span><span> </span><strong><span data-contrast=\\\"auto\\\">Zájem o software a byznys klientů: </span></strong><span data-contrast=\\\"auto\\\">Rozumíš nejen paragrafům, ale i technologickému prostředí a tomu, co klienti skutečně v jejich byznysu potřebují.</span></p>\\n<p><span data-contrast=\\\"auto\\\">?</span><span> </span><strong><span data-contrast=\\\"auto\\\">Přátelský vztah k technologiím: </span></strong><span data-contrast=\\\"auto\\\">Používáš AI nástroje, abys byl/a efektivnější a usnadňovaly ti  práci? Skvěle – budeš je totiž používat denně a všude, kde to bude dávat smysl.</span></p>\\n<p><span data-contrast=\\\"auto\\\">?</span><span> </span><strong><span data-contrast=\\\"auto\\\">Zdravé ambice: </span></strong><span data-contrast=\\\"auto\\\">Chceš se neustále zlepšovat a posouvat, ale zároveň  dokážeš být týmový hráč. Máš drive a nechceš se jenom vézt.</span></p>\\n<p><span data-contrast=\\\"auto\\\">✅</span><span> </span><strong><span data-contrast=\\\"auto\\\">Smysl pro odpovědnost: </span></strong><span data-contrast=\\\"auto\\\">Oceňujeme, když někdo odvede kvalitní práci a vidí v tom vlastní hodnotu, nejen splněný úkol.</span></p>\\n<h2 class=\\\"h2\\\">Proč tě to má zajímat?<span data-ccp-props=\\\"{}\\\"> </span></h2>\\n<p><span data-contrast=\\\"auto\\\">?</span><span> </span><strong><span data-contrast=\\\"auto\\\">Odměny podle skutečného výkonu: </span></strong><span data-contrast=\\\"auto\\\">Každý měsíc odměna podle vyfakturované práce. Nesledujeme, kdy jsi online, ale jakou práci odvedeš.</span></p>\\n<p><span data-contrast=\\\"auto\\\">?</span><span> </span><strong><span data-contrast=\\\"auto\\\">Prostor pro osobní i profesní růst: </span></strong><span data-contrast=\\\"auto\\\">Vzdělávání, mentoring a projekty, které tě posunou dál. Chceme, aby tě práce bavila, a věříme, že pak bude bavit i naše klienty.</span></p>\\n<p><span data-contrast=\\\"auto\\\">?</span><span> </span><strong><span data-contrast=\\\"auto\\\">Práci s klienty, kteří mění svět: </span></strong><span data-contrast=\\\"auto\\\">Startupy, software-houses a e-commerce platformy. Právo může být celkem jízda.</span></p>\\n<h2 class=\\\"h2\\\">Ohodnocení a nástup</h2>\\n<ul>\\n<li data-leveltext=\\\"\\\" data-font=\\\"Symbol\\\" data-listid=\\\"3\\\" data-list-defn-props=\\\"{\\\" aria-setsize=\\\"-1\\\" data-aria-posinset=\\\"1\\\" data-aria-level=\\\"1\\\"><strong><span data-contrast=\\\"auto\\\">80 000-120 000 Kč měsíčně</span></strong><span data-contrast=\\\"auto\\\"> – Konkrétní částka podle tvé fakturace, ne podle odsezených let.</span></li>\\n<li><strong><span data-contrast=\\\"auto\\\">Nástup dle dohody</span></strong><span data-contrast=\\\"auto\\\"> – Čím dřív tím líp. Těšíme se na tebe!</span></li>\\n</ul>\\n<h2 class=\\\"h2\\\" aria-level=\\\"2\\\">Benefity</h2>\\n<ul>\\n<li><strong><span data-contrast=\\\"auto\\\">5 týdnů dovolené</span></strong><span data-contrast=\\\"auto\\\"> – Protože i právníci potřebují občas vypnout.</span><span data-ccp-props=\\\"{\\\"> </span></li>\\n<li><strong><span data-contrast=\\\"auto\\\">5 sick days</span></strong><span data-contrast=\\\"auto\\\"> – Léčit rýmu v kanceláři nikomu neprospěje.</span><span data-ccp-props=\\\"{\\\"> </span></li>\\n<li><strong><span data-contrast=\\\"auto\\\">Další příjemné benefity</span></strong><span data-contrast=\\\"auto\\\"> – Multisport kartu, stravenkový paušál, mobilní tarif a další výhody probereme osobně.</span><span data-ccp-props=\\\"{\\\"> </span></li>\\n</ul>\\n<h2 class=\\\"h2\\\" aria-level=\\\"2\\\">Požadavky</h2>\\n<p><span data-contrast=\\\"auto\\\">⏰</span><span> </span><strong><span data-contrast=\\\"auto\\\">Plný úvazek</span></strong><span data-contrast=\\\"auto\\\"> – Hledáme parťáka na plný plyn.</span><span data-ccp-props=\\\"{\\\"> </span></p>\\n<p><span data-contrast=\\\"auto\\\">?</span><span> </span><strong><span data-contrast=\\\"auto\\\">Angličtina nutností</span></strong><span data-contrast=\\\"auto\\\"> – Bez ní se v tech světě neobejdeme.</span></p>\\n<p> </p>\\n<p><span data-contrast=\\\"auto\\\">Zaujala tě naše nabídka? Pošli svůj životopis na </span><a href=\\\"mailto:<EMAIL>\\\"><span data-contrast=\\\"none\\\"><EMAIL></span></a><span data-contrast=\\\"auto\\\"> a my se ozveme. Žádné zdlouhavé výběrové řízení – chceme poznat tebe, ne tvoji znalost paragrafů. Těšíme se! </span><span data-contrast=\\\"auto\\\">?</span></p>\"}]}',NULL,NULL);
/*!40000 ALTER TABLE `tree` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tree_file`
--

DROP TABLE IF EXISTS `tree_file`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tree_file` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `fileId` int(11) NOT NULL COMMENT 'idfile',
  `treeId` int(11) NOT NULL,
  `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `url` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `sort` mediumint(9) DEFAULT NULL,
  `size` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `fileId_treeId` (`fileId`,`treeId`),
  KEY `idtree_idx` (`treeId`),
  KEY `idfile_idx` (`fileId`),
  CONSTRAINT `tree_file_ibfk_1` FOREIGN KEY (`fileId`) REFERENCES `file` (`id`) ON DELETE CASCADE,
  CONSTRAINT `tree_file_ibfk_2` FOREIGN KEY (`treeId`) REFERENCES `tree` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tree_file`
--

LOCK TABLES `tree_file` WRITE;
/*!40000 ALTER TABLE `tree_file` DISABLE KEYS */;
INSERT INTO `tree_file` VALUES (2,37,102,'girl-with-red-hat-sLcVe47YUJI-unsplash.jpg','/data/files/37-girl-with-red-hat-slcve47yuji-unsplash.jpeg',2,5611172),(3,36,102,'Sample image','/data/files/36-toby-elliott-m3srhemrmbq-unsplash.jpeg',1,2864450),(4,40,102,'payment-detail-T644971264.pdf','/data/files/40-payment-detail-t644971264.pdf',0,3045),(5,41,102,'payment-detail-T738633803.pdf','/data/files/41-payment-detail-t738633803.pdf',41,3034),(6,102,450,'Název souboru','/data/files/102-clinic-material-4.pdf',102,1061611);
/*!40000 ALTER TABLE `tree_file` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tree_parent`
--

DROP TABLE IF EXISTS `tree_parent`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tree_parent` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=478 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tree_parent`
--

LOCK TABLES `tree_parent` WRITE;
/*!40000 ALTER TABLE `tree_parent` DISABLE KEYS */;
INSERT INTO `tree_parent` VALUES (1),(8),(12),(21),(26),(37),(40),(42),(43),(72),(74),(75),(76),(77),(78),(94),(102),(255),(394),(398),(405),(413),(445),(446),(449),(450),(451),(452),(453),(454),(455),(456),(457),(458),(459),(460),(461),(462),(463),(464),(465),(466),(467),(468),(469),(470),(471),(472),(473),(474),(475),(476),(477);
/*!40000 ALTER TABLE `tree_parent` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tree_product`
--

DROP TABLE IF EXISTS `tree_product`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tree_product` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `productId` int(11) NOT NULL,
  `treeId` int(11) NOT NULL,
  `type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `sort` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `productId_treeId_type` (`productId`,`treeId`,`type`),
  KEY `idproduct` (`productId`),
  KEY `idtree` (`treeId`),
  CONSTRAINT `tree_product_ibfk_2` FOREIGN KEY (`treeId`) REFERENCES `tree` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `tree_product_ibfk_5` FOREIGN KEY (`productId`) REFERENCES `product` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8 COLLATE=utf8_czech_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tree_product`
--

LOCK TABLES `tree_product` WRITE;
/*!40000 ALTER TABLE `tree_product` DISABLE KEYS */;
INSERT INTO `tree_product` VALUES (4,7,102,'attachedToProduct',0),(8,7,408,'attachedToProduct',0);
/*!40000 ALTER TABLE `tree_product` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tree_tree`
--

DROP TABLE IF EXISTS `tree_tree`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tree_tree` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mainTreeId` int(11) NOT NULL,
  `attachedTreeId` int(11) NOT NULL,
  `type` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `sort` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `mainTreeId_attachedTreeId_type` (`mainTreeId`,`attachedTreeId`,`type`),
  KEY `idtree` (`mainTreeId`),
  KEY `id_attached_tree` (`attachedTreeId`),
  CONSTRAINT `tree_tree_ibfk_3` FOREIGN KEY (`mainTreeId`) REFERENCES `tree` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `tree_tree_ibfk_4` FOREIGN KEY (`attachedTreeId`) REFERENCES `tree` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8 COLLATE=utf8_czech_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tree_tree`
--

LOCK TABLES `tree_tree` WRITE;
/*!40000 ALTER TABLE `tree_tree` DISABLE KEYS */;
INSERT INTO `tree_tree` VALUES (8,189,417,'linkedCategory',0);
/*!40000 ALTER TABLE `tree_tree` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user`
--

DROP TABLE IF EXISTS `user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `user` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `role` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT 'user',
  `firstname` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `lastname` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `phone` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `street` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `city` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `zip` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `stateId` int(11) DEFAULT NULL,
  `company` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `ic` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `dic` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `created` int(11) DEFAULT NULL,
  `createdTime` datetime DEFAULT NULL,
  `edited` int(11) DEFAULT NULL,
  `editedTime` datetime DEFAULT NULL,
  `lastLogin` datetime DEFAULT NULL,
  `customAddressJson` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `orderCount` int(11) DEFAULT '0',
  `priceLevelId` int(11) DEFAULT '1',
  `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `googleId` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`),
  KEY `stateId` (`stateId`),
  KEY `FK_user_price_level` (`priceLevelId`),
  CONSTRAINT `FK_user_price_level` FOREIGN KEY (`priceLevelId`) REFERENCES `price_level` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `user_ibfk_1` FOREIGN KEY (`stateId`) REFERENCES `state` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=35 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user`
--

LOCK TABLES `user` WRITE;
/*!40000 ALTER TABLE `user` DISABLE KEYS */;
INSERT INTO `user` VALUES (1,'<EMAIL>','$2y$10$2nEjnmaE5NQuE4w8bu2vtOHmYGBA3wOzbxjyP6BZ2y5.ajb7mj1uG','developer','Tomáš','Hejč','*********','street','city','90210',NULL,'','12345678','dic',NULL,NULL,NULL,'2021-06-10 17:38:26','2021-11-17 10:44:36',NULL,9,1,NULL,NULL),(2,'<EMAIL>','$2y$10$dOhmKS9Rxc2UlzVHzZloiuZLlz9lp4v76KJFDxGLkcxaPp9LfO6/.','admin','Podpora','','','','','',1,'','','',NULL,NULL,NULL,'2021-06-22 17:50:18',NULL,NULL,0,1,NULL,NULL),(3,'<EMAIL>','$2a$07$7aze1h55fxd7z7n6srlboepOlzysqQl6qA6ojP9a13vCqv7vyKlvm','developer','Vojta','Brtník','*********0','street','city','90210',NULL,'company','ic','dic',NULL,NULL,NULL,'2019-02-08 16:11:33','2018-03-14 20:54:57',NULL,5,1,NULL,NULL),(4,'<EMAIL>','$2y$10$gcTPV3c/3l7fxjm1Cj1S/..vHkWXZ6WPGzfHRNlhdxPxyW0hwDTfm','developer','Ludek1','Hradil','*********','test1a','test','22344',NULL,'','','',NULL,'2017-12-06 12:19:09',NULL,'2021-07-02 17:38:22','2019-03-01 13:05:42',NULL,26,1,NULL,NULL),(5,'<EMAIL>','$2y$10$m43UWmYwoxb9pmTbhUfal.ywWa0MP1doEqpBxLbThVEVlyF0bbS5i','developer','Tomáš','Krejčí','*********','Hněvkovského 65, Podnikatelský inkubátor PI 2 (SuperKodéři)','Brno','61700',1,'SuperKodéři s.r.o.','','',NULL,NULL,NULL,'2021-06-23 12:51:03','2021-07-07 11:06:27','[{\"firstname\":\"Tom\\u00e1\\u0161\",\"lastname\":\"Krej\\u010d\\u00ed\",\"company\":\"SuperKod\\u00e9\\u0159i s.r.o.\",\"phone\":\"*********\",\"phonePrefix\":\"420\",\"street\":\"Hn\\u011bvkovsk\\u00e9ho 65, Podnikatelsk\\u00fd inkub\\u00e1tor PI 2 (SuperKod\\u00e9\\u0159i)\",\"city\":\"Brno\",\"zip\":\"61700\",\"state\":\"1\"},{\"firstname\":\"Tom\\u00e1\\u0161\",\"lastname\":\"Krej\\u010d\\u00ed\",\"company\":\"SuperKod\\u00e9\\u0159i s.r.o.\",\"phone\":\"*********\",\"phonePrefix\":\"420\",\"street\":\"Nikol\\u010dice\",\"city\":\"Nikol\\u010dice\",\"zip\":\"69171\",\"state\":\"1\"}]',0,1,NULL,NULL),(6,'<EMAIL>','$2y$10$RZKM2zqN23BWizGUyyE1seKg81SlbyMuYG5s3P87s9njyOr82G8JO','admin','info','','','','','',NULL,'','','',NULL,'2019-04-11 14:55:23',NULL,'2021-06-10 17:28:49',NULL,NULL,0,1,NULL,NULL),(8,'<EMAIL>','$2y$10$9I1ZNifdMDGWJJlOroT3cu4fJqYdnmBCFizLE0Ij2N3IJa7wChwQu','user','Tomáš','Hejč','*********','9. května 45a','Blansko','67801',1,'SuperKodéři s.r.o.','123','zc123',NULL,'2019-02-12 22:11:28',NULL,'2021-06-23 09:47:05','2021-06-10 17:53:58',NULL,1,1,NULL,NULL),(9,'<EMAIL>','$2y$10$OreL2D4mrFOAL4lDr8Bj7ORfkGaTtMqS6NOajPcB/lluv0TZpemyy','developer','Petr','Goca','*********','Hněvkovského 65','Brno','61700',1,'','','',NULL,'2019-01-07 13:44:58',NULL,'2021-06-25 11:40:57','2021-06-25 09:07:31','[{\"firstname\":\"Petr 1\",\"lastname\":\"Goca 1\",\"company\":\"Firma 1\",\"phone\":\"*********\",\"phonePrefix\":\"CZ\",\"street\":\"Ulice 1\",\"city\":\"M\\u011bsto 1\",\"zip\":\"60001\",\"state\":\"1\"},{\"firstname\":\"Petr 2\",\"lastname\":\"Goca 2\",\"company\":\"\",\"phone\":\"*********\",\"phonePrefix\":\"CZ\",\"street\":\"Ulice 2\",\"city\":\"M\\u011bsto 2\",\"zip\":\"60002\",\"state\":\"1\"},{\"firstname\":\"Petr 3\",\"lastname\":\"Goca 3\",\"company\":\"\",\"phone\":\"\",\"phonePrefix\":\"CZ\",\"street\":\"Ulice 3\",\"city\":\"\",\"zip\":\"\",\"state\":\"1\"}]',7,1,NULL,NULL),(10,'<EMAIL>','$2y$10$RPrtnXcqWP29pkQvj.ckpOFguBTG/1K9h./sVX/779E6aXQj2.uOW','developer','Viktor','Daňko','','','','',NULL,'','','',NULL,'2019-07-12 07:58:44',NULL,NULL,NULL,NULL,0,1,NULL,NULL),(11,'<EMAIL>','$2y$10$A91IGMqqYbkiyOOdU8OF1ehw.DoNx9DVIVvmRYMeSkg2nmyAu1w/e','developer','Eliška','Plitzová','','','','',NULL,'','','',NULL,'2020-03-11 14:43:51',NULL,'2025-05-13 12:51:26',NULL,NULL,0,1,NULL,NULL),(12,'<EMAIL>','$2y$10$XgxBed2akfS7T9N.xXJ./OBnIMitcNrnRTJP6dSWB7v1yyMTp8dCm','developer','Jan','Kočiš','+420606123456','test','test','59266',1,'','','',NULL,'2020-12-07 14:26:46',NULL,'2021-07-14 11:35:28','2021-07-14 11:35:14','[{\"firstname\":\"Jan\",\"lastname\":\"Ko\\u010di\\u0161\",\"company\":\"\",\"phone\":\"+420606559677\",\"street\":\"V\\u00edr 274\",\"city\":\"V\\u00edr\",\"zip\":\"59266\",\"state\":\"1\"}]',4,1,NULL,NULL),(13,'<EMAIL>','$2y$10$2Mcj0QpAdj4zNyB5vyd6jO82cU1mvfjrN6M1CVnT/1G/z0D1n.oJG','developer','Petr','Grochál','*********0','street','city','90210',NULL,'company','ic','dic',NULL,NULL,NULL,'2021-05-21 10:21:23','2018-03-14 20:54:57',NULL,5,1,NULL,NULL),(14,'<EMAIL>','$2y$10$1NxnNnVfwM/WxPYBMFMkO.uMV5pxvnF.GlX7Ha.rR.t.HJaCZ1Smi','user','','','*********','Hněvkovského 65','Brno','61700',NULL,'','','',NULL,'2021-06-08 13:59:49',NULL,'2021-07-01 19:08:30',NULL,NULL,6,1,NULL,NULL),(15,'<EMAIL>','$2y$10$u6lsdiXkQY2DE22/5kDPYOjUNft8GOBj4MlaFkZtjyddzt22lEci2','user','','','','','','',NULL,'','','',NULL,'2021-06-10 17:45:32',NULL,NULL,NULL,NULL,0,1,NULL,NULL),(16,'<EMAIL>','$2y$10$ndfJw2Ewes6FQ9pGPPfOWeUrhc3XzquRREmFV2YFIgoX2/f7N86GK','user','','','','','','',NULL,'','','',NULL,'2021-06-10 22:11:54',NULL,NULL,NULL,NULL,0,1,NULL,NULL),(17,'<EMAIL>','$2y$10$1m5AaCttSPOgmkIUkN.rRelepjPfGFmb9B8aYj8u8Psk2HhaEzXMy','user','','','','','','',NULL,'','','',NULL,'2021-06-14 17:29:07',NULL,'2021-06-25 10:53:38',NULL,NULL,0,1,NULL,NULL),(18,'<EMAIL>','$2y$10$zgfq/mr971c.1WtBka/GteCE.dCud8fN4R5SGB8sS6cxcUzZtLRve','user','','','','','','',NULL,'','','',NULL,'2021-06-17 08:20:47',NULL,NULL,NULL,NULL,0,1,NULL,NULL),(19,'<EMAIL>','$2y$10$ess5TBxmSE9VQYlp7Jqj.uXDIw0Sjhrv2BmiTwyHW9dW9dQNfduPO','user','J','K','','','','',NULL,'','','',NULL,'2021-06-22 19:41:55',NULL,'2021-06-23 13:43:19',NULL,NULL,3,1,NULL,NULL),(21,'<EMAIL>','$2y$10$oYq/wYHVe/hU81TsuggpneB1xqQWPkIUOg6bBpjVwb8MtRkm1MzHm','user','Tomáš','Hejč','+420603123456','Hněvkovského','Brno','61700',1,'','','',NULL,'2021-07-01 17:15:38',NULL,'2021-07-01 17:19:03','2021-07-01 17:18:08','[]',0,1,NULL,NULL),(22,'<EMAIL>','$2y$10$IQUuW2SD.eZTCpFYisIwPOZSPQOL.P58NW5f5IBtDyqcRz7BNLXsq','developer','Zuzka','Šumlanská','*********','test 1','test','11111',1,'','','',NULL,'2021-07-01 20:29:07',NULL,'2021-07-08 11:17:47','2021-07-07 12:12:17','[{\"firstname\":\"Zuzana\",\"lastname\":\"\\u0160umlansk\\u00e1\",\"company\":\"Zuzana Sumlanska\",\"phone\":\"*********\",\"street\":\"Bene\\u0161ova 254\",\"city\":\"Mod\\u0159ice\",\"zip\":\"66442\",\"state\":\"1\"}]',4,1,NULL,NULL),(23,'<EMAIL>','$2y$10$opeSgiqkPlbtWd7t4PLTWe65qE4zuUiGntvIulwrsq6052ti.DT02','developer','Jakub','Fatrdla','*********0','street','city','90210',NULL,'company','ic','dic',NULL,NULL,NULL,'2019-02-08 16:11:33','2018-03-14 20:54:57',NULL,5,1,NULL,NULL),(24,'<EMAIL>','$2y$10$DFkwvsCE8XeutWUdsS.f8O.q8irwmrGpy8/DvQW7l1esE/0DTulvC','developer','Eva','Maťová','','','','',1,'','','',NULL,'2021-09-01 10:48:11',NULL,NULL,NULL,NULL,0,1,'{}',NULL),(25,'<EMAIL>','$2y$10$oMW2dRJmAsoaVLKilXqdMe5IsgmIHn/p5BeVmBbnRMyK8aUkictgq','developer','Petr','Kudyn','','','','',1,'','','',NULL,'2021-09-01 17:22:22',NULL,'2021-09-02 08:16:07',NULL,NULL,0,1,'{}',NULL),(26,'<EMAIL>','$2y$10$JIzzPHwPfPNXoAQYHj86Se3NNYmXqp6S.HmQWt/Yxr4KCHQRuMLBa','developer','Petr','Bulánek','','','','',1,'','','',NULL,'2021-09-01 17:22:57',NULL,'2021-09-03 09:44:12',NULL,NULL,0,1,'{}',NULL),(27,'<EMAIL>','$2y$10$ivsgXQnVMVj8bUYpBA7H1eLELKxk3CSOf.cfW5ybtV1SBd7KLGNyC','developer','Petr','','','','','',1,'','','',NULL,'2021-11-25 10:05:05',NULL,NULL,NULL,NULL,0,1,'{}',NULL),(28,'<EMAIL>','$2y$10$OAtxFog3Z2FkogkiEIBLY.ZRSr6mRVnXoUM883rhOrzToHEfBDPDK','developer','Jiří','Pudil','','','','',1,'','','',NULL,'2022-01-04 19:59:32',NULL,NULL,NULL,NULL,0,1,'{}',NULL),(29,'<EMAIL>','$2y$10$4fQIYhfGaPbqg/4pIuWKt./TvTDLKrSzhWA3ElbvBns2ztMadFaki','developer','Martin','Fryšták','','','','',1,'','','',NULL,'2022-04-13 15:36:20',NULL,NULL,NULL,NULL,0,1,'{}',NULL),(30,'<EMAIL>','$2y$10$0M9SScI8pNnV3I3WJIk1B.vZz/mtTZQkXNnPFu9OqMzBw9OGk5MLy','user','MartinFryšták','Test','','','','',1,'','','',NULL,'2022-04-14 13:07:00',NULL,NULL,NULL,NULL,0,1,'{}',NULL),(31,'<EMAIL>','$2y$10$LZh4Z4E4ZMnrclB2gAL3qupAI4D1ujev2Cyinefzf343FJTWNRV26','admin','Test','User','','','','',1,'','','',NULL,'2025-04-23 11:48:32',NULL,NULL,NULL,NULL,0,1,'{}',NULL),(32,'<EMAIL>','$2y$10$unREJLZVvjmmwuLjhYSRUucnZxJS/QPjNh08uGMiyMQsSnYwQ7whm','developer','Martina','Paclíková','','','','',1,'','','',NULL,NULL,NULL,'2023-03-01 09:40:34',NULL,NULL,0,1,NULL,NULL),(33,'<EMAIL>','$2y$10$fliC2SobBP53YHHgol62Vex0fUCqjEduU2J4fsUxS5mT5s7BanAaa','developer','Jakub','Mikita','','','','',1,'','','',NULL,NULL,NULL,'2024-01-22 16:40:34',NULL,NULL,0,1,NULL,NULL),(34,'<EMAIL>','$2y$10$FBQFf3bcwvFllcSE.3YRb.blpdSt1t2GqG3LPV1OSWl.HftGHhvJm','developer','Luděk','Ch','','','','',NULL,'','','',NULL,'2025-04-24 09:30:01',NULL,NULL,NULL,NULL,0,1,'{}',NULL);
/*!40000 ALTER TABLE `user` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_hash`
--

DROP TABLE IF EXISTS `user_hash`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `user_hash` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `userId` int(11) NOT NULL,
  `type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `hash` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `createdTime` datetime NOT NULL,
  `validTo` datetime NOT NULL,
  `valid` tinyint(1) NOT NULL DEFAULT '1',
  `usedTime` datetime DEFAULT NULL,
  `data` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  PRIMARY KEY (`id`),
  KEY `userId` (`userId`),
  CONSTRAINT `user_hash_ibfk_1` FOREIGN KEY (`userId`) REFERENCES `user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf16 COLLATE=utf16_czech_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_hash`
--

LOCK TABLES `user_hash` WRITE;
/*!40000 ALTER TABLE `user_hash` DISABLE KEYS */;
INSERT INTO `user_hash` VALUES (1,12,'lostPassword','7277fd8ae8b52ddffbe8c01a433ffb67c7b479dd','2020-12-07 14:26:59','2020-12-08 14:26:59',1,NULL,NULL),(7,5,'lostPassword','3d88e133d5f00f94fbb94ab15e4c3bea4e5a5505','2021-06-23 12:16:41','2021-06-24 12:16:41',1,NULL,NULL),(8,5,'lostPassword','f8682eb898d701933c7053c6d7d7b86bb5d5b7c3','2021-06-23 12:16:42','2021-06-24 12:16:42',1,NULL,NULL),(9,5,'lostPassword','042b063bd61844ecd4b1f2e62d8bb04fb1911c34','2021-06-23 12:17:06','2021-06-24 12:17:06',1,NULL,NULL),(17,4,'lostPassword','36b2b40c6eacb4b6fd1aed1b54a39d10129505a7','2021-07-02 17:38:05','2021-07-03 17:38:05',1,NULL,NULL),(18,4,'lostPassword','4665336fadd4dc0d7dffe55e43ceff2831b89e16','2025-04-30 11:55:21','2025-05-01 11:55:21',1,NULL,'{}');
/*!40000 ALTER TABLE `user_hash` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_mutation`
--

DROP TABLE IF EXISTS `user_mutation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `user_mutation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `userId` int(11) NOT NULL,
  `mutationId` int(11) NOT NULL,
  `newsletter` tinyint(4) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `userId` (`userId`),
  KEY `mutationId` (`mutationId`),
  CONSTRAINT `user_mutation_ibfk_3` FOREIGN KEY (`userId`) REFERENCES `user` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `user_mutation_ibfk_4` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=56 DEFAULT CHARSET=utf8 COLLATE=utf8_czech_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_mutation`
--

LOCK TABLES `user_mutation` WRITE;
/*!40000 ALTER TABLE `user_mutation` DISABLE KEYS */;
INSERT INTO `user_mutation` VALUES (1,1,1,0),(24,2,1,0),(25,3,1,0),(26,4,1,0),(27,5,1,0),(28,6,1,0),(29,8,1,0),(30,9,1,0),(31,10,1,0),(32,11,1,0),(33,12,1,0),(34,13,1,0),(35,14,1,0),(36,15,1,0),(37,16,1,0),(38,17,1,0),(39,18,1,0),(40,19,1,0),(41,21,1,0),(42,22,1,0),(44,23,1,0),(45,24,1,0),(46,25,1,0),(47,26,1,0),(48,27,1,0),(49,28,1,0),(50,29,1,0),(51,30,1,0),(52,31,1,0),(53,32,1,0),(54,33,1,0),(55,34,1,0);
/*!40000 ALTER TABLE `user_mutation` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `voucher`
--

DROP TABLE IF EXISTS `voucher`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `voucher` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mutationId` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `internalName` varchar(255) NOT NULL,
  `type` varchar(20) NOT NULL,
  `minPriceOrder` decimal(18,4) DEFAULT NULL,
  `reuse` int(11) NOT NULL DEFAULT '0',
  `combination` int(11) NOT NULL DEFAULT '0',
  `combinationType` varchar(20) DEFAULT NULL,
  `public` int(11) NOT NULL DEFAULT '0',
  `publicTo` datetime DEFAULT NULL,
  `publicFrom` datetime DEFAULT NULL,
  `created` int(11) DEFAULT NULL,
  `createdTime` datetime DEFAULT NULL,
  `edited` int(11) DEFAULT NULL,
  `editedTime` datetime DEFAULT NULL,
  `discount_amount` decimal(18,4) DEFAULT NULL,
  `discount_currency` char(3) DEFAULT NULL,
  `discountPercent` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `mutationId` (`mutationId`),
  CONSTRAINT `voucher_ibfk_1` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `voucher`
--

LOCK TABLES `voucher` WRITE;
/*!40000 ALTER TABLE `voucher` DISABLE KEYS */;
/*!40000 ALTER TABLE `voucher` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `voucher_code`
--

DROP TABLE IF EXISTS `voucher_code`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `voucher_code` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `isUsed` int(11) NOT NULL DEFAULT '0',
  `usedAt` datetime DEFAULT NULL,
  `createdTime` datetime NOT NULL,
  `voucher` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `voucher` (`voucher`),
  CONSTRAINT `voucher_code_ibfk_1` FOREIGN KEY (`voucher`) REFERENCES `voucher` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `voucher_code`
--

LOCK TABLES `voucher_code` WRITE;
/*!40000 ALTER TABLE `voucher_code` DISABLE KEYS */;
/*!40000 ALTER TABLE `voucher_code` ENABLE KEYS */;
UNLOCK TABLES;
/*!50112 SET @disable_bulk_load = IF (@is_rocksdb_supported, 'SET SESSION rocksdb_bulk_load = @old_rocksdb_bulk_load', 'SET @dummy_rocksdb_bulk_load = 0') */;
/*!50112 PREPARE s FROM @disable_bulk_load */;
/*!50112 EXECUTE s */;
/*!50112 DEALLOCATE PREPARE s */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-06-10 13:38:24
