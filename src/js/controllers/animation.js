import { Controller } from '@hotwired/stimulus';
import gsap from 'gsap';
import { MotionPathPlugin } from 'gsap/MotionPathPlugin';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

gsap.registerPlugin(MotionPathPlugin, ScrollTrigger);

export default class extends Controller {
	static targets = ['rocket', 'path', 'arrow', 'dashed'];

	connect() {
		const path = this.pathTarget;
		const rocket = this.rocketTarget;
		if (!path || !rocket) return;

		let arrow = null;
		if (this.hasArrowTarget) {
			arrow = this.arrowTarget;
			arrow.style.transformOrigin = 'center center';
		}

		// --- <PERSON><PERSON><PERSON><PERSON><PERSON> animovan<PERSON>ch čárek podle pohybu rakety a šipky ---
		// SVG dashed path (čárkovaná čára) - použij Stimulus target
		const dashedPath = this.hasDashedTarget ? this.dashedTarget : null;
		if (dashedPath) {
			dashedPath.setAttribute('stroke', '#060606');
			dashedPath.setAttribute('stroke-width', '1.2');
			dashedPath.setAttribute('fill', 'none');
			dashedPath.setAttribute('stroke-dasharray', '14 14');
			dashedPath.setAttribute('d', path.getAttribute('d'));
		}

		// --- GSAP timeline pro raketu, šipku a čárky ---
		const tl = gsap.timeline({
			scrollTrigger: {
				trigger: path,
				start: 'top center',
				end: 'bottom center',
				scrub: true,
			},
		});

		// Raketa + zoom efekt
		// Nejprve ji skryj (opacity 0)
		gsap.set(this.rocketTarget, { opacity: 0, scale: 0.6 });
		tl.to(
			this.rocketTarget,
			{
				opacity: 1,
				scale: 0.6,
				transformOrigin: 'center center',
			},
			0,
		);
		tl.to(
			this.rocketTarget,
			{
				scale: 1,
				opacity: 1,
				transformOrigin: 'center center',
				ease: 'none',
				motionPath: {
					path: path,
					align: path,
					autoRotate: true,
					alignOrigin: [0.5, 0.5],
				},
			},
			0,
		);

		// Šipka
		if (arrow) {
			gsap.set(arrow, {
				motionPath: {
					path: path,
					align: path,
					alignOrigin: [0.5, 0.5],
					autoRotate: true,
					start: 0,
					end: 0.92,
				},
			});
			tl.to(
				arrow,
				{
					ease: 'none',
					motionPath: {
						path: path,
						align: path,
						alignOrigin: [0.5, 0.5],
						autoRotate: true,
						start: 0,
						end: 0.92,
					},
				},
				0,
			);
		}

		// --- Animace čárkované čáry pomocí postupného přidávání path segmentů ---
		// Nejprve smaž předchozí segmenty (pokud existují)
		const svg = path.closest('svg');
		svg.querySelectorAll('[data-rocket-segment]').forEach((el) => el.remove());

		// Parametry čárky a mezery
		const dash = 14,
			gap = 14;
		const pathLength = path.getTotalLength();
		const numDashes = Math.floor(pathLength / (dash + gap));

		// Proxy pro synchronizaci s animací (raketa/šipka)
		let progressProxy = { p: 0, opacity: 0 };
		tl.to(
			progressProxy,
			{
				p: 1,
				opacity: 1,
				ease: 'none',
				onUpdate: () => {
					svg.querySelectorAll('[data-rocket-segment]').forEach((el) => el.remove());
					let prog = progressProxy.p;
					if (arrow) {
						prog = 0.15 + prog * (0.92 - 0.15);
					}
					if (prog <= 0) return;
					const visibleLength = prog * pathLength;
					for (let i = 0; i < numDashes; i++) {
						const dashStart = i * (dash + gap);
						const dashEnd = dashStart + dash;
						if (dashStart < visibleLength) {
							const segEnd = Math.min(dashEnd, visibleLength);
							if (segEnd > dashStart) {
								const segmentLength = segEnd - dashStart;
								const points = [];
								const steps = Math.max(2, Math.round(segmentLength / 2));
								for (let s = 0; s <= steps; s++) {
									const l = dashStart + (segEnd - dashStart) * (s / steps);
									const pt = path.getPointAtLength(l);
									points.push(`${pt.x},${pt.y}`);
								}
								if (points.length > 1) {
									const segment = document.createElementNS('http://www.w3.org/2000/svg', 'path');
									segment.setAttribute('data-rocket-segment', '');
									segment.setAttribute('stroke', '#060606');
									segment.setAttribute('stroke-width', '1.2');
									segment.setAttribute('fill', 'none');
									segment.setAttribute('stroke-linecap', 'round');
									segment.setAttribute('d', `M${points.join(' L')}`);
									svg.insertBefore(segment, dashedPath ? dashedPath.nextSibling : svg.firstChild.nextSibling);
								}
							}
						}
					}
				},
			},
			0,
		);
	}
}
