import { Controller } from '@hotwired/stimulus';

export const create = () => {
	return class extends Controller {
		static targets = ['link'];
		static values = {
			target: String,
		};

		connect() {
			console.log('tag-filter controller connected');
			// Initialize the controller
		}

		async filter(event) {
			if (!event || !event.currentTarget) return;

			event.preventDefault();

			const url = event.currentTarget.getAttribute('href');
			if (!url) return;

			const targetElement = document.getElementById(`snippet--${this.targetValue}`);

			if (!targetElement) return;

			// Show loading state
			targetElement.style.opacity = '0.5';

			try {
				const response = await fetch(url, {
					headers: {
						'X-Requested-With': 'XMLHttpRequest',
					},
				});

				if (!response.ok) throw new Error('Network response was not ok');

				const data = await response.json();

				// Get the HTML content from the snippets
				const articlesHtml = data.snippets[`snippet--${this.targetValue}`] ?? data.snippets['snippet--content'];
				if (!articlesHtml) throw new Error('No articles snippet found in response');

				// Update articles content
				if (data.snippets['snippet--content'] && data.snippets['snippet--content'].length > 0) {
					document.getElementById('snippet--content').innerHTML = articlesHtml;
				} else {
					targetElement.innerHTML = articlesHtml;
				}

				// Update tags section if available
				const tagsHtml = data.snippets['snippet--tags'];
				if (tagsHtml) {
					const tagsElement = this.element;
					if (tagsElement) {
						tagsElement.outerHTML = tagsHtml;
					}
				}

				// Update URL without page reload
				window.history.pushState({}, '', url);

				// Update active state of tags
				// this.linkTargets.forEach((link) => {
				// 	if (link && link.tagName === 'A') {
				// 		link.classList.remove('btn--secondary');
				// 		link.classList.add('btn--gray');
				// 	}
				// });

				// const clickedElement = event.currentTarget;
				// if (clickedElement && clickedElement.tagName === 'A') {
				// 	clickedElement.classList.remove('btn--gray');
				// 	clickedElement.classList.add('btn--secondary');
				// }
			} catch (error) {
				console.error('Error:', error);
			} finally {
				// Reset loading state
				if (targetElement) {
					targetElement.style.opacity = '1';
				}
			}
		}
	};
};
