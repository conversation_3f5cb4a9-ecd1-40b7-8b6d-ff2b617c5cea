import { Controller } from '@hotwired/stimulus';
import { index } from '../tools/index.js';

export const create = () => {
	return class extends Controller {
		static targets = ['btn', 'content'];
		// static values = { value: String };

		toggle = (e) => {
			this.disableAll();
			var i = index(e.currentTarget.parentNode);
			this.setActive(i);
		};
		disableAll = () => {
			this.btnTargets.forEach((btn) => {
				btn.classList.remove('btn--secondary');
				btn.classList.add('btn--gray');
			});
			this.contentTargets.forEach((content) => {
				content.classList.remove('is-active');
			});
		};
		setActive = (index) => {
			console.log(index);

			this.btnTargets[index].classList.remove('btn--gray');
			this.btnTargets[index].classList.add('btn--secondary');
			this.contentTargets[index].classList.add('is-active');
		};
	};
};
