import { Controller } from '@hotwired/stimulus';
import { useIntersection, useWindowResize } from 'stimulus-use';
import EmblaCarousel from 'embla-carousel';
import Autoplay from 'embla-carousel-autoplay';
import AutoScroll from 'embla-carousel-auto-scroll';
import { disablePrevNextBtns } from './carousel/prevAndNextButtons';
import { setupDotBtns, generateDotBtns, selectDotBtn, disableDots } from './carousel/dotButtons';
import { MQ } from '../tools/MQ';

export const create = () => {
	return class extends Controller {
		static targets = ['prevButton', 'nextButton', 'viewport', 'dots'];
		static values = { init: String, settings: Object, autoplay: Object, autoScroll: Object };

		connect() {
			useIntersection(this);
			useWindowResize(this);
		}

		appear = () => {
			var shouldInit = this.initValue ? MQ(this.initValue) : true;
			if (!this.carousel && shouldInit) {
				this.element.classList.add('is-initialized');

				const optionsDefault = {
					loop: false,
					align: 'start',
					containScroll: 'trimSnaps',
					skipSnaps: true,
					dragFree: true,
					speed: 20,
					...this.settingsValue,
				};

				// Setup plugins array
				const plugins = [];

				// Add autoplay plugin if configured
				if (this.hasAutoplayValue && Object.keys(this.autoplayValue).length > 0) {
					const autoplayOptions = {
						delay: 4000,
						stopOnInteraction: true,
						stopOnMouseEnter: false,
						...this.autoplayValue,
					};
					plugins.push(Autoplay(autoplayOptions));
				}

				// Add auto-scroll plugin if configured
				if (this.hasAutoScrollValue && Object.keys(this.autoScrollValue).length > 0) {
					const autoScrollOptions = {
						speed: 1,
						stopOnMouseEnter: true,
						stopOnInteraction: false,
						...this.autoScrollValue,
					};
					plugins.push(AutoScroll(autoScrollOptions));
				}

				this.carousel = EmblaCarousel(this.viewportTarget, optionsDefault, plugins);
				this.slides = this.carousel.slideNodes();

				// arrows
				const { update, setActiveSlides, loadNext, setActiveState } = this;
				this.carousel.on('init', () => update());
				this.carousel.on('reInit', () => update());

				// active slide
				this.carousel.on('select', () => setActiveSlides());

				// helper class for pointer-events: none when dragging
				const onScroll = () => this.viewportTarget.classList.add('is-dragging');
				this.carousel.on('pointerDown', () => this.carousel.on('scroll', onScroll));
				this.carousel.on('pointerUp', () => {
					this.carousel.off('scroll', onScroll);
					this.viewportTarget.classList.remove('is-dragging');
				});

				// load image on next slide
				loadNext();
				this.carousel.on('select', () => loadNext());

				// active
				setActiveState();
			} else if (this.carousel && !shouldInit) {
				// Destroy
				this.element.classList.remove('is-initialized');
				this.carousel.destroy();
				this.carousel = null;
			}
		};

		windowResize() {
			if (this.carousel) {
				this.setActiveState();
			}
		}

		setActiveState = () => {
			this.isScrollable = this.carousel.internalEngine().scrollSnaps.length > 1;
			this.carousel.reInit({ active: this.isScrollable });
			this.element.classList[this.isScrollable ? 'remove' : 'add']('is-disabled');
		};
		update = () => {
			// arrows
			if (this.hasPrevButtonTarget && this.hasNextButtonTarget) {
				var disablePrevAndNextBtns = disablePrevNextBtns(this.prevButtonTarget, this.nextButtonTarget, this.carousel);
				disablePrevAndNextBtns();
				this.carousel.on('select', disablePrevAndNextBtns);
			}

			// dots
			if (this.hasDotsTarget) {
				var dotsArray = generateDotBtns(this.dotsTarget, this.carousel);
				var setSelectedDotBtn = selectDotBtn(dotsArray, this.carousel);
				var disableAllDots = disableDots(this.dotsTarget, this.carousel);
				setupDotBtns(dotsArray, this.carousel);
				setSelectedDotBtn();
				disableAllDots();
				this.carousel.on('select', setSelectedDotBtn);
			}
		};

		setActiveSlides = () => {
			this.slides.forEach((slide, i) => slide.classList[this.carousel.selectedScrollSnap() == i ? 'add' : 'remove']('is-active'));
		};

		loadNext = () => {
			this.slides[this.carousel.selectedScrollSnap() + 1]?.querySelector('[loading="lazy"]')?.removeAttribute('loading');
		};

		prev(event) {
			event.preventDefault();
			this.carousel.scrollPrev();
		}

		next(event) {
			event.preventDefault();
			this.carousel.scrollNext();
		}
	};
};
