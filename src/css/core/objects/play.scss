@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.play,
.lty-playbtn,
.ltv-playbtn.ltv-playbtn {
	position: absolute;
	top: 50%;
	left: 50%;
	width: 50%;
	max-width: 7rem;
	aspect-ratio: 24/17;
	border: none;
	border-radius: 25% / 40%;
	background: none;
	background-image: url(variables.$img-path + 'illust/yt-play.png');
	background-size: contain;
	pointer-events: none;
	transition: opacity variables.$t;
	transform: translate(-50%, -50%);

	// STATES
	.video.is-playing &,
	.video:has(.lyt-activated) &,
	.video:has(.ltv-activated) & {
		display: none;
	}

	// HOVERS
	.hoverevents .video:hover & {
		opacity: 0.9;
	}
}
