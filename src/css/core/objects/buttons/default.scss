@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.btn {
	--btn-fs: 1.6rem;
	--btn-bg: #{variables.$color-primary};
	--btn-c: #{variables.$color-btn};
	--btn-bdc: transparent;
	--btn-hover-bg: #{variables.$color-hover-primary};
	--btn-hover-c: #{variables.$color-btn};
	--btn-hover-bdc: transparent;
	--btn-gap: 0.8rem;
	--btn-icon-size: 2rem;
	--btn-fw: 400;
	--btn-radius: #{variables.$border-radius};
	--btn-h: 4.4rem;
	--btn-p: 0.8rem 2.4rem;
	$s: &;
	display: inline-block;
	vertical-align: middle;
	padding: 0;
	border: 0;
	background: none;
	text-decoration: none;
	&__text {
		position: relative;
		display: flex;
		gap: var(--btn-gap);
		justify-content: center;
		align-items: center;
		min-height: var(--btn-h);
		padding: var(--btn-p);
		border: 0.1rem solid var(--btn-bdc);
		border-radius: var(--btn-radius);
		background-color: var(--btn-bg);
		color: var(--btn-c);
		font-weight: var(--btn-fw);
		font-size: var(--btn-fs);
		line-height: 100%;
		text-align: center;
		text-decoration: none;
		transition: background-color variables.$t, border-color variables.$t, color variables.$t;
	}
	&__icon {
		flex: 0 0 auto;
		width: var(--btn-icon-size);
		height: var(--btn-icon-size);
	}
	&__arrow {
		transition: transform variables.$t;
	}

	// VARIANT
	&--block {
		display: block;
		min-width: 100%;
	}
	&--secondary {
		--btn-bg: #{variables.$color-secondary};
		--btn-hover-bg: #{variables.$color-hover-secondary};
	}
	&--square {
		--btn-radius: 1rem;
	}
	&--icon,
	&--gray {
		--btn-hover-bdc: #{variables.$color-btn};
		--btn-bg: transparent;
		--btn-c: #{variables.$color-gray};
		--btn-bdc: #{variables.$color-gray};
		--btn-hover-bg: transparent;
	}
	&--icon {
		--btn-p: 0.8rem 1.1rem;
	}
	&--bd {
		--btn-bdc: #{variables.$color-btn};
		--btn-hover-bdc: #{variables.$color-btn};
		--btn-bg: transparent;
		--btn-hover-bg: #{variables.$color-hover-tertiary};
	}
	&--transparent {
		--btn-bdc: transparent;
		--btn-bg: transparent;
		--btn-hover-bg: #{variables.$color-hover-tertiary};
	}
	&--loader &__text::before {
		content: '';
		position: absolute;
		top: 50%;
		left: 50%;
		width: calc(var(--btn-h) / 3);
		height: calc(var(--btn-h) / 3);
		margin: calc(var(--btn-h) / -6) 0 0 calc(var(--btn-h) / -6);
		border: 0.1rem solid var(--btn-c);
		border-top-color: transparent;
		border-radius: 50%;
		opacity: 0;
		visibility: hidden;
		transition: opacity variables.$t, visibility variables.$t;
	}
	&--tag {
		--btn-h: 4rem;
	}

	// STATEs
	&:disabled,
	&.is-disabled {
		opacity: 0.5;
		pointer-events: none;
	}
	.is-loading &--loader,
	.hoverevents .is-loading &--loader:hover,
	&--loader.is-loading,
	.hoverevents &--loader:hover.is-loading {
		position: relative;
		pointer-events: none;
		#{$s}__text,
		#{$s}__text .icon-svg {
			color: transparent;
		}
		#{$s}__text::before {
			opacity: 1;
			visibility: visible;
			animation: animation-rotate 0.8s infinite linear;
		}
	}

	@at-root #{&}:is(a, button) {
		cursor: pointer;

		// HOVERS
		.hoverevents &:hover {
			#{$s}__text {
				border-color: var(--btn-hover-bdc);
				background-color: var(--btn-hover-bg);
				color: var(--btn-hover-c);
			}
			#{$s}__arrow {
				transform: translateX(0.5rem);
			}
		}
	}

	// MQ
	@media (config.$xl-down) {
		&--icon {
			--btn-p: 0.6rem 0.7rem;
			--btn-fs: 1.6rem;
			--btn-icon-size: 1.6rem;
			--btn-h: 3.2rem;
		}
	}
	@media (config.$md-up) {
		&--lg {
			--btn-h: 6.4rem;
			--btn-fs: 2.4rem;
		}
	}
}
