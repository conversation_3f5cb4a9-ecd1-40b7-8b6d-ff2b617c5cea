@use 'config';
@use 'base/variables';

.switch {
	position: relative;
	display: flex;
	align-items: center;
	user-select: none;
	&__inp {
		position: absolute;
		left: -500rem;
	}
	&__label {
		margin-right: auto;
		padding-right: 2rem;
		color: rgba(variables.$color-white, 0.6);
	}
	&__inner {
		position: relative;
		display: flex;
		border-radius: 3rem;
		background: variables.$color-gray10;
		cursor: pointer;
		transition: background-color variables.$t;
	}
	&__text {
		display: flex;
		flex: 1 1 auto;
		justify-content: center;
		align-items: center;
		width: 2.1rem;
		height: 2.3rem;
		text-align: center;
		opacity: 0;
		transition: opacity variables.$t;
		.icon-svg {
			position: relative;
			z-index: 1;
			svg {
				fill: currentcolor;
			}
		}
	}
	&__tool {
		position: absolute;
		top: 0.2rem;
		bottom: 0.2rem;
		left: 0.2rem;
		width: 1.9rem;
		border-radius: 50%;
		background: rgba(variables.$color-white, 0.8);
		transition: transform variables.$t;
	}

	// MODIF
	&__text--right {
		opacity: 1;
	}

	// STATES
	&__inp:focus + &__inner {
		border-color: variables.$color-text;
	}
	&__inp:checked + &__inner {
		background-color: variables.$color-hover-secondary;
	}

	&__inp:checked + &__inner &__tool {
		transform: translateX(100%);
	}
	&__inp:checked + &__inner &__text--left {
		opacity: 1;
	}
	&__inp:checked + &__inner &__text--right {
		opacity: 0;
	}
	&__inp:disabled + &__inner {
		opacity: 0.5;
		pointer-events: none;
	}
}
