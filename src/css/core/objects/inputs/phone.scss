@use 'base/variables';
@use 'core/objects/inputs/extends';

.js-phone-input {
	$input-padding-horizontal: 1.6rem;
	$input-padding-vertical: 2rem;
	$flag-padding-horizontal: 2rem;
	$flag-padding-vertical: 1.6rem;
	&__base {
		position: absolute;
		left: 0;
		opacity: 0;
		pointer-events: none;
	}
	&__wrapper {
		position: relative;
		z-index: 0;
		display: flex;
		flex-direction: row-reverse;
		font-size: 1.5rem;
		line-height: variables.$line-height;
	}
	&__flag {
		display: flex;
		align-items: center;
		padding: 0 3rem;
		border-bottom: 0.1rem solid variables.$color-text;
		background-image: url(variables.$svg-select);
		background-position: top 50% right 1rem;
		background-repeat: no-repeat;
		background-size: 1rem 0.5rem;
		pointer-events: none;
		img {
			position: absolute;
			top: 50%;
			left: 0;
			width: 2rem;
			height: auto;
			transform: translateY(-50%);
		}
		.validated & {
			border-color: variables.$color-bd;
		}
		.has-error & {
			border-color: variables.$color-red;
		}
	}
	&__input {
		background-color: transparent;
		text-shadow: 0 0.1rem 0.1rem variables.$color-white, 0.1rem 0 0.1rem variables.$color-white, 0 -0.1rem 0.1rem variables.$color-white,
			-0.1rem 0 0.1rem variables.$color-white, -0.1rem -0.1rem 0.1rem variables.$color-white,
			0.1rem 0.1rem 0.1rem variables.$color-white, 0.1rem -0.1rem 0.1rem variables.$color-white,
			-0.1rem 0.1rem 0.1rem variables.$color-white;
		font-variant-numeric: tabular-nums;
		.validated &,
		.validated &:focus {
			border-color: variables.$color-green;
		}
	}
	&__placeholder {
		position: relative;
		top: -0.25rem;
		z-index: -1;
		display: flex;
		align-items: center;
		width: 0;
		color: rgba(variables.$color-text, 0.5);
		font-size: variables.$font-size;
		line-height: variables.$line-height;
		white-space: nowrap;
		pointer-events: none;
		font-variant-numeric: tabular-nums;
	}
	&__select {
		position: absolute;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		z-index: -1;
		border: none;
		background-color: transparent;
		color: transparent;
		appearance: none;
		option {
			color: variables.$color-text;
		}
		&:focus {
			outline: 0;
		}
	}
}
