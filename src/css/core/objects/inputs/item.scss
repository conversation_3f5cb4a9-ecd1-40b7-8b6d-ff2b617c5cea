@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.inp-item {
	position: relative;
	display: inline-block;
	vertical-align: top;
	padding-left: 3.6rem;
	cursor: pointer;
	-webkit-tap-highlight-color: transparent;
	&__inp {
		position: absolute;
		left: -500rem;
	}
	&__text {
		display: block;
		font-size: 1.4rem;
		&::before {
			content: '';
			position: absolute;
			top: -0.1rem;
			left: 0;
			width: 2.4rem;
			height: 2.4rem;
			border: 0.1rem solid variables.$color-text;
			border-radius: 0.4rem;
			transition: border-color variables.$t, background-color variables.$t;
		}
		&::after {
			content: '';
			position: absolute;
			opacity: 0;
			transition: opacity variables.$t;
		}
	}

	// VARIANTS
	&--checkbox &__text {
		&::after {
			top: 0.3rem;
			left: 0.9rem;
			display: inline-block;
			width: 0.6rem;
			height: 1.3rem;
			border-right: 0.2rem solid variables.$color-text;
			border-bottom: 0.2rem solid variables.$color-text;
			transform: rotate(45deg);
		}
	}
	&--radio &__text {
		&::before,
		&::after {
			border-radius: 50%;
		}
		&::after {
			top: 0.6rem;
			left: 0.7rem;
			width: 1rem;
			height: 1rem;
			background: variables.$color-text;
		}
	}

	// STATES
	// focus
	&__inp:focus + &__text {
		&::before {
			border-color: variables.$color-gray;
		}
	}
	// checked
	&__inp:checked + &__text {
		&::before {
			background: variables.$color-primary;
		}
		&::after {
			opacity: 1;
		}
	}
	// disabled
	&__inp:disabled + &__text {
		color: variables.$color-gray;
		cursor: default;
		&::before {
			border-color: variables.$color-bd;
			background: variables.$color-bg;
		}
	}
	// error
	.has-error &__text {
		&::before {
			border-color: variables.$color-red;
		}
	}
	.has-error &--checkbox &__inp:checked + &__text,
	&--checkbox.has-error &__inp:checked + &__text {
		&::before {
			border-color: variables.$color-red;
			background: variables.$color-red;
		}
	}
	.has-error &--radio &__inp:checked + &__text,
	&--radio.has-error &__inp:checked + &__text {
		&::before {
			border-color: variables.$color-red;
		}
		&::after {
			background: variables.$color-red;
		}
	}
	// error focus
	.has-error &__inp:focus + &__text,
	&.has-error &__inp:focus + &__text {
		&::before {
			border-color: variables.$color-red;
		}
	}
}
