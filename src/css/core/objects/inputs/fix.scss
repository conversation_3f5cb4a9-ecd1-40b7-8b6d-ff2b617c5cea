@use 'base/variables';
@use 'core/objects/inputs/extends';

.inp-fix {
	position: relative;
	display: block;

	// MODIF
	// Input s tlačítkem uvnitř
	// &:has(&__btn) {
	// 	@extend %inp;
	// 	display: flex;
	// 	padding: 0;
	// 	outline: variables.$focus-outline-width variables.$focus-outline-style transparent;
	// 	outline-offset: -(variables.$focus-outline-width);
	// 	.inp-text {
	// 		flex: 1 1 auto;
	// 		border: 0;
	// 		outline: none;
	// 	}
	// }

	// STATEs
	.has-error & {
		color: variables.$color-red;
	}
	.has-warning & {
		color: variables.$color-orange;
	}
	.has-ok & {
		color: variables.$color-green;
	}
}
