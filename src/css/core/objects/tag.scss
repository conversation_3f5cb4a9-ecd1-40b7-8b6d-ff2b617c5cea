@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.tag {
	display: inline-flex;
	gap: 0.9rem;
	align-items: center;
	min-height: 4.7rem;
	padding: 0.8rem 2.4rem;
	border-radius: 99rem;
	background: variables.$color-bg;
	color: variables.$color-text;
	font-weight: bold;
	line-height: 1.5;
	&__icon {
		flex: 0 0 auto;
		width: 2.1rem;
	}
	&--blue {
		background: variables.$color-blue80;
	}
	&--yellow {
		background: variables.$color-yellow-100;
	}
}
