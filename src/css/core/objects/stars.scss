@use 'base/variables';

.stars {
	--icon-size: 1.3rem;
	position: relative;
	display: inline-flex;
	align-items: center;
	.icon-svg {
		flex: 0 0 auto;
		width: var(--icon-size);
	}
	&__icons {
		display: inline-flex;
		gap: 0.2rem;
		align-items: center;
		height: var(--icon-size);
		color: #dcdcdc;
		white-space: nowrap;
		overflow: hidden;
		pointer-events: none;
		&--active {
			position: absolute;
			top: 0;
			left: 0;
			width: 0;
			color: #ffc107;
		}
	}
}
