@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.img {
	$s: &;
	display: block;
	width: 100%;
	height: fit-content;
	aspect-ratio: 1/1;
	object-fit: cover;
	// position: relative;
	// display: block;
	// overflow: hidden;
	// &::before {
	// 	content: '';
	// 	display: block;
	// 	padding-top: 100%;
	// 	pointer-events: none;
	// }
	// > *:is(img, lite-youtube, lite-vimeo, video, iframe),
	// &__media {
	// 	position: absolute;
	// 	top: 0;
	// 	left: 0;
	// 	width: 100%;
	// 	height: 100%;
	// 	@supports (object-fit: cover) {
	// 		object-fit: cover;
	// 	}
	// }

	// MODIF
	&--3-4 {
		aspect-ratio: 3/4;
	}
	&--4-3 {
		aspect-ratio: 4/3;
	}
	&--3-1 {
		aspect-ratio: 3/1;
	}
	&--3-5 {
		aspect-ratio: 3/5;
	}
	&--16-9 {
		aspect-ratio: 16/9;
	}

	// &--contain > *:is(img, lite-youtube, lite-vimeo, video, iframe),
	// &--contain &__media {
	// 	object-fit: contain;
	// }
	&--contain {
		object-fit: contain;
	}
	&--fit {
		width: 100%;
		height: 100%;
	}
}
