@forward 'utilities/clearfix';
@forward 'utilities/image';
@forward 'utilities/screen-reader';
@forward 'utilities/text';
@forward 'utilities/tracy';
@forward 'utilities/visibility';
@forward 'utilities/sizing';
@use 'base/mixins';
@use 'base/variables';
@use 'base/functions';

// Inspirujeme se z emmetího cheat-sheetu: https://docs.emmet.io/cheat-sheet/

$utilities: (
	'align': (
		property: vertical-align,
		class: va,
		values: (
			t: top,
			m: middle,
			b: bottom
		)
	),
	'display': (
		responsive: true,
		property: display,
		class: d,
		values: (
			n: none,
			b: block
		)
	),
	'float': (
		property: float,
		class: fl,
		values: (
			l: left,
			r: right
		)
	),
	'font-style': (
		property: font-style,
		class: fs,
		values: (
			i: italic
		)
	),
	'font-weight': (
		property: font-weight,
		class: fw,
		values: (
			l: 300,
			n: normal,
			b: bold
		)
	),
	'text-align': (
		property: text-align,
		class: ta,
		values: (
			l: left,
			r: right,
			c: center,
			j: justify
		)
	),
	'text-transform': (
		property: text-transform,
		class: tt,
		values: (
			l: lowercase,
			u: uppercase,
			c: capitalize
		)
	),
	'white-space': (
		property: white-space,
		class: whs,
		values: (
			nw: nowrap
		)
	),
	'margin-top': (
		property: margin-top,
		class: mt,
		responsive: true,
		values: (
			'md': functions.spacing('md'),
			'lg': functions.spacing('lg')
		)
	),
	'margin-bottom': (
		property: margin-bottom,
		class: mb,
		responsive: true,
		values: (
			'0': functions.spacing('0'),
			'xs': functions.spacing('xs'),
			'sm': functions.spacing('sm'),
			'md': functions.spacing('md'),
			'mdxs': calc(functions.spacing('md') + functions.spacing('xs')),
			'lg': functions.spacing('lg'),
			'xl': functions.spacing('xl'),
			'2xl': functions.spacing('2xl'),
			'3xl': functions.spacing('3xl')
		)
	),
	'padding-top': (
		property: padding-top,
		class: pt,
		responsive: true,
		values: (
			'xs': functions.spacing('xs'),
			'sm': functions.spacing('sm'),
			'md': functions.spacing('md'),
			'lg': functions.spacing('lg'),
			'xl': functions.spacing('xl'),
			'2xl': functions.spacing('2xl'),
			'3xl': functions.spacing('3xl')
		)
	),
	'padding-bottom': (
		property: padding-bottom,
		class: pb,
		values: (
			'xs': functions.spacing('xs'),
			'sm': functions.spacing('sm'),
			'md': functions.spacing('md'),
			'lg': functions.spacing('lg'),
			'xl': functions.spacing('xl'),
			'2xl': functions.spacing('2xl'),
			'3xl': functions.spacing('3xl')
		)
	),
	'color': (
		property: color,
		class: c,
		values: (
			'red': variables.$color-red,
			'text': variables.$color-text,
			'orange': variables.$color-orange,
			'green': variables.$color-green
		)
	),
	'background-color': (
		property: background-color,
		class: bgc,
		values: (
			default: variables.$color-bg,
			secondary: variables.$color-bg-secondary,
			tertiary: variables.$color-bg-tertiary,
			'yellow': variables.$color-yellow-100,
			'green': variables.$color-secondary,
			'green-light': variables.$color-green-80,
			'white': variables.$color-white
		)
	),
	'max-width': (
		property: max-width,
		class: maw,
		values: (
			d: variables.$row-main-width,
			c: variables.$row-main-width-content,
			'hp': 87rem,
			'sm': 79rem
		)
	)
);

@include mixins.generate-utilities($utilities);

.u-mb-last-0 > *:last-child:not(.grid) {
	margin-bottom: 0;
}
.u-mt-auto {
	margin-top: auto;
}
.u-ml-auto {
	margin-left: auto;
}
.u-mx-auto {
	margin-right: auto;
	margin-left: auto;
}
.u-mt-0 {
	margin-top: 0;
}
