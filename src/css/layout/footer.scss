@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'config';
@use 'sass:math';

.footer {
	--row-main-width: calc(144.8rem + 2 * var(--row-main-gutter));
	--footer-gap: 2rem;
	padding: functions.spacing('md') 0;
	border-top: 0.1rem solid #f1f1f1;
	background-color: variables.$color-white;
	&__inner {
		display: grid;
		grid-template-columns: 1fr;
		grid-template-areas: 'social' 'menu' 'contact' 'copyrights';
		gap: 4rem var(--footer-gap);
	}
	&__logo {
		flex: 1 0 auto;
	}
	&__menu {
		grid-area: menu;
	}
	&__contacts {
		display: flex;
		grid-area: contact;
		gap: var(--footer-gap);
	}
	&__copyrights {
		grid-area: copyrights;
		color: variables.$color-gray;
		a {
			color: variables.$color-gray;
		}
	}
	&__content {
		font-size: 1.4rem;
		line-height: 150%;
	}
	&__social {
		@extend %reset-ul;
		display: flex;
		grid-area: social;
		gap: 1rem;
		li {
			@extend %reset-ul-li;
		}
		.btn__text {
			font-weight: 700;
			font-size: variables.$font-size;
		}
	}

	// MQ
	@media (config.$md-down) {
		&__social {
			margin-left: -0.8rem;
			.btn__text {
				padding: 0.8rem;
			}
		}
		&__contacts {
			flex-direction: column;
		}
	}
	@media (config.$md-up) {
		--footer-gap: 4.6rem;
		&__inner {
			grid-template-columns: 1fr;
			grid-template-areas: 'menu' 'contact' 'copyrights';
		}
		&__menu,
		&__contacts,
		&__copyrights {
			display: flex;
			gap: var(--footer-gap);
		}
		&__menu,
		&__contacts {
			justify-content: space-between;
		}
		&__copyrights {
			align-items: center;
		}
	}
	@media (config.$xl-up) {
		padding: functions.spacing('2xl') 0 functions.spacing('md');

		&__inner {
			grid-template-columns: minmax(auto, 50.5rem) 1fr auto;
			grid-template-areas: 'menu contact contact' 'copyrights copyrights social';
			gap: 11.7rem var(--footer-gap);
			align-items: center;
		}
		&__menu {
			justify-content: space-between;
		}
		&__contacts {
			justify-self: flex-end;
		}
		&__copyrights {
			justify-content: space-between;
		}
		&__content {
			flex: 1 1 auto;
			text-align: center;
		}
	}
	@media (config.$md-up) and (config.$xl-down) {
		&__contact,
		&__menu .m-footer {
			flex: 1 0 percentage(math.div(1, 3));
			max-width: calc(percentage(math.div(1, 3)) - var(--footer-gap));
		}
		&__social {
			display: none;
		}
	}
}
