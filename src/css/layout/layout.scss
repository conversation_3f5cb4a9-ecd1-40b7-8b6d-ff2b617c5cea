html {
	--scroll-offset: var(--header-height);
	display: flex;
	flex-direction: column;
	box-sizing: border-box;
	min-height: 100%;
	background: #f7f7f7;
	scroll-behavior: smooth;
	scrollbar-gutter: stable;
	&.tracy-bs-visible.tracy-bs-visible {
		overflow: visible;
	}
}
*:target {
	scroll-margin-top: var(--scroll-offset);
}
*,
*::before,
*::after {
	box-sizing: inherit;
}
body {
	position: relative;
	display: flex;
	flex: 1;
	flex-direction: column;
	min-width: 32rem;
	overflow-y: scroll;
}
:first-child {
	margin-top: 0;
}
