@use 'config';
@use 'base/variables';

html {
	color: variables.$color-text;
	font-size: 62.5%;
}

body {
	font-family: variables.$font-primary;
	font-size: variables.$font-size;
	line-height: variables.$line-height;
}

// Headings
h1,
.h1,
h2,
.h2,
h3,
.h3,
h4,
.h4,
h5,
.h5,
h6,
.h6 {
	margin: 1em 0 0.6em;
	font-family: variables.$font-secondary;
	font-weight: 700;
	font-size: var(--font-size-mobile);
	line-height: 1.2;
	.b-annot--author & {
		--font-size-desktop: 3.2rem;
		font-weight: 400;
	}
	@media (config.$md-up) {
		font-size: var(--font-size-desktop);
	}
}
h1,
.h1 {
	--font-size-desktop: 5.6rem;
	--font-size-mobile: 5rem;
	line-height: 1.1;
	.b-annot--author & {
		font-weight: 300;
	}
}
h2,
.h2 {
	--font-size-desktop: 4.8rem;
	--font-size-mobile: 4rem;
}
h3,
.h3 {
	--font-size-desktop: 4rem;
	--font-size-mobile: 3.2rem;
}
h4,
.h4 {
	--font-size-desktop: 3.2rem;
	--font-size-mobile: 2.4rem;
	line-height: 1.3;
}
h5,
.h5 {
	--font-size-desktop: 2.4rem;
	--font-size-mobile: 1.8rem;
	line-height: 1.4;
}
h6,
.h6 {
	--font-size-desktop: 1.8rem;
	--font-size-mobile: 1.6rem;
	line-height: 1.4;
}

// Paragraph
p {
	margin: 0 0 variables.$typo-space-vertical;
}
hr {
	height: 0.1rem;
	margin: variables.$typo-space-vertical 0;
	border: solid variables.$color-bd;
	border-width: 0.1rem 0 0;
	overflow: hidden;
}

// Blockquote
blockquote {
	margin: 1.8rem 0;
	padding: 1rem 2rem;
	border-radius: 1rem;
	background: variables.$color-yellow-200;
	font-size: 1.8rem;
	p:last-child {
		margin-bottom: 0;
	}
	@media (config.$md-up) {
		margin: 3.2rem 0;
		padding: 4rem 7.2rem;
		font-size: 3.2rem;
	}
	@media (config.$xl-up) {
		max-width: 80%;
		margin: 5rem 0 9.6rem;
		.b-content--blog & {
			max-width: none;
			margin: 3.2rem -7.2rem;
		}
	}
}

// Links
a,
.as-link {
	--color-link: #{variables.$color-link};
	--color-hover: #{variables.$color-hover};
	--color-link-decoration: transparent;
	--color-hover-decoration: transparent;
	color: var(--color-link);
	text-decoration: underline;
	text-decoration-color: var(--color-link-decoration);
	text-underline-offset: 0.3rem;
	transition: color variables.$t, text-decoration-color variables.$t;
	-webkit-tap-highlight-color: transparent;
	.hoverevents &:hover {
		color: var(--color-hover);
		text-decoration-color: var(--color-hover-decoration);
	}
}

.as-link {
	cursor: pointer;
}

// Lists
*:is(ul, ol, dl) {
	margin: 0 0 variables.$typo-space-vertical;
	padding: 0;
	list-style: none;
}
li {
	margin: 0 0 calc(variables.$typo-space-vertical / 4);
	padding: 0 0 0 2.6rem;
}
ul {
	li {
		background-image: url(variables.$svg-bullet);
		background-position: 0.8rem 0.65em;
		background-repeat: no-repeat;
		background-size: 0.5rem 0.5rem;
	}
}
ol {
	counter-reset: item;
	li {
		position: relative;
		&::before {
			content: counter(item) '.';
			counter-increment: item;
			position: absolute;
			top: 0;
			left: 0;
		}
	}
	ol {
		li {
			&::before {
				content: counter(item, lower-alpha) '.';
			}
		}
	}
}
dt {
	margin: 0;
	font-weight: bold;
}
dd {
	margin: 0 0 calc(variables.$typo-space-vertical / 2);
	padding: 0;
}

// Tables
table {
	--table-x-padding: 2rem;
	--table-y-padding: 1.5rem;
	--table-bd-color: #{variables.$color-bd};
	clear: both;
	border-collapse: collapse;
	border-spacing: 0;
	empty-cells: show;
	width: 100%;
	margin: 0 0 variables.$typo-space-vertical;
	// border: 0.1rem solid var(--table-bd-color);
}
caption {
	padding: 0 0 1rem;
	font-weight: bold;
	text-align: left;
	caption-side: top;
}
*:is(td, th) {
	vertical-align: top;
	padding: var(--table-y-padding) var(--table-x-padding);
	border: 0.1rem solid var(--table-bd-color);
	border-width: 0.1rem 0;
	&:first-child {
		padding-left: 0;
	}
	&:last-child {
		padding-right: 0;
	}
}
th {
	font-weight: bold;
	text-align: left;
}
thead th {
	background: variables.$color-bg;
}

// Image
figure {
	margin-bottom: variables.$typo-space-vertical;
}
figcaption {
	margin-top: 0.5em;
}

img {
	max-width: 100%;
	height: auto;
}
