@use 'config';

:root {
	--grid-gutter: 2.4rem;
	--row-main-gutter: var(--grid-gutter);
	--row-main-width: calc(138.4rem + 2 * var(--row-main-gutter));
	--row-main-width-author: calc(108rem + 2 * var(--row-main-gutter));
	--row-main-width-content: calc(85rem + 2 * var(--row-main-gutter));
	--font-size: 1.6rem;
	--font-size-sm: 1.4rem;
	@media (config.$md-up) {
		--grid-gutter: 3.2rem;
		--font-size: 1.8rem;
	}
}

// Colors
$color-yellow-100: #ffd96a;
$color-yellow-200: #ffcc33;
$color-yellow: $color-yellow-200;

$color-green-100: #93e6b4;
$color-green-80: #b5efcd;
$color-green-200: #5ad88c;
$color-green: $color-green-200;

$color-black: #000000;
$color-white: #ffffff;
$color-gray: #818181;
$color-red: #ff6673;
$color-orange: $color-yellow-200;
$color-gray10: #3333331a;
$color-blue80: #01355acc;

$color-primary: $color-yellow-100;
$color-secondary: $color-green-100;
$color-hover-primary: $color-yellow;
$color-hover-secondary: $color-green;
$color-hover-tertiary: #ebebeb;

$color-text: $color-black;
$color-btn: #1a1a1a;
$color-bd: #dddddd;
$color-bd-title: #333333;
$color-bg: #f8f8f8;
$color-bg-secondary: #bef0d2;
$color-bg-tertiary: #ffe9a6;
$color-link: #0a68ff;
$color-hover: #004ecc;
// $color-facebook: #3b5998;
// $color-twitter: #1da1f2;
// $color-google: #dd4b39;
// $color-youtube: #ff0000;
// $color-linkedin: #0077b5;
// $color-instagram: #c13584;
// $color-pinterest: #bd081c;

// Font
$font-system: matter, blinkmacsystemfont, 'Segoe UI', roboto, helvetica, arial, sans-serif;
$font-primary: $font-system;
$font-secondary: $font-primary;
$font-size: var(--font-size);
$font-size-sm: var(--font-size-sm);
$line-height: calc(28 / 18);

// Typography
$typo-space-vertical: 1.25em;

// Radius
$border-radius: 10rem;

// Focus
$focus-outline-color: $color-bd;
$focus-outline-style: solid;
$focus-outline-width: 0.1rem;

// Spacing
$utils-spacing: (
	'0': 0,
	'xs': 1rem,
	'sm': 2rem,
	'md': 4rem,
	'lg': 6rem,
	'xl': 8rem,
	'2xl': 10rem,
	'3xl': 12rem,
	'4xl': 14rem,
	'5xl': 15rem
);

// Grid
$grid-columns: 12;
$grid-gutter: var(--grid-gutter);
$row-main-width: var(--row-main-width);
$row-main-width-content: var(--row-main-width-content);
$row-main-width-author: var(--row-main-width-author);
$row-main-gutter: var(--row-main-gutter);

// Paths
$img-path: map-get(config.$paths, 'images');
$fonts-path: map-get(config.$paths, 'fonts');

// Transitions
$t: 0.3s;

// SVGs
$svg-bullet: 'data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%204%204%22%3E%3Ccircle%20cx%3D%222%22%20cy%3D%222%22%20r%3D%222%22%2F%3E%3C%2Fsvg%3E';
$svg-select: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 10 5'%3E%3Cpath d='M10 0L5 5 0 0'/%3E%3C/svg%3E%0A";
$svg-submenu: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='14' height='14' fill='none'%3E%3Cpath fill='%23000' d='M6.1136 11.263a.8137.8137 0 0 0 1.1502 0l4.875-4.875a.8137.8137 0 0 0 0-1.1502.8137.8137 0 0 0-1.1502 0L6.6875 9.539 2.3863 5.2403a.8137.8137 0 0 0-1.1502 0 .8137.8137 0 0 0 0 1.1502l4.875 4.875.0025-.0025Z'/%3E%3C/svg%3E";
