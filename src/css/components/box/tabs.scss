@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-tabs {
	&__list {
		@extend %reset-ul;
		display: flex;
		flex-wrap: wrap;
		gap: 1rem;
		li {
			@extend %reset-ul-li;
		}
	}
	&__fragment:not(.is-active) {
		position: absolute;
		left: -500rem;
	}
	&__content {
		padding: 3.2rem;
		border-radius: 1rem;
		background: variables.$color-white;
		box-shadow: 0 0 14px 0 #0000001a;
		*:is(h2, h3) {
			--font-size-desktop: 2.4rem;
			--font-size-mobile: 1.8rem;
		}
		*:is(h4, h5, h6) {
			--font-size-desktop: 1.8rem;
			--font-size-mobile: 1.6rem;
		}
	}

	// MQ
	@media (config.$md-up) {
		&__list {
			flex-direction: column;
			gap: 2rem;
			padding-top: 1rem;
			.btn {
				width: 100%;
			}
		}
		&__grid {
			flex-wrap: nowrap;
		}
		&__cell-tabs {
			flex: 0 0 18rem;
		}
		&__cell-content {
			flex: 1 1 auto;
		}
		&__content {
			padding: 5rem 6rem;
		}
	}
}
