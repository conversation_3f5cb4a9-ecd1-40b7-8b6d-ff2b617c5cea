@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-casestudy {
	display: flex;
	flex-direction: column;
	border-radius: 1rem;
	background: variables.$color-white;
	overflow: clip;
	box-shadow: 0 0 1rem 0 rgba(0, 0, 0, 0.1);
	&__main {
		display: flex;
		flex: 1 1 auto;
		flex-direction: column;
		justify-content: space-between;
		padding: 2.4rem 3.2rem 3.2rem;
		box-shadow: 0 0 0.4rem 0 rgba(0, 0, 0, 0.25);
	}
	&__title {
		--font-size-desktop: 2rem;
		--font-size-mobile: 1.8rem;
	}
	&__link {
		--color-link: #{variables.$color-text};
		--color-hover: #{variables.$color-text};
		--color-link-decoration: transparent;
		--color-hover-decoration: var(--color-hover);
		font-size: 1.6rem;
		span {
			display: inline-block;
			vertical-align: baseline;
			transition: transform variables.$t;
		}
		&:hover {
			span {
				transform: translateX(0.5rem);
			}
		}
	}
	&__img {
		overflow: hidden;
		.img {
			transition: transform variables.$t;
		}
	}
	.hoverevents &:hover &__img {
		.img {
			transform: scale(1.05);
		}
	}
	.btn {
		--btn-fw: 700;
	}
}
