@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-annot {
	padding: 5rem 0;

	&__title {
		margin: 0 0 1.2rem;
	}

	&__desc {
		margin: 0 0 1.8rem;
	}

	&__btn {
		--btn-fw: 700;
	}

	&__right {
		border-radius: 1rem;
		overflow: hidden;
	}

	&__box {
		display: flex;
		gap: 3.2rem;
	}

	&__contacts {
		display: flex;
		flex-wrap: wrap;
		gap: 1rem;
		align-items: center;

		.btn__text {
			font-weight: normal;
		}
	}

	// MODIF
	.u-bgc-yellow &__btn {
		--btn-bg: #{variables.$color-yellow-200};
		--btn-hover-bg: #{variables.$color-secondary};
	}

	&--hp &__desc > p {
		margin-bottom: 3.2rem;
	}

	&--hp &__box {
		align-items: flex-start;
	}

	&--author &__title {
		font-weight: 300;
	}

	// MQ
	@media (config.$md-down) {
		&__box {
			flex-direction: column;
		}
	}

	@media (config.$md-up) {
		&__title {
			margin: 0 0 2.4rem;
		}

		&__desc {
			margin: 0 0 3.2rem;
		}

		&__grid {
			flex-direction: row-reverse;
		}

		&__right {
			border-radius: 2rem;
		}

		&__cnt {
			max-width: calc(50% - 1.6rem);
		}

		&--hp &__cnt {
			position: sticky;
			top: 0;
		}
	}

	@media (config.$lg-up) {
		padding: 10rem 0;

		&__box {
			gap: 0 6rem;
		}

		&__cnt {
			max-width: 41.7rem;
		}
	}

	@media (config.$xl-up) {
		&__box {
			gap: 0 16.7rem;
		}
	}
}
