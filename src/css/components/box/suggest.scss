@use 'base/variables';
@use 'base/functions';
@use 'base/extends';

.b-suggest {
	position: absolute;
	top: calc(100% + 6.4rem);
	right: 0;
	left: 0;
	z-index: 50;
	max-width: variables.$row-main-width-content;
	margin: 0 auto;
	padding: functions.spacing('sm');
	border: 0.1rem solid variables.$color-bd;
	border-radius: 1rem;
	background: variables.$color-white;
	opacity: 0;
	visibility: hidden;
	transition: opacity variables.$t, visibility 0s variables.$t;
	box-shadow: 0 0 0.4rem 0 rgba(0, 0, 0, 0.25);
	&__list {
		@extend %reset-ul;
		margin: 0 functions.spacing('sm') * -1;
		border-top: 0.1rem solid variables.$color-bd;
		// &:first-child {
		// 	margin-top: functions.spacing('sm') * -1;
		// }
	}
	&__item {
		@extend %reset-ul-li;
	}
	&__link {
		display: block;
		padding: functions.spacing(xs) functions.spacing('sm');
		transition: color variables.$t, background-color variables.$t;
	}

	// STATEs
	&.is-visible {
		opacity: 1;
		visibility: visible;
		transition-delay: 0s, 0s;
	}

	.is-selected &__link,
	.hoverevents &__link:hover {
		background: variables.$color-bg;
	}
}
