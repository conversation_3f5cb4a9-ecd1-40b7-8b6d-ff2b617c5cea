@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-card {
	position: relative;
	aspect-ratio: 250 / 420;
	border-radius: 10px;
	border-radius: 0.8rem;
	background-color: #ffffff;
	overflow: hidden;

	&__link {
		display: block;
		width: 100%;
		height: 100%;
	}

	img {
		width: 100%;
		height: 100%;
		object-fit: cover;
		transition: transform variables.$t;
	}
	&:hover img {
		transform: scale(1.03);
		transform-origin: center center;
	}

	.tag {
		min-height: 1.9rem;
		padding: 0 0.8rem;
		font-weight: 400;
	}

	&__title {
		margin: 0;
		font-size: 1.1rem;
		.tag {
			position: absolute;
			top: 1rem;
			left: 0.8rem;
			z-index: 1;
			color: variables.$color-white;
		}
	}

	.c-tags {
		position: absolute;
		bottom: 0.6rem;
		left: 0.8rem;
		display: flex;
		flex-wrap: wrap;
		gap: 0.4rem;

		.tag {
			font-size: 0.8rem;
		}
	}

	@media (config.$lg-down) {
		&:nth-child(2) {
			margin-top: 2.6rem;
		}

		&:nth-child(3) {
			margin-top: -2.6rem;
		}

		&:nth-child(5) {
			margin-top: -2.6rem;
		}
	}

	@media (config.$lg-up) {
		&__title {
			top: 1.5rem;
			left: 1.3rem;
			font-size: 1.2rem;
		}

		.tag {
			min-height: 2.8rem;
			font-size: 1.2rem;
		}

		.c-tags {
			bottom: 0.9rem;
			left: 1.3rem;
			gap: 0.7rem;

			.tag {
				font-size: 1.2rem;
			}
		}

		&:nth-child(2) {
			margin-top: 4rem;
		}

		&:nth-child(3) {
			margin-top: 1.8rem;
		}

		&:nth-child(4) {
			margin-top: -4rem;
		}

		&:nth-child(6) {
			margin-top: -1.8rem;
		}
	}
}
