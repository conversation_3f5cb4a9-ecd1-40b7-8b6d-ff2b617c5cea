@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-contact-person {
	&__box {
		border-radius: 1rem;
		box-shadow: 0 0 14px 0 #0000001a;
	}
	.f-contact {
		padding: 3.2rem;
	}
	&__person {
		height: 100%;
		padding: 3.2rem;
		background: variables.$color-bg;
		img {
			border-radius: 1rem;
		}
	}
	&__tags {
		display: flex;
		flex-direction: column;
		gap: 0.8rem;
		align-items: flex-start;
	}

	// MQ
	@media (config.$md-up) {
		.f-contact {
			padding: 4.8rem 8rem;
		}
		&__person {
			padding: 4.8rem 8rem;
		}
	}
	@media (config.$lg-up) {
		&__grid {
			flex-wrap: nowrap;
		}
		&__left {
			flex: 1 1 auto;
		}
		&__right {
			flex: 0 0 40rem;
		}
	}
	@media (config.$xl-up) {
		&__right {
			flex: 0 0 44rem;
		}
	}
}
