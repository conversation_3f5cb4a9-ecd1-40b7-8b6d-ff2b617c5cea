@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-branches-map {
	padding: 5.5rem 0 6.5rem;
	&__title {
		margin: 0 0 2.2rem;
	}
	&__annot {
		margin: 0 0 2.2rem;
	}
	&__branches {
		margin: 0 0 4.4rem;
		.grid {
			--grid-x-spacing: 1.5rem;
			--grid-y-spacing: 1.5rem;
		}
	}
	&__branch {
		display: flex;
		gap: 0.8rem;
		align-items: center;
		min-height: 3.7rem;
		padding: 0.6rem 1.4rem;
		border-radius: 10rem;
		background: variables.$color-yellow-100;
		font-size: 1.5rem;
	}
	&__count {
		display: inline-flex;
		justify-content: center;
		align-items: center;
		width: 2.5rem;
		aspect-ratio: 1/1;
		border: 0.2rem solid variables.$color-white;
		border-radius: 50%;
		background: variables.$color-yellow-200;
		color: #7f5f00;
		line-height: 1;
	}
	&__img {
		img {
			object-fit: contain;
		}
	}
	&__btn {
		margin-top: 4.8rem;
		.btn__text {
			font-weight: 700;
		}
	}

	// MQ
	@media (config.$lg-up) {
		display: grid;
		&__img {
			grid-area: 4/1/4/1;
		}
		&__numbers {
			grid-area: 4/1/4/1;
			align-self: flex-end;
		}
	}
}
