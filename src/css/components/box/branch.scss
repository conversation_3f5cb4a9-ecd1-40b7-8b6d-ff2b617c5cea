@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';
@use 'sass:math';

.b-branch {
	--branch-gap: 3.2rem;

	&__list {
		display: flex;
		gap: var(--branch-gap);
	}
	&__item {
		flex: 0 1 100%;
		border-radius: 0.8rem;
		background-color: variables.$color-white;
		overflow: hidden;
	}
	&__img {
		aspect-ratio: 384 / 148;
		img {
			width: 100%;
			height: 100%;
			object-fit: cover;
		}
	}
	&__subtitle {
		margin-bottom: 1.6rem;
		font-size: 2.2rem;
		line-height: 150%;
		letter-spacing: -1.1%;
	}
	&__cnt {
		padding: 2.4rem 3.2rem 3.2rem;
	}
	&__link {
		color: variables.$color-text;
		text-decoration: underline;
	}
	&__address {
		margin-bottom: 1.6rem;
	}
	&__hours {
		margin-bottom: 1.6rem;
	}

	// MQ
	@media (config.$md-down) {
		&__list {
			flex-direction: column;
		}
	}
	@media (config.$md-up) {
		&__title {
			margin-bottom: 5.4rem;
		}
		&__list {
			flex-wrap: wrap;
		}
		&__item {
			flex: 0 1 percentage(math.div(1, 2));
			max-width: calc(percentage(math.div(1, 2)) - var(--branch-gap) / 2);
		}
	}
	@media (config.$lg-up) {
		&__title {
			margin-bottom: 5.4rem;
		}
		&__list {
			flex-wrap: nowrap;
		}
		&__item {
			flex: 0 1 percentage(math.div(1, 3));
		}
	}
}
