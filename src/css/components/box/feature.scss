/* stylelint-disable custom-property-no-missing-var-function */
@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-feature {
	--bg: #{variables.$color-white};
	background: var(--bg);
	transition: border-color variables.$t, background-color variables.$t, --bg variables.$t;
	box-shadow: 0 3px 6px rgba(0, 0, 0, 0.06);
	.grid__cell > & {
		height: 100%;
	}
	&__img {
		margin: 0;
		border-radius: 1rem 1rem 0 0;
		overflow: hidden;
		.img {
			transition: transform variables.$t;
		}
	}
	&__content {
		display: flex;
		flex: 1;
		flex-direction: column;
		gap: 1.6rem;
		padding: 2.5rem 3rem 3rem;
		& > * {
			margin: 0;
		}
	}
	&__link {
		--color-link: #{variables.$color-text};
		--color-hover: #{variables.$color-text};
		text-decoration: none;
	}
	&__annot {
		@include mixins.line-clamp(3);
		margin-bottom: auto;
	}
	&__btn {
		margin-top: auto;
	}
	&:hover &__img {
		.img {
			transform: scale(1.05);
		}
	}

	// MQ
	@media (config.$md-up) {
		display: flex;
		flex-direction: column;
		border-radius: 2rem;

		// HOVERS
		.hoverevents &:hover {
			border-color: variables.$color-gray10;
		}
	}
}
