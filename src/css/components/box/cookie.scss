// @import '@superkoders/cookie/src/css/components/box/cookie';
@use 'config';
@use 'base/variables';

.b-cookie {
	position: fixed;
	top: 0;
	left: 0;
	z-index: 100;
	display: none;
	justify-content: center;
	align-items: flex-end;
	width: 100%;
	min-width: 0;
	height: 100%;
	min-height: 0;
	&__bg {
		position: absolute;
		width: 100%;
		height: 100%;
		background: rgba(0, 0, 0, 0.5);
	}
	&__box {
		position: relative;
		display: flex;
		flex: 1 1 auto;
		flex-direction: column;
		width: 100%;
		min-width: 0;
		max-height: calc(100% - 4rem);
		padding: 1.5em;
		border-radius: 1rem;
		background: variables.$color-white;
		overscroll-behavior: contain;
	}
	&__box-inner {
		margin-right: -2rem;
		padding-right: 2rem;
		overflow-y: auto;
	}

	&__btns {
		display: flex;
		gap: 1.5em;
		justify-content: space-between;
		align-items: center;
		margin-top: 2em;
		p {
			display: flex;
			gap: 1rem;
			margin: 0;
		}
	}

	&__option {
		margin-top: 1em;
		margin-bottom: 1em;
		& + & {
			margin-top: 0;
			padding-top: 1em;
			border-top: 0.1rem solid #cccccc;
		}
	}
	&__option-head {
		display: flex;
		align-items: center;
		cursor: default;
		> span {
			position: relative;
			flex: 1 1 auto;
			padding-right: 1.25em;
			padding-left: 1.25em;
			cursor: default;
			&::before {
				content: '';
				position: absolute;
				top: 0.4em;
				left: 0.21em;
				border-top: 0.357em solid transparent;
				border-bottom: 0.357em solid transparent;
				border-left: 0.357em solid currentcolor;
			}
			strong {
				cursor: pointer;
			}
		}
	}
	&__option-body {
		display: none;
		padding-top: 1em;
		> *:last-child {
			margin-bottom: 0;
		}
	}

	// states
	html:not([data-cookie-state='settings']) & [data-step='2'],
	html[data-cookie-state='settings'] & [data-step='1'] {
		display: none;
	}

	html[data-show-cookie='true'] & {
		display: flex;
	}

	// when toggle is open (active state)
	&__option-head[data-cookie-toggle='true'] > span::before {
		transform: rotate(90deg);
	}
	&__option-head[data-cookie-toggle='true'] + &__option-body {
		display: block;
	}

	// rwd
	@media (config.$md-up) {
		align-items: center;
		padding: 0 1.5em;
		&__box {
			max-width: 90rem;
			padding: 2.5em;
		}
	}
	@media (max-width: 52rem) {
		&__btns {
			flex-direction: column;
			gap: 1em;
			p {
				flex-direction: column;
				width: 100%;
			}
			.btn {
				display: block;
				width: 100%;
			}
		}
	}
}
