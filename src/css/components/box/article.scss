/* stylelint-disable custom-property-no-missing-var-function */
@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

@property --bg {
	syntax: '<color>';
	inherits: true;
	initial-value: #f7f7f7;
}

.b-article {
	transition: border-color variables.$t, background-color variables.$t, --bg variables.$t;
	.grid__cell > & {
		height: 100%;
	}
	&__tags {
		position: relative;
		display: flex;
		gap: 1rem;
		min-height: 4.4rem;
		overflow: clip;
		&::after {
			content: '';
			position: absolute;
			top: 0;
			right: 0;
			bottom: 0;
			width: 4rem;
			background: linear-gradient(to right, transparent, var(--bg));
			pointer-events: none;
		}
		.btn {
			flex: 0 0 auto;
		}
	}
	&__img {
		margin: 0 0 1.6rem;
		border-radius: 1rem;
		overflow: hidden;
		.img {
			transition: transform variables.$t;
		}
	}
	&__content {
		display: flex;
		flex: 1;
		flex-direction: column;
		gap: 1.6rem;
		& > * {
			margin: 0;
		}
	}
	&__link {
		--color-link: #{variables.$color-text};
		--color-hover: #{variables.$color-text};
		text-decoration: none;
	}
	&__annot {
		@include mixins.line-clamp(3);
	}
	&__btn {
		margin-top: auto;
	}

	// MQ
	@media (config.$md-down) {
		padding: var(--grid-gutter) 0 calc(var(--grid-gutter) * 2);
		border-bottom: 0.1rem solid variables.$color-bd;
		.c-articles__item:last-child & {
			padding-bottom: 0;
			border-bottom: none;
		}
	}
	@media (config.$md-up) {
		display: flex;
		flex-direction: column;
		padding: 4rem;
		border: 0.1rem solid transparent;
		border-radius: 2rem;
		background: var(--bg);

		// HOVERS
		.hoverevents &:hover {
			--bg: #{variables.$color-white};
			border-color: variables.$color-gray10;
		}
	}
}
