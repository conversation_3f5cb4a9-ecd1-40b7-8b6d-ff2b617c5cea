@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/mixins';

.b-burger {
	$s: &;
	@include mixins.button-reset;
	position: relative;
	z-index: 1;
	width: 4rem;
	height: 4rem;
	margin-right: -1.2rem;
	outline: none;
	font-size: 0;
	text-align: center;
	transition: background-color variables.$t;
	-webkit-tap-highlight-color: transparent;
	span {
		position: absolute;
		top: calc(50% - 0.1rem);
		left: calc(50% - 0.95rem);
		display: block;
		width: 1.9rem;
		height: 0.2rem;
		border-radius: 0.1rem;
		background: variables.$color-black;
		transition: transform variables.$t, top variables.$t, right variables.$t, width variables.$t, background-color variables.$t;
		&:nth-child(1) {
			top: calc(50% - 0.5rem);
		}
		&:nth-child(2),
		&:nth-child(3) {
			top: 50%;
		}
		&:nth-child(4) {
			top: calc(50% + 0.5rem);
		}
	}

	// STATES
	.is-open & span,
	.is-menu-open & span {
		background: variables.$color-hover;
		&:nth-child(1),
		&:nth-child(4) {
			width: 0;
		}
		&:nth-child(2) {
			width: 1.9rem;
			transform: rotate(45deg);
		}
		&:nth-child(3) {
			width: 1.9rem;
			transform: rotate(-45deg);
		}
	}

	// HOVERS
	.hoverevents &:hover span {
		background: variables.$color-hover;
	}
}
