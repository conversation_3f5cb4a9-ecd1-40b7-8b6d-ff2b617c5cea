@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';
@use 'sass:math';

.b-partners {
	&__list {
		@extend %reset-ul;
		display: flex;
		flex-wrap: wrap;
		gap: 0.5rem;
		justify-content: center;
	}
	&__item {
		@extend %reset-ul-li;
		max-width: 26rem;
		padding: 1.8rem;
		font-size: variables.$font-size-sm;
		line-height: 1.7rem;
		text-align: center;
	}
	&__logo {
		width: auto;
		height: 2.6rem;
		margin-bottom: 1.3rem;
	}

	// MQ
	@media (config.$md-up) {
		&__list {
			gap: functions.spacing('md');
		}
		&__item {
			flex: 0 1 calc((100% - functions.spacing('md')) / 2);
			max-width: calc((100% - functions.spacing('md')) / 2);
		}
	}
	@media (config.$lg-up) {
		&__title {
			margin-bottom: 2.5rem;
		}
		&__item {
			flex: 0 1 calc((100% - 2 * functions.spacing('md')) / 3);
			max-width: calc((100% - 2 * functions.spacing('md')) / 3);
			padding: functions.spacing('sm');
		}
		&__logo {
			margin-bottom: 1.5rem;
		}
	}
	@media (config.$xl-up) {
		&__item {
			flex: 0 1 calc((100% - 4 * functions.spacing('md')) / 5);
			max-width: calc((100% - 4 * functions.spacing('md')) / 5);
		}
	}
}
