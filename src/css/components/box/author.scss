@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';
@use 'sass:math';

.b-author {
	display: flex;
	flex-direction: column;
	gap: 0.6rem;
	align-items: flex-start;
	height: 100%;
	& > * {
		margin: 0;
	}
	&__img {
		border-radius: 1rem;
		overflow: hidden;
	}
	&__name {
		font-size: variables.$font-size;
	}
	&__link {
		color: variables.$color-text;
		text-decoration: none;
	}
	&__tags {
		display: flex;
		flex-direction: column;
		gap: 0.6rem;
		align-items: flex-start;
	}
}
