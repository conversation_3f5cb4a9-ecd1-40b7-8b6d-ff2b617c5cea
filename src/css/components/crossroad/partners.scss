@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.c-partners {
	&__title {
		margin: 0 0 2.2rem;
	}
	&__annot {
		margin: 0 0 4.2rem;
	}
	&__box {
		display: flex;
		flex-direction: column;
		gap: 0.6rem;
		align-items: flex-start;
		height: 100%;
		& > * {
			margin: 0;
		}
	}
	&__img {
		border-radius: 1rem;
		overflow: hidden;
	}
	&__name {
		font-size: 1.6rem;
	}
	.btn {
		--btn-fs: 1.4rem;
		--btn-h: 3rem;
		--btn-p: 0.4rem 2.2rem;
		--btn-radius: 0.3rem;
	}
	&__position .btn {
		--btn-bg: #{variables.$color-yellow-200};
	}

	// MQ
	@media (config.$xl-up) {
		.grid__cell {
			width: calc(100% / 5);
		}
	}
}
