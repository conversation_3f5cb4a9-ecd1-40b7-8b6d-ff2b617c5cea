@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.m-footer {
	&__list {
		@extend %reset-ul;
		line-height: 145%;
	}
	&__item {
		@extend %reset-ul-li;
	}
	&__link {
		--color-link: #{variables.$color-text};
		--color-hover: #{variables.$color-text};
		--color-hover-decoration: var(--color-hover);
	}

	// MQ
	@media (config.$md-down) {
		margin-bottom: 2rem;
		line-height: 130%;

		&--social {
			display: none;
		}
	}
	@media (config.$xl-up) {
		&__btn {
			display: none;
		}
		&--social {
			display: none;
		}
	}
}
