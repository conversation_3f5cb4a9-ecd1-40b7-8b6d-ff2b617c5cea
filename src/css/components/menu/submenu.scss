@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.m-submenu {
	$s: &;
	display: none;
	gap: 2.4rem;
	background-color: variables.$color-primary;

	&__list {
		@extend %reset-ul;
		position: relative;
		display: flex;
		flex-wrap: wrap;
		gap: 1.6rem;
		margin: 0;
		font-weight: 500;
		font-size: 1.4rem;
		line-height: 150%;
	}
	&__item {
		@extend %reset-ul-li;
		.btn__icon,
		.icon-svg {
			max-width: 2.2rem;
		}
	}
	&__link {
		display: inline-flex;
		gap: 1rem;
		align-items: center;
		padding: 1rem 2.4rem;
		border: 0.14rem solid transparent;
		border-radius: 10rem;
		background: variables.$color-white;
		color: variables.$color-text;
		text-decoration: none;
		transition: border-color variables.$t;
		.hoverevents &:hover {
			--color-hover: variables.$color-text;
			border-color: variables.$color-black;
		}
	}

	&__title {
		margin-bottom: 1.6rem;
		font-weight: 600;
		line-height: 150%;
	}
	.submenu-open & {
		display: flex;
	}

	// MQ
	@media (config.$ls-down) {
		flex-direction: column;
		margin: 1.6rem calc(var(--row-main-gutter) * -1) 0;
		padding: var(--row-main-gutter);
		padding-left: calc(var(--row-main-gutter) + 2.4rem);
		overflow: hidden;
		transition: all variables.$t;
		&__list {
			display: flex;
			flex-direction: column;
		}
		&__category + &__category {
			margin-top: 2.4rem;
		}
	}
	@media (config.$ls-up) {
		position: absolute;
		top: calc(100% + 0.4rem);
		left: -5000px;
		gap: 4.8rem;
		width: 80rem;
		max-width: calc(100vw - 2 * var(--row-main-gutter));
		padding: 3.2rem;
		border-radius: 8px;
		opacity: 0;
		visibility: hidden;
		transition: opacity variables.$t, visibility variables.$t, left 0s variables.$t, height 0s variables.$t;
		box-shadow: 0 3px 30px variables.$color-gray10;
		&__category {
			flex: 1 0 50%;
			max-width: calc(50% - 4.8rem);
			&:first-child {
				margin-right: 3.8rem;
				#{$s}__list {
					&::after {
						content: '';
						position: absolute;
						top: 0;
						right: -3.8rem;
						width: 0.1rem;
						height: 100%;
						background: #dfaa0d;
					}
				}
			}
		}
		&__title {
			font-size: 2rem;
		}

		// HOVERS
		// .hoverevents .m-main__item:hover & {
		// 	left: -0.1rem;
		// 	height: auto;
		// 	opacity: 1;
		// 	visibility: visible;
		// 	transition-delay: 0s, 0s;
		// }

		// STATES
		.m-main__item.is-open &,
		.m-main__item.submenu-open & {
			// .hoverevents .m-main__item.is-hover &,
			// .no-js.hoverevents .m-main__item:hover & {
			left: -0.1rem;
			opacity: 1;
			visibility: visible;
			transition-delay: 0s;
		}
	}
}
