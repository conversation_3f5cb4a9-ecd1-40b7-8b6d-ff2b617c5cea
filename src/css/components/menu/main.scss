@use 'base/variables';
@use 'base/extends';
@use 'config';

.m-main {
	$s: &;
	&__list {
		@extend %reset-ul;
		position: relative;
		display: flex;
		gap: 0.1rem;
	}
	&__item {
		@extend %reset-ul-li;
		position: relative;
		flex: 1 0 auto;
		&:has(.m-submenu) #{$s}__link {
			padding-right: 2.4rem;
		}
	}
	&__link {
		--color-link: #{variables.$color-text};
		--color-hover: #{variables.$color-text};
		--color-hover-decoration: var(--color-hover);
		display: block;
		padding: 0.6rem 0.8rem;
		border: 0.14rem solid transparent;
		border-radius: 10rem;
		font-weight: 500;
		text-decoration: none;
		cursor: pointer;
		transition: border-color variables.$t;
		.submenu-open & {
			& + #{$s}__btn .icon-svg {
				transform: rotate(-180deg);
			}
		}
	}
	&__btn {
		position: absolute;
		top: 2rem;
		right: 0.6rem;
		display: flex;
		justify-content: center;
		align-items: center;
		width: 1.4rem;
		height: 1.4rem;
		color: variables.$color-text;
		transition: color variables.$t;
		transform: translateY(-50%);
		.icon-svg,
		.icon-svg__svg {
			width: 1.4rem;
			height: 1.4rem;
			transition: transform variables.$t;
		}
	}
	// MQ
	@media (config.$ls-down) {
		&__list {
			flex-direction: column;
			margin-bottom: 2.4rem;
			text-align: left;
		}
		&__link {
			width: 100%;
			&.is-active {
				text-decoration: underline;
			}
		}
	}
	@media (config.$ls-up) {
		margin: 0 auto;
		&__list {
			font-size: 1.6rem;
		}
		&__link {
			padding: 0.5rem 1.2rem;
		}
		&__btn {
			top: 50%;
		}
	}
	@media (config.$xl-up) {
		&__list {
			align-items: center;
			font-size: variables.$font-size;
		}
		&__item {
			flex: 1 0 auto;
			&:has(.m-submenu) #{$s}__link {
				padding-right: 4.4rem;
			}
		}
		&__link {
			padding: 0.7rem 2.2rem;
			&.is-active,
			.hoverevents &:hover {
				border-color: variables.$color-black;
			}
		}
		&__btn {
			right: 2.2rem;
		}
	}
}
