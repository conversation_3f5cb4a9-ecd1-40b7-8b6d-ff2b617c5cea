@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.m-lang {
	$s: &;
	position: relative;
	height: 100%;
	&__btn {
		@include mixins.button-reset;
	}
	&__img {
		object-fit: contain;
	}
	&__arrow {
		width: 1.2rem;
		transition: transform variables.$t;
	}
	&__list {
		@extend %reset-ul;
		display: flex;
		gap: 1rem;
		align-items: center;
	}
	&__item {
		@extend %reset-ul-li;
	}
	&__link {
		display: flex;
		gap: 1rem;
		align-items: center;
		padding: 0.5rem 0;
		color: inherit;
		text-decoration: none;
	}

	// STATES
	&__item.is-active &__link {
		font-weight: bold;
	}

	// HOVERS
	.no-hoverevents &.is-open,
	.hoverevents &:hover {
		#{$s}__arrow {
			transform: rotate(-180deg);
		}
		#{$s}__list {
			opacity: 1;
			visibility: visible;
		}
	}
	// MQ
	@media (config.$ls-down) {
		&__list {
			flex-direction: row-reverse;
			justify-content: flex-end;
		}
	}
	@media (config.$xl-up) {
		&__list {
			gap: 2rem;
		}
	}
	@media (config.$ls-up) and (config.$xl-down) {
		&__btn {
			.btn__text {
				min-height: auto;
				padding: 0.7rem 1.2rem;
			}
		}
	}
}
