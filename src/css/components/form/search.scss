@use 'base/variables';
@use 'config';

.f-search {
	// position: relative;
	margin: 0 0 0 auto;
	.inp-icon__icon {
		.icon-svg {
			transition: opacity variables.$t;
		}
	}
	&__wrap {
		position: absolute;
		top: calc(100% + 1rem);
		right: 0;
		left: 0;
		display: none;
		max-width: variables.$row-main-width-content;
		height: 4.4rem;
		margin: 0 auto;
		border-radius: variables.$border-radius;
		background-color: variables.$color-primary;
		transition: height variables.$t;
		box-shadow: 0 0 0.4rem 0 rgba(0, 0, 0, 0.25);
		.inp-fix {
			flex: 1;
		}
	}
	&__loader {
		content: '';
		position: absolute;
		top: 50%;
		left: 50%;
		width: 1.6rem;
		height: 1.6rem;
		margin: -0.8rem 0 0 -0.8rem;
		border: 0.2rem solid;
		border-top-color: transparent;
		border-radius: 2rem;
		opacity: 0;
		transition: opacity variables.$t;
	}

	&__inp {
		height: 100%;
		border: none;
	}

	// STATEs
	&--active &__wrap {
		display: flex;
	}
	&--active &__inp {
		border-color: variables.$color-primary;
	}
	&__inp.is-loading ~ .inp-icon__icon {
		.icon-svg {
			opacity: 0;
		}
		.f-search__loader {
			opacity: 1;
			animation: animation-rotate 0.8s infinite linear;
		}
	}
	@media (config.$xl-up) {
		order: 1;
	}
}
