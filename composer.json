{"name": "superkoders/sedlakova-legal", "type": "project", "require": {"php": "^8.3", "ext-fileinfo": "*", "ext-gd": "*", "ext-json": "*", "ext-zip": "*", "azuyalabs/yasumi": "^2.3", "brick/money": "^0.8.0", "composer/ca-bundle": "^1.3", "contributte/apitte": "^0.11.1", "contributte/application": "^0.5.1", "contributte/console": "^0.10.1", "contributte/console-extra": "^0.8.0", "contributte/elastica": "^1.1", "contributte/event-dispatcher": "^0.9.1", "contributte/event-dispatcher-extra": "^0.10.0", "contributte/logging": "^0.6.1", "contributte/messenger": "^0.1.0", "contributte/monolog": "^0.5.0", "contributte/redis": "^0.5.3", "contributte/validator": "^1.0", "cweagans/composer-patches": "^1.7", "firebase/php-jwt": "^6.1", "gopay/payments-sdk-php": "^1.4", "guzzlehttp/psr7": "^1.8.3", "heureka/overeno-zakazniky": "^4.0", "jaybizzle/crawler-detect": "^1.2", "latte/latte": "^3.0", "league/csv": "^9.6", "league/oauth2-google": "^4.0", "marc-mabe/php-enum": "^4.3", "mpdf/mpdf": "^8.0", "nette/nette": "~3.1", "nettrine/annotations": "^0.7.0", "nettrine/cache": "^0.3.0", "nextras/migrations": "^3.1", "nextras/orm": "^4.0", "ontob/qrpayment": "^2.0", "pelago/emogrifier": "^7.0", "php-curl-class/php-curl-class": "^9.0", "sentry/sdk": "^3.1", "symfony/lock": "^6.0", "symfony/property-access": "^6.2", "symfony/redis-messenger": "^5.4", "symfony/validator": "^6.0", "texy/texy": "^3.1", "tracy/tracy": "^2.8", "ublaboo/datagrid": "^6.9.5"}, "require-dev": {"deployer/deployer": "^7.5", "mockery/mockery": "^1.4", "nette/tester": "^2.4", "nextras/orm-phpstan": "^1.0", "ninjify/coding-standard": "^0.12", "php-parallel-lint/php-console-highlighter": "^1.0", "php-parallel-lint/php-parallel-lint": "^1.3", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^1.10", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-nette": "^1.0", "roave/security-advisories": "dev-latest"}, "autoload": {"psr-4": {"App\\": ["app/"]}, "files": ["app/bd.php"]}, "autoload-dev": {"psr-4": {"App\\Tests\\": ["tests/"]}}, "config": {"platform": {"php": "8.3.7"}, "sort-packages": true, "allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true, "phpstan/extension-installer": true, "cweagans/composer-patches": true, "php-http/discovery": true}}, "scripts": {"tests": "tester -C -s tests/", "phpstan": "phpstan analyse -c .phpstan.neon --memory-limit=2G", "lint": "parallel-lint --blame app tests --exclude tests/var", "latte-lint": "latte-lint app", "cs": "phpcs --standard=.ruleset.xml", "cs-fix": "phpcbf --standard=.ruleset.xml"}, "extra": {"patches": {"nextras/dbal": {"Do not cast decimal to float (https://github.com/nextras/dbal/issues/52)": "patches/nextras-dbal-decimal-string.patch"}, "contributte/elastica": {"Missing init for $totalTime (https://github.com/contributte/elastica/pull/10)": "patches/contributte-elastica-total-time.patch"}}}}