

CREATE TABLE IF NOT EXISTS `material` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `internalName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `customFields<PERSON>son` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin;


CREATE TABLE IF NOT EXISTS `material_localization` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mutationId` int(11) NOT NULL,
  `materialId` int(11) NOT NULL,
  `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `nameAnchor` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `nameTitle` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `keywords` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `isTop` int(11) NOT NULL DEFAULT 0,
  `public` int(11) NOT NULL DEFAULT 0,
  `forceNoIndex` int(11) NOT NULL DEFAULT 0,
  `hideInSearch` int(11) NOT NULL DEFAULT 0,
  `hideInSitemap` int(11) NOT NULL DEFAULT 0,
  `publicFrom` datetime DEFAULT NULL,
  `publicTo` datetime DEFAULT NULL,
  `edited` int(11) DEFAULT NULL,
  `editedTime` datetime DEFAULT NULL,
  `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `customContentSchemeJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `customContentJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `viewsNumber` int(10) unsigned NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `FK_blog_mutation` (`mutationId`) USING BTREE,
  KEY `FK_blog_localization_blog` (`materialId`) USING BTREE,
  CONSTRAINT `material_localization_ibfk_1` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE,
  CONSTRAINT `material_localization_ibfk_2` FOREIGN KEY (`materialId`) REFERENCES `material` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin;


CREATE TABLE IF NOT EXISTS `material_x_blog_tag` (
  `materialId` int(11) NOT NULL,
  `blogTagId` int(11) NOT NULL,
  PRIMARY KEY (`materialId`,`blogTagId`) USING BTREE,
  KEY `FK__blog` (`materialId`) USING BTREE,
  KEY `FK__blog_tag` (`blogTagId`) USING BTREE,
  CONSTRAINT `material_x_blog_tag_ibfk_1` FOREIGN KEY (`materialId`) REFERENCES `material` (`id`) ON DELETE CASCADE,
  CONSTRAINT `material_x_blog_tag_ibfk_2` FOREIGN KEY (`blogTagId`) REFERENCES `blog_tag` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin;


CREATE TABLE IF NOT EXISTS `material_x_material` (
  `materialId` int(11) NOT NULL,
  `attachedMaterialId` int(11) NOT NULL,
  PRIMARY KEY (`materialId`,`attachedMaterialId`) USING BTREE,
  KEY `FK_blog_x_blog_blog` (`materialId`) USING BTREE,
  KEY `FK_blog_x_blog_blog_2` (`attachedMaterialId`) USING BTREE,
  CONSTRAINT `material_x_material_ibfk_1` FOREIGN KEY (`materialId`) REFERENCES `material` (`id`) ON DELETE CASCADE,
  CONSTRAINT `material_x_material_ibfk_2` FOREIGN KEY (`attachedMaterialId`) REFERENCES `material` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin;

