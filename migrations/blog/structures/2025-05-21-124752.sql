ALTER TABLE `blog_tag`
ADD `type` varchar(255) COLLATE 'utf8mb4_unicode_520_ci' NOT NULL AFTER `internalName`;


CREATE TABLE IF NOT EXISTS `author_x_blog_tag` (
  `authorId` int(11) NOT NULL,
  `blogTagId` int(11) NOT NULL,
  PRIMARY KEY (`authorId`,`blogTagId`) USING BTREE,
  KEY `FK__blog` (`authorId`) USING BTREE,
  KEY `FK__blog_tag` (`blogTagId`) USING BTREE,
  CONSTRAINT `author_x_blog_tag_ibfk_1` FOREIGN KEY (`authorId`) REFERENCES `author` (`id`) ON DELETE CASCADE,
  CONSTRAINT `author_x_blog_tag_ibfk_2` FOREIGN KEY (`blogTagId`) REFERENCES `blog_tag` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin;


CREATE TABLE IF NOT EXISTS `reference_x_blog_tag` (
  `referenceId` int(11) NOT NULL,
  `blogTagId` int(11) NOT NULL,
  PRIMARY KEY (`referenceId`,`blogTagId`) USING BTREE,
  KEY `FK__blog` (`referenceId`) USING BTREE,
  KEY `FK__blog_tag` (`blogTagId`) USING BTREE,
  CONSTRAINT `reference_x_blog_tag_ibfk_1` FOREIGN KEY (`referenceId`) REFERENCES `reference` (`id`) ON DELETE CASCADE,
  CONSTRAINT `reference_x_blog_tag_ibfk_2` FOREIGN KEY (`blogTagId`) REFERENCES `blog_tag` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin;
