CREATE TABLE IF NOT EXISTS `blog_tag_localization_tree` (
  `Id` int(11) NOT NULL AUTO_INCREMENT,
  `blogTagLocalizationId` int(11) NOT NULL,
  `treeId` int(11) NOT NULL,
  `sort` int(11) NOT NULL DEFAULT '1',
  <PERSON>IMAR<PERSON> KEY (`Id`),
  <PERSON><PERSON><PERSON> `FK_blog_localization_tree_blog_localization` (`blogTagLocalizationId`),
  <PERSON><PERSON>Y `FK_blog_localization_tree_tree` (`treeId`),
  CONSTRAINT `blog_tag_localization_tree_ibfk_1` FOREIGN KEY (`blogTagLocalizationId`) REFERENCES `blog_tag_localization` (`id`) ON DELETE CASCADE,
  CONSTRAINT `blog_tag_localization_tree_ibfk_2` FOREIGN KEY (`treeId`) REFERENCES `tree` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
