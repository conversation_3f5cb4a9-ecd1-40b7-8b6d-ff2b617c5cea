
CREATE TABLE IF NOT EXISTS `reference` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `internalName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `customFields<PERSON>son` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin;


CREATE TABLE IF NOT EXISTS `reference_localization` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mutationId` int(11) NOT NULL,
  `referenceId` int(11) NOT NULL,
  `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `nameAnchor` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `nameTitle` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `keywords` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `isTop` int(11) NOT NULL DEFAULT 0,
  `public` int(11) NOT NULL DEFAULT 0,
  `forceNoIndex` int(11) NOT NULL DEFAULT 0,
  `hideInSearch` int(11) NOT NULL DEFAULT 0,
  `hideInSitemap` int(11) NOT NULL DEFAULT 0,
  `publicFrom` datetime DEFAULT NULL,
  `publicTo` datetime DEFAULT NULL,
  `edited` int(11) DEFAULT NULL,
  `editedTime` datetime DEFAULT NULL,
  `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `customContentSchemeJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `customContentJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `viewsNumber` int(10) unsigned NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `FK_blog_mutation` (`mutationId`) USING BTREE,
  KEY `FK_blog_localization_blog` (`referenceId`) USING BTREE,
  CONSTRAINT `reference_localization_ibfk_2` FOREIGN KEY (`referenceId`) REFERENCES `reference` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `reference_localization_ibfk_3` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin;


CREATE TABLE IF NOT EXISTS `reference_tag` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `internalName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin;


CREATE TABLE IF NOT EXISTS `reference_tag_localization` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `referenceTagId` int(11) NOT NULL,
  `mutationId` int(11) NOT NULL,
  `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `sort` int(11) NOT NULL DEFAULT 0,
  `nameAnchor` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `nameTitle` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `keywords` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `public` int(11) NOT NULL DEFAULT 0,
  `forceNoIndex` int(11) NOT NULL DEFAULT 0,
  `hideInSearch` int(11) NOT NULL DEFAULT 0,
  `hideInSitemap` int(11) NOT NULL DEFAULT 0,
  `edited` int(11) DEFAULT NULL,
  `editedTime` datetime DEFAULT NULL,
  `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `customContentJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `FK_blog_tag_mutation` (`mutationId`) USING BTREE,
  KEY `FK_blog_tag_localization_blog_tag` (`referenceTagId`),
  CONSTRAINT `reference_tag_localization_ibfk_2` FOREIGN KEY (`referenceTagId`) REFERENCES `reference_tag` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `reference_tag_localization_ibfk_3` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin;


CREATE TABLE IF NOT EXISTS `reference_x_reference` (
  `referenceId` int(11) NOT NULL,
  `attachedReferenceId` int(11) NOT NULL,
  PRIMARY KEY (`referenceId`,`attachedReferenceId`) USING BTREE,
  KEY `FK_blog_x_blog_blog` (`referenceId`) USING BTREE,
  KEY `FK_blog_x_blog_blog_2` (`attachedReferenceId`) USING BTREE,
  CONSTRAINT `reference_x_reference_ibfk_3` FOREIGN KEY (`referenceId`) REFERENCES `reference` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `reference_x_reference_ibfk_4` FOREIGN KEY (`attachedReferenceId`) REFERENCES `reference` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin;


CREATE TABLE IF NOT EXISTS `reference_x_reference_tag` (
  `referenceId` int(11) NOT NULL,
  `referenceTagId` int(11) NOT NULL,
  PRIMARY KEY (`referenceId`,`referenceTagId`) USING BTREE,
  KEY `FK__blog` (`referenceId`) USING BTREE,
  KEY `FK__blog_tag` (`referenceTagId`) USING BTREE,
  CONSTRAINT `reference_x_reference_tag_ibfk_1` FOREIGN KEY (`referenceId`) REFERENCES `reference` (`id`) ON DELETE CASCADE,
  CONSTRAINT `reference_x_reference_tag_ibfk_2` FOREIGN KEY (`referenceTagId`) REFERENCES `reference_tag` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin;

